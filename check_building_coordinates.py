import json

# Path to the GeoJSON file
file_path = 'remote/public/data/osm/saoPao/logistics_buildings.geojson'

try:
    with open(file_path, 'r') as f:
        data = json.load(f)
        if data['type'] == 'FeatureCollection' and data['features']:
            first_feature = data['features'][0]
            if 'geometry' in first_feature and 'coordinates' in first_feature['geometry']:
                coords = first_feature['geometry']['coordinates']
                print('First feature coordinates:', coords)
            else:
                print('No coordinates found in the first feature.')
        else:
            print('No features found in the GeoJSON file.')
except Exception as e:
    print('Error reading file:', e) 