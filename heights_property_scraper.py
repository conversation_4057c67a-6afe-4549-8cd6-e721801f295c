import os
import json
from datetime import datetime
import requests
from dotenv import load_dotenv

# Load environment variables from the remote directory
env_path = os.path.join('remote', '.env')
load_dotenv(env_path)

FIRECRAWL_API_KEY = os.getenv('firecrawl')  # Using the key name from your .env
FIRECRAWL_BASE_URL = 'https://api.firecrawl.dev/v1'  # Updated with API version

print(f"Environment variables loaded from {env_path}")
print(f"API Key present: {'Yes' if FIRECRAWL_API_KEY else 'No'}")
print(f"API Key: {FIRECRAWL_API_KEY}")

def test_api_key():
    """
    Test if the Firecrawl API key is valid by trying a simple extraction
    """
    try:
        # Test with a simple extraction request instead of status
        response = requests.post(
            f'{FIRECRAWL_BASE_URL}/extract',
            json={
                'url': 'https://www.har.com',
                'prompt': 'Extract the page title'
            },
            headers={
                'Authorization': f'Bearer {FIRECRAWL_API_KEY}',
                'Content-Type': 'application/json'
            }
        )
        print(f"API Test Status Code: {response.status_code}")
        if response.status_code != 200:
            print(f"API Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing API key: {str(e)}")
        return False

def extract_single_property(url):
    """
    Extract data for a single property using Firecrawl's extract API
    """
    property_prompt = """
    Extract the following information for this property:
    - Address
    - Price
    - Bedrooms
    - Bathrooms
    - Square footage
    - Year built
    - Property type
    Return as a single JSON object.
    """
    
    try:
        print(f"Attempting to extract data from: {url}")
        response = requests.post(
            f'{FIRECRAWL_BASE_URL}/extract',
            json={
                'url': url,
                'prompt': property_prompt
            },
            headers={
                'Authorization': f'Bearer {FIRECRAWL_API_KEY}',
                'Content-Type': 'application/json'
            }
        )
        print(f"Extract API Status Code: {response.status_code}")
        
        if response.status_code != 200:
            print(f"Error Response: {response.text}")
            return None
            
        return response.json()
    except Exception as e:
        print(f"Error extracting data: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Error response: {e.response.text}")
        return None

def save_to_json(data, filename):
    """
    Save extracted data to a JSON file with timestamp
    """
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'data/{filename}_{timestamp}.json'
    os.makedirs('data', exist_ok=True)
    
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)
    print(f"Data saved to {filename}")

def main():
    # First test the API key
    print("\nTesting Firecrawl API key...")
    
    if test_api_key():
        print("✓ API key is valid!")
    else:
        print("✗ API key test failed!")
        return

    # Test with single property
    # Using the search URL first to find the property
    search_url = "https://www.har.com/727-west-temple-st-houston-tx-77009/home"
    print("\nTesting single property extraction...")
    
    property_data = extract_single_property(search_url)
    
    if property_data:
        print("\nProperty data extracted successfully!")
        print(json.dumps(property_data, indent=2))
        save_to_json(property_data, 'single_property_test')
    else:
        print("\nFailed to extract property data")

if __name__ == "__main__":
    main() 