import json
import os

def analyze_geojson(file_path):
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        print("\n=== GeoJSON Analysis ===")
        print(f"Type: {data.get('type')}")
        print(f"Number of features: {len(data.get('features', []))}")
        
        if data.get('features'):
            # Analyze first feature
            first_feature = data['features'][0]
            print("\nFirst Feature Analysis:")
            print(f"Feature Type: {first_feature.get('type')}")
            print(f"Geometry Type: {first_feature.get('geometry', {}).get('type')}")
            print("\nProperties:")
            for key, value in first_feature.get('properties', {}).items():
                print(f"  {key}: {value}")
            
            # Check for ID field
            print("\nID Analysis:")
            if 'id' in first_feature:
                print(f"Feature has direct ID: {first_feature['id']}")
            if 'ds_codigo' in first_feature.get('properties', {}):
                print(f"Feature has ds_codigo: {first_feature['properties']['ds_codigo']}")
            
            # Count unique IDs
            ids = set()
            for feature in data['features']:
                if 'id' in feature:
                    ids.add(feature['id'])
                if 'ds_codigo' in feature.get('properties', {}):
                    ids.add(feature['properties']['ds_codigo'])
            print(f"\nNumber of unique IDs: {len(ids)}")
            
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
    except json.JSONDecodeError:
        print("Error: Invalid JSON file")
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    file_path = "remote/public/data/osm/saoPao/distrito_wgs84.geojson"
    analyze_geojson(file_path) 