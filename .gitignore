# Environment Variables & Secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.key
*secret*
*token*
*api_key*

# Virtual Environment
venv/
env/
ENV/
.venv
venv_new/
venv_fresh/
.conda/
.pyenv/

# Python
**/__pycache__/
**/*.py[cod]
**/*.pyc
**/*pytest*.pyc
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
.coverage.*
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Node/React
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache
.next/
out/
.nuxt
dist/

# Build outputs
/build
/remote/build
**/build/
**/dist/

# macOS system files
.DS_Store
.AppleDouble
.LSOverride
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# IDE and Editors
.idea/
.vscode/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Data files (large/sensitive)
*.geojson
*.csv
*.json.gz
*.parquet
*.h5
*.hdf5
data/debug_*
data/har_*
*.har

# Project Specific
.coverage
PLAN.txt
prism.txt
atlas.egg-info/
*.pt
benchmark_output.json
benchmark_results/
reit_*.log
coherence_*.log
*_cleaner.log

# Local development files
.python-version
.hintrc
.env.example

# Temporary/backup files
**/*copy*.*
**/OLD/
**/*backup*
**/*temp*
**/tmp/
**/.tmp/

# OS generated files
.directory
.fuse_hidden*
.nfs*
