{"name": "har-scraper", "tools": [{"name": "search_properties", "description": "Search for properties on HAR.com", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "The search query (address, area, etc.)"}, "max_results": {"type": "integer", "description": "Maximum number of results to return", "default": 10}}, "required": ["query"]}}, {"name": "search_property_history", "description": "Search for a property's history in Matrix MLS", "parameters": {"type": "object", "properties": {"address": {"type": "string", "description": "The full property address"}}, "required": ["address"]}}], "command": "python har_scraper.py"}