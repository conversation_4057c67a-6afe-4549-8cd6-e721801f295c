<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE" />
<meta name="csrf-token" content="ZXXNcRKfNJkknqZKwmf8R45NHe0quWE5thuyTqFu">
<meta property="fb:app_id" content="196367140403306" />
<title>4017 Robertson St, Houston, TX 77009 - HAR.com</title>
<link rel="canonical" href="https://www.har.com/homedetail/4017-robertson-st-houston-tx-77009/3011563">
<link rel="alternate" href="android-app://com.har.androidapp/har/listing/active/74257223">
<meta property="og:type" content="website">
<meta property="og:title" content="4017 Robertson St, Houston, TX 77009 - HAR.com">
<meta property="og:description" content="For Sale: 4017 Robertson St, Houston, TX 77009 ∙ $998,500 ∙ 0.62 Acres Lot ∙ 940 Sqft, Single-Family ∙ View more.">
<meta property="og:locale" content="en_US">
<meta property="og:url" content="https://www.har.com/homedetail/4017-robertson-st-houston-tx-77009/3011563">
<meta property="og:image" content="https://www.har.com/ogimage/v3/listing-detail--9664623--53561822f1503193b57495987d32b9b7.jpg?t=1744381535">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="627">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:site" content="Homes And Rentals - HAR.com">
<meta name="twitter:title" content="4017 Robertson St, Houston, TX 77009 - HAR.com">
<meta name="twitter:description" content="For Sale: 4017 Robertson St, Houston, TX 77009 ∙ $998,500 ∙ 0.62 Acres Lot ∙ 940 Sqft, Single-Family ∙ View more.">
<meta name="twitter:image:alt" content="4017 Robertson St, Houston, TX 77009 - HAR.com">
<meta name="twitter:creator" content="@harmembers">
<meta name="twitter:data1" content="Patrick Burbridge">
<meta name="twitter:label1" content="Listed By">
<meta name="twitter:image" content="https://www.har.com/ogimage/v3/listing-detail--9664623--53561822f1503193b57495987d32b9b7.jpg?t=1744381535">
<meta name="description" content="For Sale: 4017 Robertson St, Houston, TX 77009 ∙ $998,500 ∙ 0.62 Acres Lot ∙ 940 Sqft, Single-Family ∙ View more.">
<meta name="keywords" content="4017 Robertson St,Houston, TX 77009,Houston,homes for sale,home values,resale homes,school,neighborhood"><link rel="shortcut icon" sizes="32x32" href="https://www.har.com/images/favicon-32x32.png">
<link rel="shortcut icon" sizes="48x48" href="https://www.har.com/images/favicon-48x48.png">
<link rel="shortcut icon" sizes="64x64" href="https://www.har.com/images/favicon-icon-64x64.png">
<link rel="shortcut icon" sizes="16x16" href="https://www.har.com/favicon.ico">
<link rel="apple-touch-icon" sizes="20x20" href="https://www.har.com/images/apple-icon-20x20.png">
<link rel="apple-touch-icon" sizes="57x57" href="https://www.har.com/images/apple-icon-57x57.png">
<link rel="apple-touch-icon-precomposed" sizes="57x57" href="https://www.har.com/images/apple-icon-precomposed.png">
<link rel="apple-touch-icon" sizes="72x72" 	 href="https://www.har.com/images/apple-icon-72x72.png">
<link rel="apple-touch-icon" sizes="96x96" 	 href="https://www.har.com/images/apple-icon-96x96.png">
<link rel="apple-touch-icon" sizes="114x114" href="https://www.har.com/images/apple-icon-114x114.png">
<link rel="apple-touch-icon" sizes="120x120" href="https://www.har.com/images/apple-icon-120x120.png">
<link rel="apple-touch-icon" sizes="144x144" href="https://www.har.com/images/apple-icon-144x144.png">
<link rel="apple-touch-icon" sizes="152x152" href="https://www.har.com/images/apple-icon-152x152.png">
<meta name="msapplication-TileColor" content="#0b50d2">
<meta name="msapplication-TileImage" content="https://www.har.com/images/ms-icon-144x144.png">

<link rel="preload" as="image" href="https://content.harstatic.com/media/icons/heart_light_blue.svg" fetchpriority="high">
<link rel="preload" as="image" href="https://content.harstatic.com/media/icons/icon-breadcrumb-as.svg" fetchpriority="high">
<link rel="preload" as="image" href="https://content.harstatic.com/resource_2019/imgs/icons/select_arrow.svg" fetchpriority="high">
<link rel="preload" as="image" href="https://content.harstatic.com/media/icons/form/checkbox-empty.svg" fetchpriority="low">
<link rel="preload" as="image" href="https://www.har.com/images/loading_pulse.svg" fetchpriority="low">
<link rel="preload" as="image" href="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" fetchpriority="low">
<link rel="preload" as="image" href="https://content.harstatic.com/resources/images/common/logo-blue.svg" fetchpriority="high">
<link rel="preload" as="image" href="https://content.harstatic.com/media/icons/menu_icon_humberger2.svg" fetchpriority="high">
<link rel="preload" as="image" href="https://content.harstatic.com/media/icons/share_arrow_black.svg" fetchpriority="high">
<link rel="preload" as="image" href="https://content.harstatic.com/img/common/loading1.gif" fetchpriority="low">



        <link rel="preload" href="https://content.harstatic.com/media/fonts/Inter-Regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://content.harstatic.com/media/fonts/Inter-SemiBold.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://content.harstatic.com/media/fonts/Inter-Bold.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://content.harstatic.com/media/fonts/Inter-ExtraBold.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="/js/jquery-2.2.4.min.js" as="script" />
    
        <link href="/css/bootstrap.min.css" rel="stylesheet">
    
    <link href="/css/base.css?v=*********" rel="stylesheet">
    <link href="/css/layout.css?v=*********" rel="stylesheet">
    <link href="abc" />
    	    
            <style>
	.controls__total-time {
		display: none;
	}
</style>
<link href="/plugins/slider/slick.css?v=*********" rel="stylesheet">
<link href="/css/views/listing_details.css?v=*********" rel="stylesheet">
<link href="/plugins/audioplayer/green-audio-player_customized.css" rel="stylesheet">
<link href="/plugins/swiper/css/swiper-bundle.min.css" rel="stylesheet" />
    <script type="application/ld+json">{"@context":"https:\/\/schema.org","@graph":[]}</script>
<script type="application/ld+json">{"@context": "https://schema.org", "@type": "BreadcrumbList","itemListElement": [{"@type":"ListItem","position":1,"name":"Texas","item":"https:\/\/www.har.com\/texasrealestate\/?tab=region"},{"@type":"ListItem","position":2,"name":"Harris","item":"https:\/\/www.har.com\/search\/dosearch?fips_code=48201"},{"@type":"ListItem","position":3,"name":"Houston","item":"https:\/\/www.har.com\/houston\/realestate\/for_sale"},{"@type":"ListItem","position":4,"name":"77009","item":"https:\/\/www.har.com\/zipcode_77009\/realestate\/for_sale"},{"@type":"ListItem","position":5,"name":"Northside Village","item":"https:\/\/www.har.com\/search\/dosearch?ptids=7997"}]}</script>
<link rel="preload" as="image" href="https://photos.harstatic.com/411360813/hr/img-1.jpeg?ts=2025-04-07T13:11:39.830">
  <link rel="preload" as="image" href="https://photos.harstatic.com/411360813/hr/img-2.jpeg?ts=2025-04-07T13:38:38.283">
  <link rel="preload" as="image" href="https://photos.harstatic.com/411360813/hr/img-5.jpeg?ts=2025-04-07T13:38:38.513">
<link data="test" href="/css/plugins/jqueryui/thin/jquery-ui.min.css?v=*********" rel="stylesheet">
<style>.lightbox {
    position: fixed;
    /*z-index: 10000; Replaced by Ivens */
    z-index:10000001;
    width: 100%;
    height: 100%;
    text-align: center;
    top: 0;
    left: 0;
    background: rgba(0,0,0,.8)
}
#loadertext {
    color: #fff!important;
    font-size: 13pt
}</style>

    
    
        
        <script>!function(){window.semaphore=window.semaphore||[],window.ketch=function(){window.semaphore.push(arguments)};var e=document.createElement("script");e.type="text/javascript",e.src="https://global.ketchcdn.com/web/v3/config/har/website_smart_tag/boot.js",e.defer=e.async=!0,document.getElementsByTagName("head")[0].appendChild(e)}();</script>
    </head>

<body id="" class="">
	
    <nav class="navbar navbar-expand-md har-nav">
    <div class="container container--v2">
        <!--<a id="HamIcon" href="javascript:void(0);"><img style="width:24px;" src="https://content.harstatic.com/resources/images/icons/svg/hamburg_icon.svg"></a>-->
        <a class="navbar-brand" href="/"><img fetchPriority="high" alt="HAR" src="https://content.harstatic.com/resources/images/common/logo-blue.svg"></a>
        <a class="resp_nav_close" href="#"><img loading="lazy" style="width:14px;" fetchPriority="high" alt="HAR" src="https://content.harstatic.com/media/icons/close.svg"></a>

        <div class="collapse navbar-collapse" id="MainNavBar">

            <a class="resp_nav_logo" href="/"><img fetchPriority="high" style="width:56px;" alt="HAR" src="https://content.harstatic.com/resources/images/common/logo-blue.svg"></a>
           
            <ul class="navbar-nav mr-auto me-auto">      
                                
                <li class="nav-item dropdown ">                    
                    <a class="nav-link dropdown-toggle" href="#"  role="button" data-toggle="dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Buy/Rent
                    <img loading="lazy" fetchPriority="low" alt="dropdown image" class="linkarrow" src="https://content.harstatic.com/resources/images/icons/red_down_arrow.svg">
                    </a>
                    <div class="dropdown-menu dropdown-buyrent" aria-labelledby="navbarDropdown">
                        <ul class="dropdown-content font_size--medium font_weight--semi_bold">
                            <li>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Buy</li>
                                    <li><a href="/search/single_family_homes">Single Family Homes</a></li>
                                    <li><a href="/search/townhouse_condo">Townhouses/Condos</a></li>
                                    <li><a href="/search/multi_family">Multi-Family Homes</a></li>
                                    <li><a href="/search/residential_lots_and_land">Land and Lots</a></li>
                                    <li><a href="/search/country_homes_acreage">Country Homes/Acreage</a></li>
                                    <li><a href="/search/mid_high_rise_condominium">Mid/High-Rise Condos </a></li>
									<li><a href="/mapsearch">Search by Map</a></li>

                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Rent</li>
                                    <li><a href="/rentals">Rental Search</a></li>
                                    <li><a href="/search/single_family_homes?for_sale=0">Single Family Homes</a></li>
                                    <li><a href="/search/townhouse_condo?for_sale=0">Townhouses/Condos</a></li>
                                    <li><a href="/search/multi_family?for_sale=0">Multi-Family Homes</a></li>
                                    <li><a href="/search/mid_high_rise_condominium?for_sale=0">Mid/High-Rise Condos </a></li>
                                    <!--
                                    <li><a href="/search/residential_lots_and_land?for_sale=0">Residential Lots &amp; Land</a></li>

                                    <li><a href="/search/country_homes_acreage?for_sale=0">Country Homes/Acreage</a></li>
                                    -->

                                    <li><a href="/apartments">Apartments</a></li>
                                    <li><a href="/mobile/#rental">Rentals App</a></li>
									<li><a href="/mortgage/time-value-calculators?CALCULATORID=HF05">Buying vs Renting Calculator</a></li>
									<li><a href="/mapsearch?for_sale=0">Search by Map</a></li>
                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Specialties</li>
										<li><a href="/drivetimesearch/">Drive Time <img width="15px" src="https://content.harstatic.com/media/icons/DriveTime.svg" alt="car icon"></a></li>
                                        <li><a href="/new-homes-by-city">Just Listed</a></li>
                                        <li><a href="/openhouse">Open Houses</a></li>
                                        <li><a href="/new-construction-by-city">New Homes</a></li>
                                        <li><a href="/luxury-homes-for-sale-by-city">Luxury Homes</a></li>
                                        <li><a href="/lots-and-land-for-sale-by-city">Land and Lots</a></li>
                                        <li><a href="/price-reduced-homes-by-city">Price Reduction</a></li>
                                        <li><a href="/foreclosed-homes-by-city">Foreclosure</a></li>                                     
                                        <li><a href="https://www.commgate.com/" target="_blank">Commercial Properties</a></li>
                                        <li><a href="/international">Global Properties (International)</a></li>


                                </ul>
                                
								<div class="graphics_section font_weight--regular">
																	<div class="border_radius--round bg_color_har_blue color_snow_white p-4 mb-2">
										<h3 tabindex="0" class="color_snow_white">Enjoy all Benefits of HAR.com</h3>
										<div class="pb-3">Save searches and favorites, ask questions, and connect with agents through seamless mobile and web experience, by creating an HAR account.</div>
										<a href="/login/createaccount?" class="btn btn--prominent">Sign up / Create an account</a>
										<a href="/login" class="btn btn--shapeless"><span class="color_snow_white">Sign In</span></a>
									</div>
								
									<div class="p-4">
										<h3 tabindex="0" class="mb-2">Get the Top Real Estate App</h3>
										<div class="pb-3 d-flex">
											<div class="mr-3 me-3">
											   <img loading="lazy" fetchPriority="low" alt="Full Star" class="pb-1" src="https://content.harstatic.com/media/icons/stars/star_full.svg">
                                               <img loading="lazy" fetchPriority="low" alt="Full Star" class="pb-1" src="https://content.harstatic.com/media/icons/stars/star_full.svg">
                                               <img loading="lazy" fetchPriority="low" alt="Full Star" class="pb-1" src="https://content.harstatic.com/media/icons/stars/star_full.svg">
                                               <img loading="lazy" fetchPriority="low"  alt="Full Star" class="pb-1" src="https://content.harstatic.com/media/icons/stars/star_full.svg">
                                               <img loading="lazy" fetchPriority="low"  alt="Half and quarter star" class="pb-1" src="https://content.harstatic.com/media/icons/stars/star_half_and_quarter.svg">
											</div>
											<div class="color_cement_dark">4.8 • 130K Ratings</div>
										</div>
										<div class="pb-2">
											<a rel="nofollow" class="d-inline-block pr-3 pe-3 pb-3" href="https://itunes.apple.com/us/app/har-com-houston-real-estate/id386981161"><img loading="lazy" fetchPriority="low" style="max-width:123px;" src="https://content.harstatic.com/media/icons/download-on-the-app-store-black.svg" alt="Download on the App Store"></a>
											<a rel="nofollow" class="d-inline-block pb-3" href="https://play.google.com/store/apps/details?id=com.har.androidapp&amp;hl=en"><img loading="lazy" fetchPriority="low" style="max-width:135px;" src="https://content.harstatic.com/media/icons/google-play-badge_black.svg" alt="Get it on Google Play"></a>
										</div>
										<a href="/mobile/" class="font_weight--semi_bold font_size--medium">View other HAR apps -></a>
									</div>
								</div>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item dropdown cols_3 ">
                    <a class="nav-link dropdown-toggle" href="#"  role="button" data-toggle="dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Home Values
                    <img loading="lazy" fetchPriority="low" alt="dropdown image" class="linkarrow" src="https://content.harstatic.com/resources/images/icons/red_down_arrow.svg">
                    </a>
                    <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                        <ul class="dropdown-content  font_size--medium font_weight--semi_bold">
                            <li>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Home Values</li>
                                    <li><a href="/homevalues">Home Values Search</a></li> 
									<li><a href="/trackhomevalue">Track Home Value</a></li>
                                    <li><a href="/homevalues/compare">Compare Home Values Instantly</a></li>
                                    <li><a href="/homevalues/TexasAppraisalDistricts">List of Appraisal Districts</a></li>
									<li><a href="/insight_category/homevalue">Home Value Resource</a></li>
									<li><a href="/homeownersvideos/category/view/home-value/homevalue">Videos About Home Values</a></li>
                                    <!---
                                    <li class="font_size--large font_weight--bold color_black pb-3">\Home</li>
                                    <li><a href="/sell_your_home">Ready to sell your home?</a></li>
                                    <li><a href="#">Home selling guide</a></li>
                                    <li><a href="/homevalues">See your home's value</a></li>
                                                                        <li><a href="/realestatepro#Nearby" class="agent-request-callback">Connect with a local expert</a></li>
                                                                        <li><a href="#">Get Marketinsight</a></li>-->
                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Home Selling Tips & Tools</li>
                                    <!--<li><a href="/mortgage/calculators/buying-vs-renting-calculator/HF05">Buying vs Renting Calculator</a></li>-->
                                    <li><a href="/insight_category/selling">Home Selling Resource</a></li>
                                    <li><a href="/answers_category/home-selling_14">Selling Q&A</a></li>
                                    <li><a href="/blogs_category/home-selling_14">Latest Blogs about Selling</a></li>
                                </ul>
                                <div class="graphics_section">
                                    <div class="font_size--large_extra_extra font_weight--bold color_black pb-3" style="max-width:250px;">Compare the value of your house!</div>
                                    <div class="color_slate font-weight-normal fw-normal font_size--small pb-3">Try our new tool that lets you compare home values instantly from leading sources.</div>
                                    <a href="/homevalues/compare" class="btn btn--prominent mb-5">Get Home Value Comparables</a>
                                    <img loading="lazy" fetchPriority="low" alt="Home Values" class="img-fluid" src="https://content.harstatic.com/media/temprary/menu_graphics_2.webp">
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item dropdown ">
                    <a class="nav-link dropdown-toggle" href="#"  role="button" data-toggle="dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Explore
                    <img loading="lazy" fetchPriority="low" alt="dropdown image" class="linkarrow" src="https://content.harstatic.com/resources/images/icons/red_down_arrow.svg">
                    </a>
                    <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                    <ul class="dropdown-content  font_size--medium font_weight--semi_bold">
                            <li>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Schools</li>
                                    <li><a href="/school">Public Schools</a></li>
									<li><a href="/charter_schools">Charter Schools</a></li>
									<li><a href="/texas_private_schools">Private Schools</a></li>
                                    <li><a href="/school/search-compare">Compare Schools</a></li>								
									<li><a href="/school/search/advance?grade=E&schtype=0">Search Elementary Schools</a></li>
									<li><a href="/school/search/advance?grade=M&schtype=0">Search Middle Schools</a></li>
									<li><a href="/school/search/advance?grade=S&schtype=0">Search High Schools</a></li>									
                                    <li><a href="/best_elementary_schools">Best Elementary Schools</a></li>
                                    <li><a href="/best_middle_schools">Best Middle Schools</a></li>
                                    <li><a href="/best_high_schools">Best High Schools</a></li>                                                                    
                                    <li><a href="/top_high_schools_by_SAT">High Schools with Top SAT Scores</a></li>
                                    <li><a href="/school/texas_school_districts">School Districts</a></li>
                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Communities</li>
                                    <li><a href="/neighborhoods">Neighborhoods</a></li>
                                    <li><a href="/highrisefinder">High-Rise Living</a></li>
                                    <li><a href="/masterplanned">Master-Planned Communities</a></li>									
                                    <li><a href="/golfcourses">Golf Course Communities</a></li>
                                    <li><a href="/seniorliving">Senior Living</a></li>
                                    <li><a href="/historic_districts">Historic Districts</a></li>
									<li><a href="/texasrealestate?tab=city">Texas Cities</a></li>
									<li><a href="/texasrealestate?tab=county">Texas Counties</a></li>
									<li><a href="/texasrealestate?tab=zip">Texas Zip Codes</a></li>
                                    <li><a href="/homevalues/TexasAppraisalDistricts">Appraisal Districts</a></li>
                                    <li><a href="/texasrealestate/waterdistricts">Water Districts</a></li>									
                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Resources</li>
                                    <li><a href="/answers">Ask a Pro / Community</a></li>
                                    <li><a href="/insight">RealInsight (Newsletter) </a></li>

                                    <li><a href="/blogs">Real Estate Blogs</a></li>
                                     
                                        <li><a href="/flood">Flood Resource</a></li>
                                    									<li><a href="/insight_category/realestateinvestment">Investors</a></li>
                                    <li><a href="/content/consumer_knowledge">Knowledge Videos</a></li>
                                    <li><a href="/content/homeowners_videos">Homeowner Videos</a></li>
                                    <li><a href="/dictionary">Knowledge Base</a></li>
                                    <li><a href="/content/houston_neighborhood_videos">Houston Neighborhood Videos</a></li>
                                    <li><a href="/texasrealestate/?tab=mktarea">Greater Houston Market Areas</a></li>
                                                                        <li><a href="javascript:void(0);" onclick="trackForMatomo()">Texas Electricity Learning</a></li>									<li><a href="https://cms.har.com/tools">View All Tools</a></li>
                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Commercial</li>
                                    <li><a href="https://commgate.com/search/#/search/grid?bounds=29.905372412150157,-95.1553918031829,29.19815276895737,-96.3913537172454&locations=%7B%22geographies%22%3A%5B%7B%22regionCode%22%3A%22TX%22,%22country%22%3A%7B%22countryCode%22%3A%22USA%22%7D%7D%5D%7D&propertyCategories=RETAIL">Retail</a></li>
                                    <li><a href="https://commgate.com/search/#/search/grid?bounds=29.905372412150157,-95.1553918031829,29.19815276895737,-96.3913537172454&locations=%7B%22geographies%22%3A%5B%7B%22regionCode%22%3A%22TX%22,%22country%22%3A%7B%22countryCode%22%3A%22USA%22%7D%7D%5D%7D&propertyCategories=OFFICE">Office</a></li>
                                    <li><a href="https://commgate.com/search/#/search/grid?bounds=29.905372412150157,-95.1553918031829,29.19815276895737,-96.3913537172454&locations=%7B%22geographies%22%3A%5B%7B%22regionCode%22%3A%22TX%22,%22country%22%3A%7B%22countryCode%22%3A%22USA%22%7D%7D%5D%7D&propertyCategories=INDUSTRIAL">Industrial</a></li>
									<!--<li><a href="https://commgate.com/search/#/search/grid?bounds=29.905372412150157,-95.1553918031829,29.19815276895737,-96.3913537172454&locations=%7B%22geographies%22%3A%5B%7B%22regionCode%22%3A%22TX%22,%22country%22%3A%7B%22countryCode%22%3A%22USA%22%7D%7D%5D%7D&propertyCategories=LIFE_SCIENCE">Life Science</a></li>-->
                                    <li><a href="https://commgate.com/search/#/search/grid?bounds=29.905372412150157,-95.1553918031829,29.19815276895737,-96.3913537172454&locations=%7B%22geographies%22%3A%5B%7B%22regionCode%22%3A%22TX%22,%22country%22%3A%7B%22countryCode%22%3A%22USA%22%7D%7D%5D%7D&propertyCategories=LAND">Land</a></li>
                                    <li><a href="https://commgate.com/search/#/search/grid?bounds=29.905372412150157,-95.1553918031829,29.19815276895737,-96.3913537172454&locations=%7B%22geographies%22%3A%5B%7B%22regionCode%22%3A%22TX%22,%22country%22%3A%7B%22countryCode%22%3A%22USA%22%7D%7D%5D%7D&propertyCategories=FARM_RANCH">Farm/Ranch</a></li>
                                    <li><a href="https://commgate.com/search/#/search/grid?bounds=29.905372412150157,-95.1553918031829,29.19815276895737,-96.3913537172454&locations=%7B%22geographies%22%3A%5B%7B%22regionCode%22%3A%22TX%22,%22country%22%3A%7B%22countryCode%22%3A%22USA%22%7D%7D%5D%7D&propertyCategories=HOSPITALITY">Hospitality</a></li>
                                    <li><a href="https://commgate.com/search/#/search/grid?bounds=29.905372412150157,-95.1553918031829,29.19815276895737,-96.3913537172454&locations=%7B%22geographies%22%3A%5B%7B%22regionCode%22%3A%22TX%22,%22country%22%3A%7B%22countryCode%22%3A%22USA%22%7D%7D%5D%7D&propertyCategories=SPECIALTY">Specialty</a></li>
                                    <li><a href="https://commgate.com/search/#/search/grid?bounds=29.905372412150157,-95.1553918031829,29.19815276895737,-96.3913537172454&locations=%7B%22geographies%22%3A%5B%7B%22regionCode%22%3A%22TX%22,%22country%22%3A%7B%22countryCode%22%3A%22USA%22%7D%7D%5D%7D&propertyCategories=MULTIFAMILY">Multi-Family</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </li>


                <li class="nav-item dropdown ">
                    <a class="nav-link dropdown-toggle" href="#"  role="button" data-toggle="dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Agents
                    <img loading="lazy" fetchPriority="low" alt="dropdown image" class="linkarrow" src="https://content.harstatic.com/resources/images/icons/red_down_arrow.svg">
                    </a>
                    <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                        <ul class="dropdown-content  font_size--medium font_weight--semi_bold">
                            <li>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Agents/Brokers</li>
                                    <li><a href="/realestatepro">Real Estate Agents</a></li>
                                    <li><a href="/realestatepro/search?search_type=member&term=&officecity=&officezip=&rating=1">Agents with Ratings</a></li>
                                    <li><a href="/content/platinum_agent">Platinum Agents </a></li>
                                    <li><a href="/realestatepro/multilingual">Multi-lingual Agents</a></li>
                                    <li><a href="/realestatepro/multicultural">Multi-cultural Agents</a></li>
                                    <li><a href="/realestatepro/designations">Agents with Designations</a></li>
                                    <li><a href="/realestatepro/brokers">Real Estate Firms</a></li>
                                    <li><a href="/trec">Texas Real Estate Directory</a></li>
                                    <li><a href="/realestatepro/listby/city">Agents by City</a></li>
                                    <li><a href="/realestatepro/listby/zipcode">Agents by Zip Code</a></li>
                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Service Providers</li>
                                    <li><a href="/realestatepro/serviceproviders">Local Service Providers</a></li>
                                    <li><a href="/service_providers/inspectors/INSP">Inspectors</a></li>
                                    <li><a href="/service_providers/mortgage-services/MORT">Mortgage Services</a></li>
                                    <li><a href="/service_providers/insurance-_-general/INSGEN">Insurance/ General</a></li>
                                    <li><a href="/service_providers/pest-control/PEST">Termite & Pests</a></li>
                                    <li><a href="/service_providers/staging/STAGE">Home Staging</a></li>
                                    <!--<li><a href="/service_providers/builders/BUILD">Builders</a></li>-->
                                    <li><a href="https://www.har.com/joinhar/signup?type=AF">Service Provider Sign up</a></li>
                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">For Pros</li>
									<li><a href="/mobile">HAR.com Mobile App</a></li>
                                    <li><a href="/content/realtor">HAR Members Area</a></li>
                                    <li><a href="/agentmax/signup?cmp=1">Texas Realtors Claim Your Profile</a></li>
                                    <li><a href="https://www.har.com/education">Find Real Estate Training </a></li>
                                    <li><a href="https://cms.har.com/cer/">Join Client Experience Rating</a></li>
                                    <li><a href="/directions">Route Planner / Directions</a></li>
                                    <li><a href="https://cms.har.com/media/">Media App</a></li>
                                    <li><a href="https://cms.har.com/openhouse/">Open House Registry App</a></li>
                                    <li><a href="/mobile/#TexasAgents">Texas Real Estate Directory App</a></li>
                                    <li><a href="/trec">Texas Real Estate Directory</a></li>
                                    <li><a href="https://www.har.com/joinhar">Become a HAR Member</a></li>
                                    <li><a href="/content/page/platinum_services">Become a Platinum Agent</a></li>
                                    <li><a href="https://www.har.com/joinhar/signup?type=AF&trec_num=">Become an Affiliate Member</a></li>

                                    <li><a href="https://cms.har.com/realtor-pros-tools/">Other Tools for Agents </a></li>
                                </ul>
                                <div class="graphics_section">
                                    <div class="border-bottom pb-3 mb-3">
                                        <div class="font_size--large_extra_extra font_weight--bold color_black pb-3" style="max-width:250px;">Ask a Pro</div>
                                        <div class="color_slate font-weight-normal fw-normal font_size--small pb-3">Find real estate questions & answers.
                                        Share insights and experience. Get answers, ask questions and more.
                                        </div>
                                        <a href="/answers" class="btn btn--prominent mb-3">Learn More</a>
                                    </div>
                                    <div class="pb-3 mb-3">
                                        <div class="font_size--large_extra_extra font_weight--bold color_black pb-3"><span class="pr-lg-3 pr-md-2 pr-0 pe-lg-3 pe-md-2 pe-0">Online Store</span> <img loading="lazy" fetchPriority="low" alt="Online Store" class="img-fluid mt-2" src="https://content.harstatic.com/media/temprary/realtor_store_logo.png"></div>
                                        <!--<div class="color_slate font-weight-normal font_size--small pb-3">Try our new tool that lets you compare home values instantly from leading sources.</div>-->
                                        <a href="https://store.har.com/" class="btn btn--ordinary mb-3">Learn More</a>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>

                <li class="nav-item dropdown cols_3 ">
                    <a class="nav-link dropdown-toggle" href="#"  role="button" data-toggle="dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Mortgage
                    <img loading="lazy" fetchPriority="low" alt="dropdown image" class="linkarrow" src="https://content.harstatic.com/resources/images/icons/red_down_arrow.svg">
                    </a>
                    <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                        <ul class="dropdown-content  font_size--medium font_weight--semi_bold">
                            <li>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Mortgage</li>
                                    <li><a href="/mortgage">Mortgage Center</a></li>
                                    <li><a href="/mortgage/rates-nearby">Mortgage Rates Nearby</a></li>
                                    <li><a href="/mortgage/calculator">Mortgage Calculator</a></li>
                                    <li><a href="/blogs_category/mortgage-and-finance_20">Mortgage & Finance Articles</a></li>
                                    <li><a href="/mortgagedictionary">Mortgage Dictionary</a></li>
                                    <li><a href="/mortgage/types">Mortgage Types</a></li>
									<li><a href="/downpayment">Down Payment Assistance Programs</a></li>
									<li><a href="/homeownersvideos/category/view/financial/financial">Financial Videos</a></li>
                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Home Financing</li>
                                    <li><a href="/mortgage/calculators/should-i-refinance/HF01">Should I refinance?</a></li>
                                    <li><a href="/mortgage/calculators/how-much-will-my-fixed-rate-mortgage-payment-be/HF02">How much will my fixed rate mortgage payment be?</a></li>
                                    <li><a href="/mortgage/calculators/how-much-will-my-adjustable-rate-mortgage-payments-be/HF03">How much will my adjustable rate mortgage payments be?</a></li>
                                    <li class="pb-2"><a href="/mortgage/financialcalculators">See More →</a></li>

                                    <li class="font_size--large font_weight--bold color_black pb-3">Personal Financing</li>
                                    <li><a href="/mortgage/calculators/how-much-car-can-i-afford/PC01">How much car can I afford?</a></li>
                                    <li><a href="/mortgage/calculators/how-long-will-it-take-to-pay-off-my-credit-card/PC02">How long will it take to pay off my credit card?</a></li>
                                    <li><a href="/mortgage/calculators/how-much-will-i-need-to-save-for-a-major-purchase/PC04">How much will I need to save for a major purchase?</a></li>
                                    <li class="pb-2"><a href="/mortgage/financialcalculators">See More →</a></li>
                                </ul>

                                

                                <div class="graphics_section">
	                                <div class="row no-gutters pb-4">
		                                <div class="col-auto"><img class="mr-3 me-3" style="max-width:60px;" src="https://content.harstatic.com/media/artwork/payment_assistance.svg" alt="Down Payment Assistance"></div>
		                                <div class="col font_size--large_extra_extra font_weight--bold color_black">Down Payment Assistance</div>
	                                </div>
	                                
	                                	
                                    <div class="color_slate font-weight-normal fw-normal font_size--medium pb-4">
	                                    More than 2,000 down payment assistance programs to help reduce your costs of homeownership.<br><br>
	                                    Active Duty . Reserve/National Guard . Veteran . Educator . Law Enforcement . Firefighter . Healthcare providers and more
                                    </div>
                                    <a href="/downpayment" class="btn btn--prominent btn--large mb-3">Get Started</a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </li>


                <li class="nav-item dropdown cols_3 ">
                    <a class="nav-link dropdown-toggle" href="#"  role="button" data-toggle="dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    More...
                    </a>
                    <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                    <ul class="dropdown-content  font_size--medium font_weight--semi_bold">
                            <li>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">RealInsight (Newsletter)</li>
									<li><a href="/insight_category/buying">Home Buying</a></li>
                                    <li><a href="/insight_category/selling">Home Selling</a></li>
									<li><a href="/insight_category/realestateinvestment">Investing</a></li>
                                    <li><a href="/insight_category/moving">Moving</a></li>
                                    <li><a href="/insight_category/repair">Home Repair</a></li>
                                    <li><a href="/insight_category/remodeling">Remodeling</a></li>
                                    <li><a href="/insight_category/appraisals">Appraisals</a></li>
                                    <li><a href="/insight_category/insurance">Insurance</a></li>
									<li><a href="/insight_category/taxes">Taxes</a></li>
									<li><a href="/insight_category/safety">Safety</a></li>
                                </ul>							
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">About</li>
									 <li><a href="/content/realtor">HAR Member Area</a></li>
                                    <li><a href="https://cms.har.com/about-us/">About HAR</a></li>
                                    <li><a href="/content/department/newsroom/">Newsroom</a></li>
									<li><a href="/content/webstats">HAR.com/Mobile Statistics</a></li>
                                    
                                    <li><a href="https://cms.har.com/code-of-ethics/">Code of Ethics</a></li>
                                </ul>
                                <ul class="mn_section">
                                    <li class="font_size--large font_weight--bold color_black pb-3">Contact</li>
                                    <li><a href="https://cms.har.com/help-center/">Help Center</a></li>
                                    <li><a href="/contact-us/">Contact us</a></li>
                                    <li><a href="https://cms.har.com/accessibility/">Accessibility</a></li>
                                    <li><a href="https://cms.har.com/privacy-policy-2/">Privacy Policy</a></li>
                                    <li><a href="https://cms.har.com/termsofuse/">Terms of Use</a></li>
									<li><a href="/content/sitesearch">HAR Site Search</a></li>
                                </ul>
                                <!--<div class="graphics_section">
                                    <div class="font_size--large_extra_extra font_weight--bold color_black pb-3" style="max-width:260px;">Sign up for the Realinsight Newsletter</div>
                                    <div class="color_slate font-weight-normal font_size--small pb-3">Receive informative articles, local market statistics and helpful information.<span class="font_weight--bold"><a href="/insight"> Go to Realinsight</a></span></div>
                                    <input type="email" id="realemail2" class="form-control mb-2" name="realemail" aria-describedby="realemail" placeholder="What’s your email?">
                                    <button type="button" onclick="subscribe('realemail2');" class="btn btn--prominent mb-3">Subscribe</button>
                                    <div class="color_slate font_size--small_extra font_weight--regular pb-5" style="opacity:0.75;">By subscribing, you accept our <span class="font_weight--bold">privacy policy.</span></div>
                                </div>-->
								<div class="graphics_section">
									<div class="row small-gutters pb-2">
										<div class="col-auto"><a href="/insight"><img style="max-width:70px;" alt="Logo Real Insight" src="https://content.harstatic.com/media/icons/realinsight/RealInsight_logo_3.svg"></a></div>
										<div class="col">
											<div class="color_slate font-weight-normal fw-normal font_size--small pb-3">Up-to-date real estate industry trends, news, and insights.<span class="font_weight--bold"><a href="/insight"> Go to RealInsight</a></span></div>        
										</div>
									</div>
									
									<div class="font_size--large_extra_extra font_weight--bold color_black pb-2">Sign up for the Newsletter</div>
									
									<input type="email" id="realemail2" class="form-control mb-2" name="realemail" aria-describedby="realemail" placeholder="What’s your email?">
									<button type="button" onclick="subscribe('realemail2');" class="btn btn--prominent mb-3">Subscribe</button>
									<div class="color_slate font_size--small_extra font_weight--regular pb-5" style="opacity:0.75;">By subscribing, you accept our <span class="font_weight--bold"><a style="display: inline-block" href="https://cms.har.com/privacy-policy-2/">privacy policy</a>.</span></div>
								</div>									
                            </li>


                        </ul>
                    </div>
                </li>
								<li class="nav-item dropdown resp_user_menu d-md-none d-block">
					<a class="nav-link dropdown-toggle" href="#"  role="button" data-toggle="dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
					  Sign In
					</a>
					<div class="dropdown-menu" aria-labelledby="navbarDropdown">
					   <ul class="dropdown-content font_size--medium font-weight-bold fw-bold">
							<li>
								<ul class="mn_section">
									<li class="font_size--large font_weight--bold color_black pb-3">Consumer</li>
									<li><a href="/login">Sign In</a></li>
									<li><a class="" href="/login/createaccount?">Sign up / Create an account</a></li>
								</ul>
								<ul class="mn_section">
									<li class="font_size--large font_weight--bold color_black pb-3">REALTOR® / Pro</li>
									<li><a class="" href="/login#member">Sign In</a></li>			
									<li><a class="" href="https://www.har.com/joinhar"> Become a member</a></li>
									<li><a class="" href="https://www.har.com/agentmax"> Claim your profile</a></li>									
								</ul>
							</li>
						</ul>
					</div>
				</li>
				            </ul>
            <a id="MobileAppDownload" href="/mobile">
                <div class="pt-5 text-center pb-5 mb-5">
	                <div class="mb-5 pb-5">
                    	<div class="font_weight--bold color_carbon font_size--medium mx-auto pt-4 mb-4" style="max-width:175px;">Enhance your real estate experience with HAR App</div>
						<img loading="lazy" fetchPriority="low" alt="Apple Store" class="pr-3 pe-3" src="https://content.harstatic.com/media/icons/icon-apple-carbon.svg">
						<img loading="lazy" fetchPriority="low" alt="Google Play" class="pl-3 ps-3" src="https://content.harstatic.com/media/icons/icon-android-carbon.svg">
	                </div>
                </div>
            </a>
        </div>

        <div class="user_menu">
			<a class="pr-0 pe-0" href="/mobile"><img style="width:24px;height: 28px;" src="https://content.harstatic.com/media/icons/mobile_small_darkblue.svg" alt="HAR Mobile Page"></a>
			<a style="display:none;" href="#" class="search_icon"><img loading="lazy" fetchPriority="low" alt="search" src="https://content.harstatic.com/media/icons/icon-search_black.svg"></a>
            <!-- <a id="favoritemenu" href="javascript:void(0)"><img alt="search" src="https://content.harstatic.com/media/icons/icon-favorite-off_black.svg"></a>-->
            <div class="dropdown fav_menu">
                <a class="dropdown-toggle color_snow_white pl-0 ps-0" href="#" role="button" id="favoritemenu" aria-haspopup="true" aria-expanded="false">
                    <span style="background-size: cover;" class="heard_icon heart_total ">0</span>
                </a>
                <div id="favorite_menu" class="dropdown-menu" data-toggle="collapse"  aria-labelledby="favoritemenu">
                    <div class="menu_list">

                    </div>
                                        <div id="browsefavorites" class="menu_footer text-center"><a class="font_weight--semi_bold" href="/myaccount/bookmarks">View all Favorites</a></div>
                                    </div>

            </div>
            <a id="HamIcon" href="javascript:void(0);"><img fetchPriority="high" alt="menu" style="width:24px;" src="https://content.harstatic.com/media/icons/menu_icon_humberger2.svg"></a>

                        <div class="dropdown my_account_menu">
                <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    Sign In
                </a>
				
				<div class="dropdown-menu" aria-labelledby="dropdownMenuLink2" style="min-width:235px;">
	                <div class="border-bottom border-color--cloudy-sky-light font_weight--semi_bold font_size--large pb-2 mt-3">Consumer</div>
	                
					<a class="dropdown-item" href="/login"> Sign in</a>
					<a class="dropdown-item" href="/login/createaccount?"> Sign up / Create an account</a>
					
					<div class="mt-5 border-bottom border-color--cloudy-sky-light font_weight--semi_bold font_size--large pb-2">REALTOR® / Pro</div>
					<a class="dropdown-item" href="/login#member"> Sign in</a>
					<a class="dropdown-item" href="https://www.har.com/joinhar"> Become a member</a>
					<a class="dropdown-item" href="https://www.har.com/agentmax"> Claim your profile</a>
					<a class="dropdown-item" href="/content/realtor"> HAR Member's Area</a>					
				</div>
            </div>

            
            <div class="clearfix"></div>
        </div>
    </div>
</nav>
<div id="texas_tracking" style="display: none;"></div>

<script>
    if(typeof subscribe !== "function"){
        function subscribe(ele) {
            email = jQuery("#" + ele).val();
            email = email.trim();
            if (email.length <= 0) {
                alert("Enter email address.");
                jQuery("#" + email).focus();
                return true;
            }
            location.href = "/" + "insight/preferences/?email=" + email;
            return false;
        }
    }
        function trackForMatomo(){
        var matomoTrackingUrl = 'https://matomo.har.com/matomo.php?' +
        'token_auth=50d549b4c5e24b1524f459ddf4b78ce2&' +
        'idsite=5&' +
        'rec=1&' +
        'apiv=1&' +
        'rand=' + Date.now() + '&' +
        'e_c=' + encodeURIComponent('Texas Electricity Learning') + '&' +
        'e_a=' + encodeURIComponent('Texas-Link-Clicked') + '&' +
        'e_n=' + encodeURIComponent('Texas-Link-Clicked');

        $('#texas_tracking').html('<img src="' + matomoTrackingUrl + '">');
        setTimeout(function() {
             window.location.href = 'https://www.energyogre.com/texas-electricity-learning-center?utm_source=website&utm_medium=affiliate&utm_term=har-texas-electricity-learning&utm_content=har-texas-electricity-learning&ref=16yy8'; 
        }, 1000);
    }
    </script>




                    
    <div class="PageContent mt-0">

        <!--[if IE]>
		<link href="../resources/css/views/ie-only.css" rel="stylesheet" type="text/css">
	<![endif]-->
<input type="hidden" name="_token" value="ZXXNcRKfNJkknqZKwmf8R45NHe0quWE5thuyTqFu">



<div class="container" style="max-width:1280px !important;">
<div class="listing_detail_standalone">
	<div class="listing_detail_cntr">
		<div class="inpage_nav_wraper">
		
			<div id="InpageNav">
				            <a href="#audioComp" class="nav-link active">Info/audio</a>
                <a href="#propertyMap" data-target="#propertyMap" class="nav-link propertyMap">Map</a>
            <a href="#propertyLotInfo" class="nav-link propertyDetail lot-info-wrapper">Lot</a>
                    <a href="#subDivisonInfo" class="nav-link subDivisonInfo"> Neighborhood</a>
                            <a href="#analyticBlock" class="nav-link analyticBlock">Analytics</a>
                        <a href="#TaxInfo" class="nav-link TaxInfo">Tax</a>
                            <a href="#ddrBlock" class="nav-link ddrBlock">Financial</a>
                        <a href="#calculatorInfo" class="nav-link calculatorInfo">Mortgage</a>
                        <a href="#schoolInfo2" class="nav-link schoolInfo"> School</a>
                        <a href="#listingBroker" class="nav-link listingBroker">Broker</a>
            			</div>
		</div>
					<div data-spy="scroll" data-target="#InpageNav" data-offset="0"  data-bs-smooth-scroll="true" class="ldc_left">
			
<style type="text/css">
    .avm-map-data .card{
        font-size: 12px;
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }
    @media  only screen and (max-width: 767px) {
        #historytable {
            overflow-x: scroll !important;
            overflow-y: hidden !important;
        }
        .facts--,
        .property--tax,
        .lot-info-container {
            overflow-x: scroll !important;
        }
        .listing_detail_cntr {
            padding-bottom: 0px !important;
        }
        h2 {
            margin-bottom: 15px !important;
        }
    }
    .breadcrumbs
    {
        padding: 15px 0px !important;
    }
</style>

    <div id="SharingBar" class="pt-2 pb-2 mt-1 mb-1  mr-3 ml-3">
                    <div class="border_radius--pudgy bg_color_har_blue_light_extra d-md-none d-flex p-2 mb-3" style="border:2px solid #94aaeb;">
                <div class="bg--image circle-image mr-3" style="background-image:url(https://pics.harstatic.com/agent/476135_112x112.jpg?ts=2020-08-14T17:20:00);width:32px;height:32px;"></div>
                <a href="/schedule_showing/74257223" target="_blank" class="text-decoration-none font_weight--bold font_size--medium align-self-center">Want to Schedule a Tour?</a>
                <a class="d-none ml-auto  align-self-center pr-2" href="javascript:void(0);"><img src="https://content.harstatic.com/media/icons/icons-24-x-24-close-thick.svg" alt="Close"></a>
            </div>
                        <div class="row no-gutters">
                                <div class="col align-self-center">
                
									                                                    <div class="breadcrumbs pb-3"><div><a class="color_auxiliary" href="/texasrealestate/?tab=region">Texas</a><a class="color_auxiliary" href="/search/dosearch?fips_code=48201">Harris</a><a class="color_auxiliary" href="/houston/realestate/for_sale">Houston</a><a class="color_auxiliary" href="/zipcode_77009/realestate/for_sale">77009</a><a class="color_auxiliary" href="/search/dosearch?ptids=7997">Northside Village</a><span class='breadcrumbs__empty'>Details</span></div></div>
                        									                </div>     
                                <div class="col-auto text-right  pt-2 pb-2 align-self-center">
                                                                            <a  href="javascript:void(0);" data-appid="5" class="favorite_big_heart btn btn--ordinary btn--medium btn--icon btn--icon--lone d-md-inline-block d-none fav--9664623 cmd--fav btn--icon--favorite" data-edit_node="hide" data-toggle="tooltip" data-placement="top" title="Bookmark" style="width:auto;" data-lid="9664623" data-image="https://photos.harstatic.com/411360813/lr/img-1.jpeg?ts=2025-04-07T13:11:39.830" data-mls="74257223" data-address="4017 Robertson St, Houston, TX 77009" data-bookmarked="0" aria-label="Save 4017 Robertson St, Houston, TX 77009 as favorite"></a>
                    <a  href="javascript:void(0);" data-appid="5" class="btn btn--icon--borderless  d-inline-block d-md-none btn--icon fav--9664623 cmd--fav btn--icon--favorite" data-edit_node="hide" data-toggle="tooltip" data-placement="top" title="Bookmark" style="width:auto;" data-lid="9664623" data-image="https://photos.harstatic.com/411360813/lr/img-1.jpeg?ts=2025-04-07T13:11:39.830" data-mls="74257223" data-address="4017 Robertson St, Houston, TX 77009" data-bookmarked="0" aria-label="Save 4017 Robertson St, Houston, TX 77009 as favorite"></a>
                                                            <a  href="javascript:void(0);" data-lid="9664623" class="cmd--notint btn btn--ordinary btn--medium btn-nointerest btn--icon--onlyicon d-md-inline-block d-none btn--icon btn--icon--lone btn--icon--dislike" data-toggle="tooltip" data-placement="top" title="Not interested" style="width:auto;"></a>
                                           
                    <a href="#SharingPopup" class="btn btn--ordinary btn--medium btn--icon--onlyicon ShareBtn d-md-inline-block d-none" data-toggle="tooltip" data-placement="top" title="Share" style="width:auto;" onclick="javascript:OpenShare(5,'9664623')"><img  alt="share" src="https://content.harstatic.com/media/icons/icon-share.svg" width="40" height="41" /></a>
                    <!--print options-->
                    
                    <div class="dropdown dropdown--custom dropdown--custom_medium d-md-inline-block d-block">

                <a href="#" id="dropdownMenuButton_icon" aria-haspopup="true" aria-expanded="false" class="btn bg_color_slate_light w-100 btn btn--ordinary btn--medium btn--icon--onlyicon d-md-inline-block d-none" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-placement="top" title="Print" style="width:auto;">
            <img alt="Print Flyer" src="https://content.harstatic.com/media/icons/icon-print.svg" width="40" height="40" />
        </a>
        
        <div class="dropdown-menu pb-0 border-0" aria-labelledby="dropdownMenuButton" style="width:280px;">
            
<a class="dropdown-item pt-3 pb-3" target="_blank" href="https://www.har.com/flyer/2/mlnum/74257223?key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=">Printable Flyer (Primary Photo)</a>
<a class="dropdown-item pt-3 pb-3" target="_blank" href="https://www.har.com/flyer/1/mlnum/74257223?key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=">Printable Flyer (Multiple Photos)</a>
<a class="dropdown-item pt-3 pb-3" target="_blank" href="https://www.har.com/search/flyer/74257223?customer=1&key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=&openLang">Print Flyer for Consumer or Buyer</a>
<a class="dropdown-item pt-3 pb-3" target="_blank" href="https://www.har.com/search/flyer/74257223?customer=1&simple=1&key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=&openLang">Print Simplified Flyer</a>
                    </div>
    </div>
    
 

                                         <div class="dropdown change_selected d-md-inline-block d-none" style="vertical-align: middle;">
                        <button class="btn btn--ordinary btn--medium dropdown-toggle d-flex align-items-center" type="button" id="pickLangDesk" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="height:40px;padding:0px 8px !important;">
                        	                            <img class="mr-1" style="border:2px;width:22px;height:auto;" src="https://content.harstatic.com/resources/images/find_a_pro/flags_mini/us.jpg" alt="English">
                                                    </button>
                        <div class="dropdown-menu depth--above_all dropdown-menu-right" style="box-shadow: 0 4px 8px 0 rgba(0,0,0,.24);border: 1px solid #dfe3f0;background-color: #fff;min-width: 200px;" aria-labelledby="pickLang2" x-placement="bottom-start">
                            <a class="dropdown-item d-flex align-items-center detailpick lang_picker" data-langid="en" href="javascript:void(0)"><img class="mr-2" style="border:2px;width:22px;height:auto;" src="https://content.harstatic.com/resources/images/find_a_pro/flags_mini/us.jpg" alt="English"> English</a>
                            <a class="dropdown-item d-flex align-items-center detailpick lang_picker" data-langid="es" href="javascript:void(0)"><img class="mr-2" style="border:2px;width:22px;height:auto;" src="https://content.harstatic.com/resources/images/find_a_pro/flags_mini/es.jpg" alt="Spanish"> Spanish</a>
                            
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <a class="dropdown-item d-flex align-items-center" target="_blank" href="https://www.har.com/search/flyer/74257223?customer=1&simple=1&key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=&openLang">Show in other languages</a>
                                                                                                                                                                </div>
                    </div>
                                        
                    <div class="dropdown dropdown--custom dropdown--custom_medium d-inline-block float-right d-md-none">
                        <a class="text-decoration-none font_size--medium hover-opacity hover--color-auxiliary color_auxiliary pl-3 pr-2" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <img alt="more" src="https://content.harstatic.com/media/icons/dot_dot_dot.svg">
                        </a>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuLink" x-placement="bottom-end">
                            <a class="dropdown-item" onclick="javascript:OpenShare(5,'9664623')">Share</a>
                                                        <a  class="cmd--notint dropdown-item" href="javascript:void(0);" data-lid="9664623">Not interested</a>
                                                        <div class="dropdown-divider"></div>
                            <a class="dropdown-item pt-3 pb-3" target="_blank" href="https://www.har.com/flyer/2/mlnum/74257223?key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=">Printable Flyer (Primary Photo)</a>
<a class="dropdown-item pt-3 pb-3" target="_blank" href="https://www.har.com/flyer/1/mlnum/74257223?key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=">Printable Flyer (Multiple Photos)</a>
<a class="dropdown-item pt-3 pb-3" target="_blank" href="https://www.har.com/search/flyer/74257223?customer=1&key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=&openLang">Print Flyer for Consumer or Buyer</a>
<a class="dropdown-item pt-3 pb-3" target="_blank" href="https://www.har.com/search/flyer/74257223?customer=1&simple=1&key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=&openLang">Print Simplified Flyer</a>


 

                         </div>
                    </div>

                </div>
            </div>
           
    </div>
    <!-- / SharingBar -->
    <!-- address row -->

    <div class="px-4">
        <div class="row no-gutters pb-2">
            <div class="col-md col-12">
                                <div class="dropdown change_selected d-inline-block d-md-none" style="vertical-align: middle;float:right">
                    <button class="btn btn--ordinary btn--small dropdown-toggle d-flex align-items-center" type="button" id="pickLangResp" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="height:40px;padding:0px 8px !important;font-weight:normal;">
                                                EN                        
                                            </button>
                    <div class="dropdown-menu depth--above_all dropdown-menu-right" style="box-shadow: 0 4px 8px 0 rgba(0,0,0,.24);border: 1px solid #dfe3f0;background-color: #fff;min-width: 200px;" aria-labelledby="pickLangResp" x-placement="bottom-start">
                        <a class="dropdown-item d-flex align-items-center detailpick lang_picker" data-langid="en" href="javascript:void(0)"><img class="mr-2" style="border:2px;width:22px;height:auto;" src="https://content.harstatic.com/resources/images/find_a_pro/flags_mini/us.jpg" alt="English"> English</a>
                        <a class="dropdown-item d-flex align-items-center detailpick lang_picker" data-langid="es" href="javascript:void(0)"><img class="mr-2" style="border:2px;width:22px;height:auto;" src="https://content.harstatic.com/resources/images/find_a_pro/flags_mini/es.jpg" alt="Spanish"> Spanish</a>
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            <a class="dropdown-item d-flex align-items-center" target="_blank" href="https://www.har.com/search/flyer/74257223?customer=1&simple=1&key=eyJhZ2VudGtleSI6InN0ZXZlbmIiLCJFWFBJUkVTIjoxNzQ0OTg2MzM1LCJUT0tFTiI6ImNiYTlkODE0YjBkNGFjYTk3NTEyMmRkNzBjOTlhYWMyIn0=&openLang">Show in other languages</a>
                                                                                                                                            </div>
                </div>
                                
                <h1 class="font_size--large_extra_extra color_carbon mb-md-2 mb-1" tabindex="0">
                    4017 Robertson St
                </h1>
                <div class="font_weight--semi_bold font_size--medium d-inline-block mr-2">Houston, TX 77009</div>
                                <a href="/directions?address=74257223" target="_blank">Get Directions</a>
                                <div class="pt-2 pb-md-0 pb-2">
            </div>
            </div>
            <div class="col-md-auto col-12">
                                <div class="row justify-content-end">
                    <div class="col-md-auto col-12 pt-3 pt-md-0">
                        <div class="pb-2">
                            <div class="label label--forsale_type">                                For Sale
                                                            </div>
                                                        <div class="label label--forsale" title="Available For Sale / For Rent">Active</div>
                            <div class="label label--forsale d-none" title="Available For Sale / For Rent" style="border: solid 1px #94aaeb;background: #dae1f8;color: #0738CD;"><a href="#" class="text-decoration-none">Track home value</a></div>
                                                                                </div>
                        <div class="pt-1 pt-md-0">
                            <div class="row pb-2 no-gutters">
                                <div class="col-auto pr-3 pr-md-5 align-self-center">
                                    <span class="font_size--large_extra font_weight--semi_bold mr-2">
                                                                                $998,500
                                                                                                                    </span>
                                </div>
                                <div class="col-auto">
                                    
                                    
                                    <div id="mortgageText" class="font_size--small_extra color_slate_light"><a href="#calculatorInfo" id="topcalclbl" class="nav-link calculatorInfo p-0">Est. <span id="top_ptypedetail">Mortgage</span> <span id="estimatedMonthlyTax">5,112</span>/month</a></div>
                                                                        <a class="font_size--small_extra font_weight--regular CurrencyConverter mb-1 d-block pt-2 pt-md-0 " data-amount="998500" data-amountformat="998,500" href="javascript:void(0);"><img alt="mortgage calculator" class="mr-1" src="https://content.harstatic.com/media/icons/icon-converter-calculator_small_blue.svg" width="9" height="10">
                                        Currency converter
                                    </a>


                                </div>
                            </div>
                                                    </div>

                    </div>
                </div>
                             
            </div>
        </div>

    </div>
     <!-- / address row -->
	<div class="">

			<div class="" id="myTabContent">
			<!-- start of Tab ListingDetails -->
			<div class="" id="ListingDetails" role="tabpanel" aria-labelledby="listing-tab">
    <!-- banner -->
  <div class="pd_banner ">
                                     <div class="LoadingSliderComponent">
  <center>
    <img alt="loading" src="https://content.harstatic.com/img/common/loading1.gif" width="46" height="47" />
  </center>
</div>
<div class="pd_banner_slides slider_show_onload" style="display:none !important;">

              <div class="pd_banner_item position-relative" data-slide="0" data-mid="412355825">
    <div class="pd_banner_item_inner">
      <div class="openGallery slideHasTags openGallery--overlay" style="position:absolute;z-index:150;width:100%;height:100%;opacity:0.9;overflow: hidden;border-radius: 10px;cursor:pointer;" data-slide="0"></div>
            <div class="pointers position-absolute" style="z-index:200;top:14px;right:0px;bottom:0px;width:152px;height:30px">
     
        <div class="position-absolute align-items-center mediaTagVisiblitySwitcher" style="display:none;left:auto;right:15px; top:0px;z-index:111;border-radius:18px; background-color:#fff; padding:2px 2px 2px 10px;" >
								<div class="color_cement_dark font_weight--bold pr-2">Tags</div>
								<div class="toggle_switch show_features" data-cb-id="switch--LDD--photo--412355825" data-photo-id="412355825" data-group="mediaTagGroupCheckboxLDK">
									<input type="checkbox"  class="mediaTagGroupCheckboxLDK mediaTag--toggle" id="switch--LDD--photo--412355825"><label  for="switch--LDD--photo--412355825"></label>
								</div>
							</div>
        
      </div>
      <div class="pointers position-absolute mediaTagContainerLDk-412355825 openGallery" data-slide="0" style="z-index:100;top:0px;left:0px;right:0px;bottom:0px;cursor:pointer;"></div>
            <img data-lazy="https://photos.harstatic.com/411360813/hr/img-1.jpeg?ts=2025-04-07T13:11:39.830" />
    </div>
  </div>
              <div class="pd_banner_item position-relative" data-slide="1" data-mid="412359281">
    <div class="pd_banner_item_inner">
      <div class="openGallery  openGallery--overlay" style="position:absolute;z-index:150;width:100%;height:100%;opacity:0.9;overflow: hidden;border-radius: 10px;cursor:pointer;" data-slide="1"></div>
            <div class="pointers position-absolute" style="z-index:200;top:14px;right:0px;bottom:0px;width:152px;height:30px">
     
        <div class="position-absolute align-items-center mediaTagVisiblitySwitcher" style="display:none;left:auto;right:15px; top:0px;z-index:111;border-radius:18px; background-color:#fff; padding:2px 2px 2px 10px;" >
								<div class="color_cement_dark font_weight--bold pr-2">Tags</div>
								<div class="toggle_switch show_features" data-cb-id="switch--LDD--photo--412359281" data-photo-id="412359281" data-group="mediaTagGroupCheckboxLDK">
									<input type="checkbox"  class="mediaTagGroupCheckboxLDK mediaTag--toggle" id="switch--LDD--photo--412359281"><label  for="switch--LDD--photo--412359281"></label>
								</div>
							</div>
        
      </div>
      <div class="pointers position-absolute mediaTagContainerLDk-412359281 openGallery" data-slide="1" style="z-index:100;top:0px;left:0px;right:0px;bottom:0px;cursor:pointer;"></div>
            <img data-lazy="https://photos.harstatic.com/411360813/hr/img-2.jpeg?ts=2025-04-07T13:38:38.283" />
    </div>
  </div>
          <div class="pd_banner_item position-relative" data-slide="2" data-mid="411834535">
    <div class="pd_banner_item_inner">
      <div class="openGallery  openGallery--overlay" style="position:absolute;z-index:150;width:100%;height:100%;opacity:0.9;overflow: hidden;border-radius: 10px;cursor:pointer;" data-slide="2"></div>
            <div class="pointers position-absolute" style="z-index:200;top:14px;right:0px;bottom:0px;width:152px;height:30px">
     
        <div class="position-absolute align-items-center mediaTagVisiblitySwitcher" style="display:none;left:auto;right:15px; top:0px;z-index:111;border-radius:18px; background-color:#fff; padding:2px 2px 2px 10px;" >
								<div class="color_cement_dark font_weight--bold pr-2">Tags</div>
								<div class="toggle_switch show_features" data-cb-id="switch--LDD--photo--411834535" data-photo-id="411834535" data-group="mediaTagGroupCheckboxLDK">
									<input type="checkbox"  class="mediaTagGroupCheckboxLDK mediaTag--toggle" id="switch--LDD--photo--411834535"><label  for="switch--LDD--photo--411834535"></label>
								</div>
							</div>
        
      </div>
      <div class="pointers position-absolute mediaTagContainerLDk-411834535 openGallery" data-slide="2" style="z-index:100;top:0px;left:0px;right:0px;bottom:0px;cursor:pointer;"></div>
            <img data-lazy="https://photos.harstatic.com/411360813/hr/img-3.jpeg?ts=2025-04-07T13:38:38.350" />
    </div>
  </div>
          <div class="pd_banner_item position-relative" data-slide="3" data-mid="411834538">
    <div class="pd_banner_item_inner">
      <div class="openGallery  openGallery--overlay" style="position:absolute;z-index:150;width:100%;height:100%;opacity:0.9;overflow: hidden;border-radius: 10px;cursor:pointer;" data-slide="3"></div>
            <div class="pointers position-absolute" style="z-index:200;top:14px;right:0px;bottom:0px;width:152px;height:30px">
     
        <div class="position-absolute align-items-center mediaTagVisiblitySwitcher" style="display:none;left:auto;right:15px; top:0px;z-index:111;border-radius:18px; background-color:#fff; padding:2px 2px 2px 10px;" >
								<div class="color_cement_dark font_weight--bold pr-2">Tags</div>
								<div class="toggle_switch show_features" data-cb-id="switch--LDD--photo--411834538" data-photo-id="411834538" data-group="mediaTagGroupCheckboxLDK">
									<input type="checkbox"  class="mediaTagGroupCheckboxLDK mediaTag--toggle" id="switch--LDD--photo--411834538"><label  for="switch--LDD--photo--411834538"></label>
								</div>
							</div>
        
      </div>
      <div class="pointers position-absolute mediaTagContainerLDk-411834538 openGallery" data-slide="3" style="z-index:100;top:0px;left:0px;right:0px;bottom:0px;cursor:pointer;"></div>
            <img data-lazy="https://photos.harstatic.com/411360813/hr/img-4.jpeg?ts=2025-04-07T13:38:38.430" />
    </div>
  </div>
          <div class="pd_banner_item position-relative" data-slide="4" data-mid="411834541">
    <div class="pd_banner_item_inner">
      <div class="openGallery  openGallery--overlay" style="position:absolute;z-index:150;width:100%;height:100%;opacity:0.9;overflow: hidden;border-radius: 10px;cursor:pointer;" data-slide="4"></div>
            <div class="pointers position-absolute" style="z-index:200;top:14px;right:0px;bottom:0px;width:152px;height:30px">
     
        <div class="position-absolute align-items-center mediaTagVisiblitySwitcher" style="display:none;left:auto;right:15px; top:0px;z-index:111;border-radius:18px; background-color:#fff; padding:2px 2px 2px 10px;" >
								<div class="color_cement_dark font_weight--bold pr-2">Tags</div>
								<div class="toggle_switch show_features" data-cb-id="switch--LDD--photo--411834541" data-photo-id="411834541" data-group="mediaTagGroupCheckboxLDK">
									<input type="checkbox"  class="mediaTagGroupCheckboxLDK mediaTag--toggle" id="switch--LDD--photo--411834541"><label  for="switch--LDD--photo--411834541"></label>
								</div>
							</div>
        
      </div>
      <div class="pointers position-absolute mediaTagContainerLDk-411834541 openGallery" data-slide="4" style="z-index:100;top:0px;left:0px;right:0px;bottom:0px;cursor:pointer;"></div>
            <img data-lazy="https://photos.harstatic.com/411360813/hr/img-5.jpeg?ts=2025-04-07T13:38:38.513" />
    </div>
  </div>
      
</div>

<script>
  function onSlideLoad(mid) {
    $(".mediaTagGroupCheckboxLDK").on("change", function() {
      if ($(this).is(":checked")) {
        $('.prnx-listingDK-Group').css({
          opacity: '0.5'
        })
        //$('.pd_banner_overlay').fadeOut()
        $('.openGallery--overlay').hide()
      } else {
        $('.prnx-listingDK-Group').css({
          opacity: '1'
        })
        //$('.pd_banner_overlay').fadeIn()
        $('.openGallery--overlay').show()
      }
    })
   

  }
</script>

                    
            <div class="pd_banner_overlay d-none">
                                <button id="gallerybtn" type="button" tabindex="0" class="btn btn--simple btn--small openGallery"><span class="font_weight--bold"><img src="https://content.harstatic.com/media/icons/icons-16-x-16-photo-gallery.svg" class="mr-2" alt=""> View Gallery </span> <span style="opacity:80%;">
                        5 photos  
                         </span></button>
                                <div class="clearfix"></div>
            </div>

            <div class="pd_banner_overlay">
                <div class="row no-gutters">
                                                                                                                       <div class="col-auto d-none">
                                <button class="btn btn--ordinary btn--medium openGallery" data-media-type="photo-Tab">
                                    5 photos
                                </button>
                                                                    <button class="btn btn--ordinary btn--medium AI-remodel d-inline-flex align-items-center" style="padding:0px 10px !important;">
                                        <img class="mr-2" style="width:22px;" src="https://content.harstatic.com/media/temporary/har_ai_invert_logo.png">Remodel
                                    </button>
                                                            </div>
                                                                                                            <div class="col text-right ">
                                                                       
                        
						                        							
																		</div>
                </div>
            </div>
    </div>
    <div class="pt-2 pb-2 mr-4 pr-md-5 ml-4 pl-md-5">                   
        <div class="mb-2 gallery_actions">
        <div class="row no-gutters">
                         <div class="col-auto pr-2 pb-2">
                <button class="btn btn--ordinary btn--medium openGallery d-flex align-items-center" data-media-type="photo-Tab" style="padding:0px 12px !important;">
                    <img class="mr-2  d-md-block d-none" style="width:22px;" src="https://content.harstatic.com/media/icons/a_all/photos_icon_black.svg">
                    <span>5 photos</span>
                </button>
            </div>
            
                            <div class="col-auto pr-2 pb-2 AI-remodel-btn">
                 
                </div>
                        
                        
                            <div class="col-auto pr-2 pb-2">            
                    						
					                </div>    
			
            
            
            


           
            </div>  
           
        </div>
    </div>
  
        <!-- / banner -->

    <!-- favi section -->
        <div id="FavoriteBox" class="pb-2 mr-4 pr-md-5 ml-4 pt-3 pl-md-5" style="display:none !important;">
    <input type="hidden" id="bookmark_current_app" name="bookmark_current_app" value="5" />
    <input type="hidden" id="bookmark_current_lid" name="bookmark_current_lid" value="9664623" />
    <input type="hidden" id="bookmark_current_bookmarked" name="bookmark_current_bookmarked" value="0" />
    <div class="border_radius--default p-md-4 p-2 cardv2">
        <div class="p-3">
            <div class="row no-gutters">
                <div class="col-auto align-self-center">
                    <div class="d-flex">
                        <img style="width:24px;" src="https://content.harstatic.com/media/icons/heart_filled.svg" alt="">
                        <div class="align-self-center pl-2"><h4 class="mb-0 d-inline-block" tabindex="0"> Added to Favorites</h4></div>
                    </div>
                </div>
                <div class="col align-self-center text-right">
                    <a data-appid="5" data-lid="9664623" href="javascript:void(0);" class="cmd--del-bookmark color_black font_size--medium font_weight--bold" style="opacity:48%;">
                        <span class="d-none d-md-inline-block"><img src="https://content.harstatic.com/media/icons/icon-delete-bookmark_black.svg" style="width:15px;" class="mr-2" alt=""> Delete Favorite</span>
                        <span class="d-inline-block d-md-none">Delete</span>
                    </a>
                </div>
            </div>

            <div class="row pt-4">
                <div class="col-sm-5 col-12 order-1 order-md-0">
                    <div class="font_size--medium  font_weight--semi_bold mb-2">Folder</div>

                    <div id="user_folder_list">
                                        </div>

                    <div class="pt-2">
                        <a id="cmd--edit-folders" data-edit_node="hide" data-appid="5" data-lid="9664623" data-mls="74257223" data-address="4017 Robertson St, Houston, TX 77009" data-bookmarked="0" data-image="https://photos.harstatic.com/411360813/lr/img-1.jpeg?ts=2025-04-07T13:11:39.830" href="javascript:void(0);"><span class="font_weight--semi_bold font_size--medium color_har_blue">Edit folders</span></a>
                    </div>
                </div>
                <div class="col-sm-7 col-12 order-0 order-md-1 pb-3 pb-md-0">
                    <div class="row">
                        <div class="col">
                            <div class="font_size--medium font_weight--semi_bold mb-2">Your notes</div>
                        </div>
                        <div class="col-auto">
                            <span class="font_weight--semi_bold font_size--medium color_available status-fav-notes-save" style="display:none">Saved!</span>
                        </div>
                    </div>
                    <div class="fav_container" style="min-height:85px;">
                        <a class="fav_container--lock" style="cursor:default;" data-toggle="tooltip" data-placement="top" title="" href="javascript:void(0);" data-original-title="Visible only to you"><img src="https://content.harstatic.com/media/icons/Lock.svg" alt="Visible only to you"></a>
                        <textarea autocomplete="off" data-appid="5" data-lid="9664623" class="form-control textarea-autogrowing txtstuff cmd--fav-save-notes" rows="3" placeholder="Write a note about this home..."></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
        <!-- / favi section -->

    <div class="pt-2 pb-2 mr-4 pr-md-5 ml-4 pl-md-5">
		        <div class="mb-4 mb-md-4 pb-2">
            <!-- feature glance row -->
<div class="border border_radius--default p-4 font_size--medium">
    <div class="row no-gutters">
        <div class="col-md-9 col-12">
                        <div class="row">
                                                                <div class="col-md-4 col-6 pb-2">
                    <span class="ftin"><span class="font_weight--bold">940 </span> Sqft</span>
                    <span class="metric" style="display: none;"><span class="font_weight--bold">87</span> (m²)</span>
                </div>
                                                <div class="col-md-4 col-6 pb-2">
                                        <span class="ftin"> <span class="font_weight--bold">26,933</span> Lot Sqft</span>
                    <span class="metric" style="display: none;"> <span class="font_weight--bold">2502</span> Lot (m²)</span>
                    
                </div>
                
                
                <div class="col-md-4 col-6 pb-2">
                    <div class="font_weight--bold">Single-Family</div>
                </div>
                
                                <div class="col-md-auto col-auto pb-2">
                     <div class="cardv2--landscape__content__footer_dayson">
                        <div class="circle_nimber" style="background-color: rgba(1,131,97,0.24);">9 Day(s) on Market</div>
                    </div>
                   </div>
                
                


            </div>
                    </div>

</div>
</div>
<!-- / feature glance row -->
                                      
        </div> 
                    <span id="audioComp" class="anchor"></span>
                    
                    <div class="mb-4 pb-5 border-bottom border-color--cloudy-sky-light">
	<div class="row">
		<div class="col-auto align-self-center">
			<h2 class="text-left mb-3" tabindex="0">Audio narrative <img class="ml-2" style="width:24px;" src="https://content.harstatic.com/media/icons/audio_tour_blue.svg"></h2>
		</div>
		<div class="col "></div>
	</div>
	<p id="speech-description" style="display:none">4017 Robertson St is listed for sale at $ 998,500. This is a Single-Family property. 940 square feet and was built in 1930. The lot size is 26933 Square feet. ##BR##Expand your investment portfolio with this incredible opportunity! Located just minutes from downtown, this property comes with preliminary proposed plans for a development featuring 16 patio homes with floor plans included. Currently, the site boasts multiple single-family, income-producing rental units. Contact us today for the latest rent rolls and more details!</p>
	<div class="row">
		<div class="col-md col-12">
			<div class="player_container green_theme">
				<div class="ready-player-1">
					<audio id="audio-speech-control" crossorigin preload="none">
						<source id="speech-control" src="" type="audio/mpeg">
					</audio>
				</div>
			</div>
		</div>

		<div class="col-md-auto col-12 align-self-center d-none d-md-block">
			<div class="dropdown dropdown--quickselect changevalue__single d-inline-block">
				<button class="btn btn--ordinary btn--small dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" value="Mid / High-Rise">Choose from 12 Languages</button>
				<div class="dropdown-menu respnsive_right depth depth--above_all" aria-labelledby="dropdownMenuButton" style="">
					<a class="dropdown-item speech-translation" href="#" data-language="ar">Arabic</a>
					<a class="dropdown-item speech-translation" href="#" data-language="zh-CN">Cantonese</a>
					<a class="dropdown-item speech-translation" href="#" data-language="en">English</a>
					<a class="dropdown-item speech-translation" href="#" data-language="fr">French </a>
					<a class="dropdown-item speech-translation" href="#" data-language="de">German</a>
					<a class="dropdown-item speech-translation" href="#" data-language="hi">Hindi</a>
					<a class="dropdown-item speech-translation" href="#" data-language="it">Italian</a>
					<a class="dropdown-item speech-translation" href="#" data-language="ja">Japanese</a>
					<a class="dropdown-item speech-translation" href="#" data-language="ko">Korean</a>
					<a class="dropdown-item speech-translation" href="#" data-language="zh-TW">Mandarin</a>
					<a class="dropdown-item speech-translation" href="#" data-language="pt">Portuguese</a>
					<a class="dropdown-item speech-translation" href="#" data-language="es">Spanish</a>
				</div>
			</div>
			<button role="button" class="btn btn--simple btn--small  btn--icon btn--icon--share mb-0 audio-share" aria-label="Search">Share</button>
		</div>

	</div>
	<div class="pb-3 pt-3 d-block d-md-none">
		<div class="dropdown dropdown--quickselect changevalue__single d-inline-block">
			<button class="btn btn--ordinary btn--small dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" value="Mid / High-Rise">Choose from 12 Languages</button>
			<div class="dropdown-menu respnsive_right depth depth--above_all" aria-labelledby="dropdownMenuButton" style="">
				<a class="dropdown-item speech-translation" href="#" data-language="ar">Arabic</a>
				<a class="dropdown-item speech-translation" href="#" data-language="zh-CN">Cantonese</a>
				<a class="dropdown-item speech-translation" href="#" data-language="en">English</a>
				<a class="dropdown-item speech-translation" href="#" data-language="fr">French</a>
				<a class="dropdown-item speech-translation" href="#" data-language="de">German</a>
				<a class="dropdown-item speech-translation" href="#" data-language="hi">Hindi</a>
				<a class="dropdown-item speech-translation" href="#" data-language="it">Italian</a>
				<a class="dropdown-item speech-translation" href="#" data-language="ja">Japanese</a>
				<a class="dropdown-item speech-translation" href="#" data-language="ko">Korean</a>
				<a class="dropdown-item speech-translation" href="#" data-language="zh-TW">Mandarin</a>
				<a class="dropdown-item speech-translation" href="#" data-language="pt">Portuguese</a>
				<a class="dropdown-item speech-translation" href="#" data-language="es">Spanish</a>
			</div>
		</div>
		<button role="button" class="btn btn--simple btn--small  btn--icon btn--icon--share audio-share" aria-label="Search">Share</button>
	</div>
</div>
        


    
        

    
    	                 
    
    
    <h2 class="listheading" tabindex="0" data-standardtxt="About this property" data-altheading="About this property">About this property</h2>
    <div>           
                <div class="highlight-featured mb-2 box--orange"> </div>

        </div> 

            <div class="row">
                <div class=" col-12">
                    <!-- Listing description -->
<p class="font_size--medium mb-3">
    Expand your investment portfolio with this incredible opportunity! Located just minutes from downtown, this property comes with preliminary proposed plans for a development featuring 16 patio homes with floor plans included. Currently, the site boasts multiple single-family, income-producing rental units. Contact us today for the latest rent rolls and more details!
</p>
<!-- \ Listing description -->
                </div>
                        </div>

<!-- paragraph -->

<!-- The property information herein and below is from the county appraisal district and should be independently verified. -->
    <!-- The property information herein and below is from the county appraisal district and should be independently verified. -->

<!--  paragraph -->
    
    <div class="row pb-4">
        <div class="col ml-auto">
         <a class="dropdown-toggle dropdownSwitchUnit font_size--medium float-md-right" href="javascript:void(0);"  role="button" id="dropdownSwitchUnit" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Measurement in Ft-in
            </a>
            <div class="dropdown-menu" aria-labelledby="dropdownSwitchUnit" x-placement="bottom-start" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(15px, 22px, 0px);">
                <a class="dropdown-item dropdownOptionSwitchUnitftin font_size--medium" onclick="javascript:switchUnit(1)" href="javascript:void(0)">Measurement in Ft-in</a>
                <a class="dropdown-item dropdownOptionSwitchUnitmetric font_size--medium" onclick="javascript:switchUnit(2)" href="javascript:void(0)">Measurement in Metric</a>
            </div>
        </div>
    </div>
    <div id="PropertyAspecC" class="mb-md-4 mb-3 pb-md-5 pb-4 border-bottom border-color--cloudy-sky-light">
    <div class="row">
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">MLS#

            </div>
            <div class="font_size--large text-break">
                                74257223
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">List Price

            </div>
            <div class="font_size--large text-break">
                                $998,500 ($1,062/Sqft.) 
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Listing Status

            </div>
            <div class="font_size--large text-break">
                                For Sale
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Address

            </div>
            <div class="font_size--large text-break">
                                4017 Robertson St
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">City

            </div>
            <div class="font_size--large text-break">
                                <a href="/houston/realestate">Houston</a>
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">State

            </div>
            <div class="font_size--large text-break">
                                TX
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Zip Code

            </div>
            <div class="font_size--large text-break">
                                <a href="/zipcode_77009/realestate">77009</a>
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">County

            </div>
            <div class="font_size--large text-break">
                                <a href="/harris/county_48201">Harris County</a>
                            </div>
        </div>
                                            <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Subdivision

            </div>
            <div class="font_size--large text-break">
                                <a href="/pricetrends/de-noyle-realestate/7997">De Noyle  (View subdivision price trend)</a>
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Legal Description

            </div>
            <div class="font_size--large text-break">
                                TRS 2A 21 21A & 31 ABST 1 J AUSTIN
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Property Type

            </div>
            <div class="font_size--large text-break">
                                Single-Family
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Stories

            </div>
            <div class="font_size--large text-break">
                                1
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Building Size

            </div>
            <div class="font_size--large text-break">
                                <span class="ftin">940 Sqft</span><span class="metric" style="display:none;">87 (m²).</span>/Appraisal District
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Architecture Style

            </div>
            <div class="font_size--large text-break">
                                Other Style
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Year Built

            </div>
            <div class="font_size--large text-break">
                                1930 /Appraisal District
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Lot Size

            </div>
            <div class="font_size--large text-break">
                                <span class="ftin">26,933 Sqft.</span><span class="metric" style="display:none;">2502 (m²).</span>/Appraisal District
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Key Map&copy;

            </div>
            <div class="font_size--large text-break">
                                453Z
                            </div>
        </div>
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">Market Area

            </div>
            <div class="font_size--large text-break">
                                <a href="/geomarketarea/northside-realestate/109">Northside</a>
                            </div>
        </div>
            </div>
            <div class="pt-md-2 pt-0">
                            <p class="font_size--large font_weight--regular">This listing features a dedicated property website. Check it out!</p>
                <a onclick="location.href='/site/4017-robertson-st_SITE74257223.htm'" class="btn btn--simple d-inline-block btn--small  bg_color_slate_light text-left">
                    <span class="color_har_blue_dark font_weight--bold font_size--medium">Property website</span>
                </a>
                   </div>
     
</div>

    
            <!-- Property Map -->
    <span class="anchor" id="propertyMap"></span>
    <div class="mb-4 pb-5 border-bottom  border-color--cloudy-sky-light">
                <h2 tabindex="0">Locate on the map</h2>
        <div id="mapcontainer">
            <a href="/directions?address=74257223" target="_blank" class="openMapView">
                                    <div class="mb-3 border_radius--round  image" style="height: 300px; width: 100%; background-size: cover; background-repeat: no-repeat; background-position: center center" data-map-url="https://www.har.com/api/staticmap?sensor=false&amp;zoom=17&amp;center=29.797257000000000,-95.357419000000000&amp;markers=icon:https://content.harstatic.com/resources/images/listing_details/placeicons/property.png|29.797257000000000,-95.357419000000000&amp;maptype=satellite&amp;client=gme-houstonrealtorsinformation&amp;token=web-property-map-detail&amp;size=640x450&amp;provider=apple&amp;signature=AqAQdQqvrylMf4Kc4qHORaW2m6I=" data-map-url-fallback="https://www.har.com/api/staticmap?sensor=false&amp;zoom=17&amp;center=29.797257000000000,-95.357419000000000&amp;markers=icon:https://content.harstatic.com/resources/images/listing_details/placeicons/property.png|29.797257000000000,-95.357419000000000&amp;maptype=satellite&amp;client=gme-houstonrealtorsinformation&amp;token=web-property-map-detail&amp;size=1000x450&amp;provider=default&amp;signature=KEO_b0LHE1vHWHfot1PTXM71Nw8=">
                        <img class="img-fluid img-loader" alt="loading" src="https://content.harstatic.com/img/common/loading1.gif" />
                    </div>
                            </a>
            
        </div>
                <div class="row small-gutters ">
            <div class="col col-6 col-md-6">
                <a class="btn btn--ordinary btn--fluid openStreetView" href="javascript:void(0);">Street <br class="d-md-none">view</a>
            </div>
            <div class="col col-6 col-md-6">
                <a href="/directions?address=74257223" target="_blank" class="btn btn--ordinary btn--fluid">Get <br class="d-md-none">directions</a>
            </div>
                                </div>
    </div>

    

        
    
    <!--
<style type="text/css">
    .mapillary-cover-background {background-size:contain; background-repeat:no-repeat}
</style>
-->

<!-- location map -->

<!-- / location map -->
        
         <!-- / Property Map -->



        <!-- mobile landscape agnet button only -->
	<div class="mobile_landscape_btn_only mb-5">
		<div id="RespBtn2">
			<div class="row no-gutters">
				<div class="col">
					<div class="agent_signaturev2 agent_signaturev2__large">
	<a href="/patrick-burbridge/agent_STEVENB">
    		<div class="agent_signaturev2__large__photo lazy" title="View  Patrick Burbridge's profile"  data-src='https://pics.harstatic.com/agent/476135.jpg?ts=2020-08-14T17:20:00' ></div>
    	</a>
	<div class="agent_signaturev2__info">
		
		<div title="View Patrick Burbridge's profile" aria-label="View Patrick Burbridge's profile">
			<a class="agent_signaturev2__info__agent_name" href="https://www.har.com/patrick-burbridge/agent_STEVENB">
				Patrick Burbridge
								<img alt="Platinum" style="width:51px;" src="https://content.harstatic.com/media/icons/label-platinum.svg">
							</a>
		</div>
										<div class="agent_signaturev2__info__broker_name" title="View CitiQuest Properties's page" aria-label="View CitiQuest Properties's page" style="max-width:220px;">
				<a class="agent_signaturev2__info__broker_name" href="/citiquest-properties/broker_SIDE37" title="View CitiQuest Properties's page" aria-label="View CitiQuest Properties's page">
					CitiQuest Properties
				</a>
			</div>
						</div>
</div>				</div>
				<div class="col-auto text-right">
					<div class="d-flex  align-self-center">
						<div class="align-self-center pr-3 border-right">
							<!-- phone icon-->
<a data-target='target_phone_landscape-1' id="source_phone_presentedby-landscape-1" href="javascript:void(0);"  class="circle_btn font_weight--semi_bold hideAgentPhone" onclick="showPhone('source_phone_presentedby-landscape-1','(*************','STEVENB','agent','','9664623')">
  <div><img alt="" src="https://content.harstatic.com/media/icons/phone_blue_small.svg" width="22" height="22" /></div><span class="font_weight--semi_bold">Call</span>
</a>


<!--target control -->
<a href="tel:(*************" id='target_phone_landscape-1' class='color_auxiliary' style="display:none;">(*************</a>
<!--source control -->

							<a href="javascript:void(0);" class="circle_btn  attachedLead-for-STEVENB" data-toggle="modal" data-target="">
								<div><img src="https://content.harstatic.com/media/icons/email.svg" alt="Email">
								</div><span class="font_weight--semi_bold">Email</span>
							</a>
						</div>
						<div class=" align-self-center pl-4">
													<a onclick='window.open("/schedule_showing/74257223")' href="javascript:void(0);" class="font_weight--bold mr-3 d-block font_size--medium">Take a
								tour -></a>
														</div>
					</div>
				</div>
			</div>
		</div>
		<!-- <div class="border-top text-center pt-2"><a href="#">Switch to <span class="font_weight--bold">MLS Subscriber View</span></a></div>-->
	</div>
	<!-- mobile landscape agnet button only -->    

    <!-- Attributes -->
    <style type="text/css">
    /* Spacing when the AD is present on layout */
    .with-ad {
        padding-top: 10px !important;
        margin-top: 5px !important;
    }

    /* Spacing when the AD is not present on layout */
    .without-ad {
        padding-top: 0px !important;
        margin-top: 0px !important;
    }
</style>



<a name="E_block"></a>
<div id="EData" class="mb-md-4 mb-3 border-bottom border-color--cloudy-sky-light
     without-ad">
        <h2 tabindex="0">
  
                                                Exterior
                                                                                    
    </h2>
    <div class="row">
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Roof
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Other
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Foundation
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Other
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Private Pool
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    No
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Exterior Type
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Unknown
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Lot Description
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Subdivision Lot
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Water Sewer
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Public Sewer, Public Water
                                                            </div>
        </div>
                    </div>
        </div>


<a name="I_block"></a>
<div id="IData" class="mb-md-4 mb-3 border-bottom border-color--cloudy-sky-light
     without-ad">
        <h2 tabindex="0">
  
                                    Interior
                                                                                                
    </h2>
    <div class="row">
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Cooling
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Other Cooling
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Heating
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Other Heating
                                                            </div>
        </div>
                    </div>
        </div>
    
   
     <!-- /Attributes -->
	

             <!-- Lot components -->
    <span class="anchor" id="propertyLotInfo"></span>
        <div class="mb-md-4 pb-md-5 mb-3 pb-3 border-bottom border-color--cloudy-sky-light lot-info-wrapper">
       <h2 tabindex="0">See the lot information</h2>
                                <!-- lot information 
$lotsrc = ($this->LotSizeSrc <> '') ?  d__($this->LotSizeSrc) : d__('Appraisal District');-->
      <div class="lot-info-container">
        <div class="row">
            <div class="col-md-auto col-12  d-md-flex">
                <div class="font_weight--bold font_size--small_extra"> Lot Size</div>
                <div class="font_size--small   ml-md-2"> 
                                        <span class="ftin">26,933 Sqft.</span>
                    <span class="metric" style="display:none;">2502 (m²).</span>/Appraisal District
                                    </div>
            </div>
            <div class="col-md col-12 text-md-right">
                <a href="#" class="font_weight--bold font_size--large mr-4 openStreetView">Street View</a>
                <a href="#" class="font_weight--bold font_size--large mr-4 openMapView">Map View</a>
                <a href="#" class="font_weight--bold font_size--large openSatelliteView">Satellite View</a>
            </div>
        </div>
        <div class="mb-3 mt-4">
            <div class="image223017927" style="height:360px; width:100%; background-color:rgb(237, 240, 244); position:relative"><!-- background:url('https://content.harstatic.com/media/temprary/googlemap_full.png') no-repeat;background-size:cover;background-position: center center; -->
                <img class="parcels" style="position:absolute; left:0; right:0" />
                <img class="measurement" style="position:absolute; left:0; right:0" />
                <img class="loader" alt="loading" src="https://content.harstatic.com/img/common/loading1.gif" width="46" height="47" />
            </div>
            <!-- this will use for map for
                                <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d27709.72798193808!2d-95.37375801399801!3d29.756931492854306!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x8640b8b4488d8501%3A0xca0d02def365053b!2sHouston%2C%20TX%2C%20USA!5e0!3m2!1sen!2s!4v1605867384995!5m2!1sen!2s" width="100%" height="360" frameborder="0" style="border:0;" allowfullscreen="" aria-hidden="false" tabindex="0"></iframe>
                            -->
        </div>
        <div class="font_size--small_extra color_slate_light mb-3">
            <span class="font_weight--bold">Disclaimer:</span> Lot configuration and dimensions are estimates, not based on personal knowledge and come from a third party (Digital Map Products); therefore, you should not rely on the estimates and perform independent confirmation as to their accuracy
        </div>
    </div>
     <!-- / lot information -->

<script type="text/javascript">
    function hideLotInformation(){
        $('.lot-info-container,.lot-info-wrapper').hide();
        console.log("Lot info Hidden due to Low Size");
    }

    function isLotIntoView(elem){
        if (elem.length == 0) return false;
        var docViewTop = jQuery(window).scrollTop();
        var docViewBottom = docViewTop + jQuery(window).height();
        var elemTop = jQuery(elem).offset().top;
        var elemBottom = elemTop + jQuery(elem).height();
            //console.log(elemTop, elemBottom, docViewTop, docViewBottom, (elemBottom >= docViewTop), (elemTop <= docViewBottom), (elemBottom <= docViewBottom), (elemTop >= docViewTop));
        return ((elemBottom + 40 >= docViewTop) && (elemTop <= docViewBottom) && (elemBottom <= docViewBottom) && (elemTop >= docViewTop));
    }

    function loadLotInfo() {
        harload('harmapLoader', 'ready').then(function() {    
            var latitude = 29.797257000000000;
        var longitude = -95.357419000000000;
        if(!latitude || !longitude) { throw new Error('Location not available for lot information.'); }
        var componentId = 'image223017927';
        var component = $('.' + componentId).removeClass(componentId);
        var parcelsImage = component.find('.parcels');
        var measurementImage = component.find('.measurement');
        var loaderImage = component.find('.loader');
        var location = { latitude:latitude, longitude:longitude };
        var params = { location:location, width:component.width(), height:component.height() };
            HARMap.load()
            .then(function(module) { return module.services.lotBoundariesImages(params); })
            .then(function(result) { return Promise.all([ result.parcelsImage.loadImage(), result.measurementImage.loadImage() ]); })
            .then(function(images) {
                var parcels = images[0];
                var measurements = images[1];
                loaderImage.remove();
                parcelsImage.prop('src', parcels.src);
                measurementImage.prop('src', measurements.src);

                parcelsImage.load(function() {
                    if(parcelsImage.prop('naturalWidth') <= 1 || parcelsImage.prop('naturalHeight') <= 1) {
                        hideLotInformation();
                    }
                });

                measurementImage.load(function() {
                    if(measurementImage.prop('naturalWidth') <= 1 || measurementImage.prop('naturalHeight') <= 1) {
                        hideLotInformation();
                    }
                });
            }, function(e) {
                console.log(e)    
                $('.lot-info-container,.lot-info-wrapper').hide();
                console.log("Lot info Hidden");
            });
        });
    }


</script>
                        </div>
    <!-- /Lot components -->
             
    <!-- Climate risk -->
        <!-- /Climate risk -->
    
            <!-- 2nd Section Attributes -->
        <div>
            <style type="text/css">
    /* Spacing when the AD is present on layout */
    .with-ad {
        padding-top: 10px !important;
        margin-top: 5px !important;
    }

    /* Spacing when the AD is not present on layout */
    .without-ad {
        padding-top: 0px !important;
        margin-top: 0px !important;
    }
</style>



<a name="A_block"></a>
<div id="AData" class="mb-md-4 mb-3 border-bottom border-color--cloudy-sky-light
     without-ad">
        <h2 tabindex="0">
  
                                                            Extra details you may need to know
                                                                        
    </h2>
    <div class="row">
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Dwelling Type
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Free Standing
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                HOA Mandatory
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    No
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                List Type
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Exclusive Right to Sell/Lease
                                                            </div>
        </div>
                    </div>
        <!--<p class="font_weight--semi_bold font_size--small_extra">*Disclaimer: Listing broker&#039;s offer of compensation is made only to participants of the MLS where the listing is filed.</p>-->
        </div>


<a name="F_block"></a>
<div id="FData" class="mb-md-4 mb-3 border-bottom border-color--cloudy-sky-light
     without-ad">
        <h2 tabindex="0">
  
                                                                        Financial information
                                                            
    </h2>
    <div class="row">
                        <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Financing Considered
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Cash Sale, Conventional, Investor
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Other Fees
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    No
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Ownership
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    Full Ownership
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Maintenance Fee
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    No
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Taxes w/o Exemp
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    $17,471/2024
                                                            </div>
        </div>
                                <div class="col-md-4 col-6 mb-4">
            <div class="font_weight--bold font_size--small_extra">

                                Tax Rate
                            </div>
            <div class="font_size--large text-break">
                                    
                             
                    2.0924
                                                            </div>
        </div>
                    </div>
        </div>
        </div>
        <!-- 2nd Section Attributes -->
    
        <!-- Report Issue  -->
            <div class="mb-4 pb-5 border-bottom border-color--cloudy-sky-light">   
                <div class="row">
                                            <div class="col-auto ml-md-auto">
                            <a href="javascript:void(0)" onclick="reportIssue()" class="font_weight--bold mb-4 pt-md-2">Report problem</a>
                        </div>
                                    </div>
            </div>    
    <!-- / Report Issue   -->
        
            <!-- Subdivision Facts -->
        <span class="anchor" id="subDivisonInfo"></span>
                                    
        <!-- / Subdivision Facts -->
        <!-- Neighborhood Section -->
    <div id="neighborhoodSectionInfo" class="mb-4 border-bottom border-color--cement_light">
        <div class="row load-neighborhood-section">
                            <div id="loadNeighborhoodSection" class="endpage"></div>
                    </div>

        <div id="NeighborhoodSectionLoader" style="display:none">
            <center><img alt="loading" src="https://content.harstatic.com/img/common/loading1.gif"></center>
        </div>

        <div class="neighborhood_section"></div>
    </div>
        
        <!-- Traffic Report -->
                                  <span class="anchor" id="analyticBlock"></span>
              
            <div class=" ">
                <div data-dt='04/11/25 9:25:35' id="TrafficReport" class="lazy" data-loader="ajax" data-src="/api/getTrafficReport/74257223?src=traffic-report&url=https://www.har.com/homedetail/4017-robertson-st-houston-tx-77009/3011563" data-contentname="TrafficReport">
                    <div class="mb-4 pb-5 border-bottom border-color--cement_light">
		<h2 tabindex="0">Get insights from analytics</h2>
			<div class="border border_radius--default p-3">
				<div class="row mb-3 mb-md-5">
							<div class="col-12 col-sm-4">
								<div class="border p-sm-3 p-2 mb-3 text-center border_radius--default">
									<div class="font_size--large_extra_extra font_weight--semi_bold">-----</div>
									<div class="font_size--medium">----------</div>
								</div>
							</div>
							<div class="col-12 col-sm-4">
								<div class="border p-sm-3 p-2 mb-3 text-center border_radius--default">
									<div class="font_size--large_extra_extra font_weight--semi_bold">-----</div>
									<div class="font_size--medium">----------</div>
								</div>
							</div>
							<div class="col-12 col-sm-4">
								<div class="border p-sm-3 p-2 mb-3 text-center border_radius--default">
									<div class="font_size--large_extra_extra font_weight--semi_bold">-----</div>
									<div class="font_size--medium">----------</div>
								</div>
							</div>
				</div>
						
				<div id="TrafficDetails" class="mb-5">
					<h3 tabindex="0">Total views</h3>
					<center><img class="img-fluid" style="width: 90%; height: 300px;" src="/images/loading_pulse.svg"></center>
				</div>
						
			</div>
	</div>
                </div>
            </div>
                        <!-- Traffic Report -->

                                 <!-- Energy Ogre -->
             <div class="mb-4 pb-5 border-bottom border-color--cloudy-sky-light">
                <!-- Estimated Electricity  -->
<div >
<h2 tabindex="0">Check out your estimated electricity cost</h2>
      
    <div class="est_cost">
        <div class="ec_top">
            <div class="p-md-5 p-3">
                <div class="row mb-4">
                    <div class="col-12 font_size--large">
                        <div class="font_weight--bold">Analysis for</div>
                        4017 Robertson St
                    </div>
                </div>

                <ul class="nav nav-tabs nav-tabs--underline" id="myTab" role="tablist">
                                <li class="nav-item pb-4">
                    <a class="nav-link active" id="MonthlyCost-tab" data-toggle="tab" data-target="#MonthlyCost" href="javascript:void(0);" role="tab" aria-controls="home" aria-selected="true">Monthly Cost</a>
                </li>
                                                <li class="nav-item">
                    <a class="nav-link " id="yearlyview-tab" data-toggle="tab"  data-target="#YearTrends" href="javascript:void(0);" role="tab" aria-controls="profile" aria-selected="false">3-Year Trends</a>
                </li>
                
                </ul>
                <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active" id="MonthlyCost" role="tabpanel" aria-labelledby="MonthlyCost">
                    <div id="EvergyOgre" style="min-height:300px;">
                    </div>
                </div>
                <div class="tab-pane fade" id="YearTrends" role="tabpanel" aria-labelledby="YearTrends">
                    <div id="EvergyOgreYearly" style="min-height:300px;" >
                    </div>
                </div>
                </div>
            </div>
                        <div class="bg_color_cloudy_sky_light pl-md-5 pl-3 pr-md-5 pr-3 pt-md-4 pt-3 pb-m-4 pb-3 font_size--medium">
                  An Energy Ogre member at this location would <span class="font_weight--bold">SAVE $171/year</span>.  <a class="font_weight--bold"  target="_blank" href="https://www.energyogre.com/how-energy-ogre-works?utm_source=website&utm_medium=affiliate&utm_term=har-listing-find-out-more&utm_content=har-listing-find-out-more&ref=16yy8">Find out more</a>
            </div>
                    </div>
    </div>
    <div class="bg_color_har_blue_dark p-5 color_snow_white">
        <div class="pb-4">Wondering if you&#039;re overpaying for electricity at your current home? Check now!</div>

        <div class="row small-gutters pb-3">
            <div class="col-12 col-md">
                <label class="color_snow_white" for="ZipCode">Your Zip Code</label>
                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                    <div class="input-group-text bg_color_cloudy_sky border-0"><img width="16" height="16" src="https://content.harstatic.com/resource_2019/imgs/icons/icon-location_charcole.svg"></div>
                    </div>
                    <input type="text" class="form-control border-0" id="zipcode" placeholder="Your Zip Code" style="height:40px;">
                </div>
            </div>
            <div class="col-12 col-md">
                <label class="color_snow_white" for="DueDate">Due Date</label>
                <div class="input-group mb-3" id='datetimepicker1'>
                    <div class="input-group-prepend">
                    <div class="input-group-text bg_color_cloudy_sky border-0"><img width="16" height="16" src="https://content.harstatic.com/resource_2019/imgs/icons/icon-calendar_charcole.svg"></div>
                    </div>
                    <input type="text" class="form-control border-0" id="duedate" placeholder="Due Date" value="04/11/2025" style="height:40px;">
					   <span class="input-group-addon">
					   <span class="glyphicon glyphicon-calendar"></span>
                </div>
            </div>
            <div class="col-12  col-md ">
                <label class="color_snow_white" for="AmountDue">Amount Due</label>
                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                    <div class="input-group-text bg_color_cloudy_sky border-0"><img width="16" height="16" src="https://content.harstatic.com/resource_2019/imgs/icons/icon-dollar-sign_charcole.svg"></div>
                    </div>
                    <input type="text" class="form-control border-0" id="energyamount" placeholder="Amount Due" style="height:40px;">
                </div>
            </div>
            <div class="col-12  col-md ">
                <label class="color_snow_white" for="TotalUsage">Total Usage</label>
                <div class="input-group mb-3">
                    <div class="input-group-prepend">
                    <div class="input-group-text bg_color_cloudy_sky border-0"><img width="16" height="16" src="https://content.harstatic.com/resource_2019/imgs/icons/icon-dashboard_charcole.svg"></div>
                    </div>
                    <input type="text" class="form-control border-0" id="totalusage" placeholder="Total Usage" style="height:40px;">
                </div>
            </div>
            <div class="col-12  col-md ">
                <label class="color_snow_white" for="ElectricHeat">Electric Heat</label>
                <select id="heatoption"   class="form-control border-0">
                    <option value=0>No</option>
                    <option value=1>Yes</option>
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col d-md-block d-none">
                <a href="https://www.energyogre.com/?utm_source=website&utm_medium=affiliate&utm_term=har-listing-power-check-eo-button&utm_content=har-listing-power-check-eo-button&ref=16yy8" target="_blank">
                    <img class="w-100 lazy" style="max-width:188px;height:auto" data-src="https://content.harstatic.com/media/temprary/<EMAIL>" widht="188" height="51">
                </a>    
            </div>
            <div class="col-md-auto col-12 ml-auto">
                <button class="btn color_snow_white btn--medium col-md-auto col-12 ml-auto" type="button" tabindex="0" style="background-color: #8cc63f;" id="powerchecker"><img width="16" height="16" src="https://content.harstatic.com/resource_2019/imgs/icons/icon-bolt-white.svg"> Power Check</button>
            </div> 
            <div class="col-12 d-md-none mt-4">
                <a href="https://www.energyogre.com/?utm_source=website&utm_medium=affiliate&utm_term=har-listing-power-check-eo-button&utm_content=har-listing-power-check-eo-button&ref=16yy8" target="_blank">
                    <img class="w-100 lazy" data-src="https://content.harstatic.com/media/temprary/<EMAIL>" widht="188" height="auto">
                </a>    
            </div>   
        </div>
      
        
    </div>
</div>
<!-- / Estimated Electricity  -->
            </div>
            <!--/ Energy Ogre -->
                    
                                                     <!-- Property Tax Info -->
                        <div class="mb-4 pb-5 border-bottom border-color--cloudy-sky-light">
               <span class="anchor" id="TaxInfo"></span>
                                                                                       
                <div id="" class="lazy" data-loader="ajax" data-src="/api/getTaxInfo/3011563?lid=9664623&amp;template=listchart" data-contentname="TaxInfo">
                    <h2 tabindex="0">Know the property tax history and market value</h2> 
	<div class="text-center mb-5">
         <center><img class="img-fluid" style="width: 90%; height: 300px;" src="/images/loading_pulse.svg"></center>
    </div>

    <h3 tabindex="0">Cost/Sqft based on tax value</h3>
    <div class="table_wrapper">
        <table class="table table--medium">
            <!-- Table description for accessibility purposes -->
            <caption>------------------------------</caption>
            <thead>
                <tr>
                    <th scope="col">----------</th>
                    <th scope="col">----------</th>
                    <th scope="col">----------</th>
                    <th scope="col">----------</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td scope="row">----------</td>
                    <td>----------</td>
                    <td>----------</td>
                    <td class="font_weight--semi_bold color_sold">----------</td>
                </tr>
                <tr>
                    <td scope="row">----------</td>
                    <td>----------</td>
                    <td>----------</td>
                    <td class="font_weight--semi_bold color_sold">----------</td>
                </tr>
                <tr>
                    <td scope="row">----------</td>
                    <td>----------</td>
                    <td>----------</td>
                    <td class="font_weight--semi_bold color_sold">----------</td>
                </tr>
                <tr>
                    <td scope="row">----------</td>
                    <td>----------</td>
                    <td>----------</td>
                    <td class="font_weight--semi_bold color_sold">----------</td>
                </tr>
                <tr>
                    <td scope="row">----------</td>
                    <td>----------</td>
                    <td>----------</td>
                    <td class="font_weight--semi_bold color_sold">----------</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="row mt-5 pt-4 mb-5">
        <div class="col-12 col-md-6 mb-md-0">
            <h3 class="mt-3 mb-2" tabindex="0">-------------</h3>
            <table class="table table--medium">
                <!-- Table description for accessibility purposes -->
                <caption>-------------</caption>
                <tbody>
                    <tr>
                        <td scope="row">-------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                    <tr>
                        <td scope="row">-------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                    <tr>
                        <td scope="row">--------------------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                    <tr>
                        <td scope="row">--------------------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                    <tr>
                        <td scope="row">-------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                </tbody>
            </table>
        </div>
        <div class="col-12 col-md-6 mb-md-0">
            <h3 class="mb-5" tabindex="0">-------------</h3>
            <table class="table table--medium">
                <!-- Table description for accessibility purposes -->
                <caption>-------------</caption>
                <tbody>
                    <tr>
                        <td scope="row">-------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                    <tr>
                        <td scope="row">-------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                    <tr>
                        <td scope="row">-------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                    <tr>
                        <td scope="row">-------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                    <tr>
                        <td scope="row">-------------</td>
                        <td class="text-right">-------------</td>
                    </tr>

                </tbody>
            </table>
        </div>
    </div>
                </div>
                <div id="readMore" class="mt-4 bg_color_cement_light text-center p-2 font_size--medium d-none"><a class="color_auxiliary font_weight--bold" href="javascript:void(0);" onclick="document.getElementById('TaxInfo').style.maxHeight='none'; document.getElementById('TaxInfo').style.removeProperty('overflow'); document.getElementById('readMore').style.display='none';">read more about property tax ↓</a>
                </div>
            </div>
              <!-- / Property Tax -->
                                        
                 <!-- Down Payment  -->
		
		                <!-- ver.20.213 -->
                    <span class="anchor" id="ddrBlock"></span>
             
																																																					        <!-- /Down Payment  -->

                

     <!-- Estimated Mortgage Tax -->
                             <div class="mb-4 pb-5 border-bottom border-color--cloudy-sky-light">

                                                                                                <span class="anchor" id="calculatorInfo"></span>
                        <div class="lazy" data-loader="ajax" data-src="/api/getCalculator/3011563?lid=9664623" data-contentname="calculatorInfo">
                            <div class="">
<h2 id="mort_loader_title" class="mb-3">Estimate your mortgage payments</h2>
<div class="pb-4 font_size--large">Create and estimate your monthly mortgage payment and property taxes based on past rates.</div>
        <div class="row mb-md-0 mb-2 pb-md-0 pb-2 justify-content-center h-100">
            <div class="col-12 col-md-5 pb-5 pb-md-0">
                <div style="max-width:340px;">
                        <p class="hero_child_page__tagline"> Estimated Monthly Payments </p>

                        <label for="LoanAmount">LIST PRICE</label>
                        <input type="text" class="form-control mb-3" name="price" value="-----">
    
                        <h5 ><b>DOWN PAYMENT</b></h5>
                        <div class="row small-gutters">
                            <div class="col-auto">
                                <label for="NewInterestRate">Amount</label>
                                <input type="text" class="form-control mb-2" name="downpay" value="------">
                            </div>
                            <div class="col">
                                <label for="Points">Percentage</label>
                                <input type="text" class="form-control mb-3" name="downpayperc" value="--">
                            </div>
                        </div>
    
                        <h5><b>Loan Term</b></h5>
                        <div class="row small-gutters">
                            <div class="col-auto">
                                <label for="NewInterestRate">Months</label>
                                <input type="text" class="form-control mb-2" name="termMonths" value="----" maxlength="3" disabled="">
                            </div>
                            <div class="col">
                                <label for="Points">Years</label>
                                <input type="text" class="form-control mb-2" name="termYears" value="----" maxlength="2">
                            </div>
                        </div>
    
                        <label for="intYear">Annual Interest Rate</label>
                        <input type="text" class="form-control mb-2" name="intYear" value="----">
    
    
                        <label for="payMonth">Estimated Monthly Principal &amp; Interest</label>
                        <div class="font_size--large_extra_extra font_weight--semi_bold mb-3 payMonth_change_label">
                            $<span id="monthlyPay">-----</span>
                        </div>
                        <input type="text" class="form-control mb-5 hide d-none" name="payMonth" value="2841.2804931494"->
    
                        <label for="LoanAmount" class="">Estimated Monthly Property Tax </label>
                        <div class="font_size--large_extra_extra font_weight--semi_bold mb-3">
                            <span class="calc_price">$<span id="monthlypTax">-----</span> </span>
                        </div>
                        <div class="pb-4">
                            <a class="collapsed collapseable_link font_weight--bold font_size--large text-decoration-none" data-toggle="collapse" href="#collapseExample" role="button" aria-expanded="false" aria-controls="collapseExample">More Options</a>    
                        
                            <div class="collapse" id="collapseExample">
                                <label for="LoanAmount">Monthly Homeowner&#039;s Insurance </label>
                                <input type="number" class="form-control mb-2" id="home_insurance" name="home_insurance" value="0" maxlength="5">
                        
                                <label for="pmi">Monthly PMI (Private Mortgage Insurance)</label>
                                <input type="number" class="form-control mb-2" name="pmi" id="pmi" value="0" maxlength="5">

                                <label for="hoa_fees">Monthly HOA fees</label>
                                <input type="number" class="form-control mb-3" id="hoa_fees" name="hoa_fees" value="0" maxlength="5">
                            </div>
                        </div>
                        <button type="button"  class="btn btn--primary btn--large">Calculate</button>
                                                        <a class="btn btn--ordinary btn--large">Full Page</a>

                </div>
            </div>
            <div class="col-12 col-md-7">
                <div class="position-sticky" style="top:25px;">
                    <div class="text-right font_size--small_extra color_slate pb-3 d-none">Presented by TimeValue Software
                        ©2021</div>
                    <div class="border_radius--default depth--above_all depth border">
                        <div class="p-md-5 p-2">
                            <div class=" donutCell">
                                <div style="width:100%;height: 400px;display: flex; justify-content: center;" class="donutDiv">
                                    <div id="donutchart" style="width:100%;height: 400px;display: flex; justify-content: center;" class="donutDiv"><div style="position: relative;"><div dir="ltr" style="position: relative; width: 340px; height: 400px;"><div aria-label="A chart." style="position: absolute; left: 0px; top: 0px; width: 100%; height: 100%;"><svg width="340" height="400" aria-label="A chart." style="overflow: hidden;"><defs id="_ABSTRACT_RENDERER_ID_4"></defs><rect x="0" y="0" width="340" height="400" stroke="none" stroke-width="0" fill="#ffffff"></rect><g><rect x="51" y="31" width="169" height="11" stroke="none" stroke-width="0" fill-opacity="0" fill="#ffffff"></rect><g column-id="Principal &amp; Interest"><rect x="51" y="31" width="107" height="11" stroke="none" stroke-width="0" fill-opacity="0" fill="#ffffff"></rect><g><text text-anchor="start" x="66" y="40.35" font-family="Arial" font-size="11" stroke="none" stroke-width="0" fill="#222222">Principal &amp; Interest</text></g><circle cx="56.5" cy="36.5" r="5.5" stroke="none" stroke-width="0" fill="#3366cc"></circle></g><g column-id="Taxes"><rect x="176" y="31" width="44" height="11" stroke="none" stroke-width="0" fill-opacity="0" fill="#ffffff"></rect><g><text text-anchor="start" x="191" y="40.35" font-family="Arial" font-size="11" stroke="none" stroke-width="0" fill="#222222">Taxes</text></g><circle cx="181.5" cy="36.5" r="5.5" stroke="none" stroke-width="0" fill="#dc3912"></circle></g></g><g><path d="M102.6986073847714,223.84287214349087L57.83101230795232,239.73812023915144A119,119,0,0,1,170,81L170,128.60000000000002A71.39999999999999,71.39999999999999,0,0,0,102.6986073847714,223.84287214349087" stroke="#ffffff" stroke-width="1" fill="#dc3912"></path><text text-anchor="start" x="79.3036529909075" y="150.7140669963692" font-family="Arial" font-size="11" stroke="none" stroke-width="0" fill="#ffffff">30.4%</text></g><g><path d="M170,128.60000000000002L170,81A119,119,0,1,1,57.83101230795232,239.73812023915144L102.6986073847714,223.84287214349087A71.39999999999999,71.39999999999999,0,1,0,170,128.60000000000002" stroke="#ffffff" stroke-width="1" fill="#3366cc"></path><text text-anchor="start" x="229.6963470090925" y="256.9859330036308" font-family="Arial" font-size="11" stroke="none" stroke-width="0" fill="#ffffff">69.6%</text></g><g></g></svg><div aria-label="A tabular representation of the data in the chart." style="position: absolute; left: -10000px; top: auto; width: 1px; height: 1px; overflow: hidden;"><table><thead><tr><th>Listing Tax</th><th>Numbers</th></tr></thead><tbody><tr><td>Principal &amp; Interest</td><td>2,841</td></tr><tr><td>Taxes</td><td>1,242</td></tr></tbody></table></div></div></div><div aria-hidden="true" style="display: none; position: absolute; top: 410px; left: 350px; white-space: nowrap; font-family: Arial; font-size: 11px;">Taxes</div><div></div></div></div>

                                    <div class="centerLabel monthlyTotal">
                                       --------
                                    </div>
                                </div>
                                <center><a class="font_weight--semi_bold text-center d-none"> -------- --------</a>
                                </center>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</div>                        </div>
            </div>
               <!-- / Estimated Mortgage Tax -->

        <!-- Market Update Video -->
            <div class="lazy" data-loader="ajax" data-src="/api/getMarketUpdate/3011563" data-contentname="market-update" id="mupdate">
                <div class="mb-5 pb-5border-bottom border-color--cement_light">
    <h2 tabindex="0">Watch market updates</h2>

    <h3 tabindex="0">--------------</h3> 
					<div class="embed-responsive embed-responsive-16by9 mb-4">					  
					  ----------------------------------------------------				  
					</div>
			
    <a class="font_weight--bold font_size--medium">----------</a>
</div>            </div>
        <!-- / Market Update Video -->


                <!-- ready to sell your home -->
        <div class="mb-4 pb-5 border-bottom border-color--cloudy-sky-light">
            <div class="sellyourhome border_radius--default" style="background-image:url('https://content.harstatic.com/media/icons/illustration-ready-to-sell-cta.svg');">
                <div class="font_size--large_extra_extra color_har_blue_dark font_weight--bold mb-3">Ready to sell your home?</div>
                <div class="font_size--medium color_auxiliary mb-4">If you like this property and are also considering selling your current home now, you may find out how. Request your home selling analysis report featuring Recently Sold, Market Analysis, Home Valuations, and Market Update.</div>
                <a href="/sell_your_home?agent=STEVENB" class="btn btn--ordinary btn--large">Start selling your home</a>
            </div>
        </div>
        <!-- / ready to sell your home -->
        
        <!-- Assign Schools -->
                                <span class="anchor" id="schoolInfo2"></span>
            <div>
                                <!-- Schools -->
        <div id="schoolInfo" class="mt-4 mb-4 pb-5 border-bottom border-color--cement_light">
        <h2 tabindex="0" class="mb-2">Explore school information</h2>
        <p class="mb-4 font_size--large">Check out assigned and nearby schools along with their detailed ratings and essential information to help you make informed decisions.</p>

        <h3 class="pb-2" tabindex="0">Assigned schools</h3>

        <div class="row load-assign-school">
                        <div id="loadAssignSchool" class="endpage"></div>
            <div id="LoaderAssignImage" style="display:none">
                <center><img alt="loading" src="https://content.harstatic.com/img/common/loading1.gif"></center>
            </div>
                       
                    </div>

        <a href="javascript:void(0);" class="text-decoration-none expendNew" data-container="schools_container">
            <h3 tabindex="0">View nearby schools <img class="ml-2 rotate_icon" width="12" height="12" style="width:12px;" src="https://content.harstatic.com/media/icons/arrow-darkblue-up.svg" alt="view nearby schools"></h3>
        </a>
        <div id="NearByLoader" style="display:none">
            <center><img alt="loading" src="https://content.harstatic.com/img/common/loading1.gif"></center>
        </div>

        <div class="nearby_school"></div>
        <div class="disclaimer color_auxiliary">
            <div class="font_weight--bold font_size--medium">Notice &amp; Disclaimer</div>
            <div class="font_size--medium">        				
                School information is computer generated and may not be accurate or current. Buyer must independently verify and confirm enrollment. Please contact the school district to determine the schools to which this property is zoned.
            </div>
        </div>
        
    </div> <!-- / Schools -->

                                    <div class="mb-5 d-none">
                                    </div>
            </div>
            <!--/ Assign Schools -->

                            <!-- sound score -->
                <div class="mb-4 pb-5 border-bottom  border-color--cement_light">
                    <h2 tabindex="0">Noise factors</h2>
                    <div id="soundScoreComponent" class="lazy" data-loader="api_mixed" data-src="/api/getSoundScore/3011563">
                        <span style="text-align:center" class="w-100"><img alt="loading" src="https://content.harstatic.com/img/common/loading1.gif"></span>
                    </div>
                </div>
                <!-- / sound score -->
            

        
                                    <!-- Listing Broker row -->
            <span class="anchor" id="listingBroker"></span>
            <div class="mb-4 pb-5 border-bottom border-color--cloudy-sky-light">
                <h2 tabindex="0">Listing broker</h2>
                <div class="row">
                                        <div class="col-auto">
                        <a class="d-block bg_color_snow_white p-3 border_radius--default overflow-hidden border" style="max-width:148px;max-height:148px;" href="/citiquest-properties/broker_SIDE37"><img class="img-fluid" src="https://content.harstatic.com/media/icons/broker_building.svg" alt="Office logo"></a>
                    </div>
                    <div class="col pr-md-5">
                        <h3 class="color_carbon" tabindex="0">
                            <a style="color:#000" href="/citiquest-properties/broker_SIDE37">CitiQuest Properties</a>
                        </h3>
                        <div class="pb-2 font_size--medium">
                            <!--show phone component-->
                            <!-- phone icon-->
<img alt="" src="https://content.harstatic.com/media/icons/map_popup/phone.svg"  class='' style="width:16px;height:16px;" />
<a data-target='target_phone_listing_broker' id="source_phone_listing_broker" href="javascript:void(0);" data-track-type=OFFICE_PHONE_CLICKED class="doListhubTracking" onclick="showPhone('source_phone_listing_broker','************','SIDE37','office','','9664623')">
  Click to view phone
</a>


<!--target control -->
<a href="tel:************" id='target_phone_listing_broker' class='color_carbon' style="display:none;">************</a>
<!--source control -->
                        </div>
                                                <div class="pb-2 font_size--medium"><img class="mr-1" style="width:16px;" width="16" height="16" src="https://content.harstatic.com/media/icons/email_blue_small.svg" alt="Email Broker"><a class="doListhubTracking" data-track-type="OFFICE_EMAIL_CLICKED" href="javascript:void(0);" data-toggle="modal" data-target="#ContactOffice"> Email Broker</a></div>
                                            </div>
                    <div class="col-md-auto col-12 border-md-left font_size--medium pl-4 pt-4 pt-md-0" style="min-width:210px;">
                        <a class="d-block pb-2" href="/citiquest-properties/broker_SIDE37">Office profile</a>
                                                <a class="d-block pb-2" href="/citiquest-properties/office_website_side37" target="_blank">Office website</a>
                                                <a class="d-block pb-2" href="/SIDE37/listings_for_sale_from_citiquest-properties">Office listings</a>
                        <a class="d-block pb-2" href="/SIDE37/sold_by_citiquest-properties">Office transaction</a>
                    </div>
                </div>
                            </div>
                        			            <!-- / Listing Broker row -->

                        <!-- Listing Source -->
               <!-- Listing Source row -->


<div class="mb-4 pb-5 border-bottom border-color--cement_light">
    <h2 tabindex="0">Information source</h2>
    <div class="row ">
        <div class="col-auto">
                        <a class="d-block bg_color_snow_white p-3 border_radius--default overflow-hidden border" target="_blank" href="https://www.har.com/go/to-url-5?url=https%3A%2F%2Fwww.har.com">
                                                <img style="max-width:140px; max-height:100px;" class="img-fluid" src="https://content.harstatic.com/ppp/applogo/har.png" alt="">
                                            </a>
                    </div>
        <div class="col-12 col-md-auto pr-5 pt-4 pt-md-0 align-self-center">
            <div class="font_weight--bold font_size--medium mb-1">Houston Association of REALTORS</div>

                        <div class="font_size--small color_slate_light">Source last updated Apr 11, 2025 at 09:18 AM</div>
                                    <div class="font_size--small color_slate_light">Listing last updated Apr 07, 2025 at 01:57 PM</div>
            
                    </div>
    </div>
</div>
<!-- / Listing Source row -->
            <!-- Listing Source -->

            
            
                
                
            
			            <!--  Listings for Sale Nearby -->
            <div class="mb-4 pb-5 border-bottom border-color--cloudy-sky-light">   
                <div class="lazy" data-loader="ajax" data-src="/api/similar_listing?type=sale&amp;mlsnum=74257223" data-contentname="slider_similar_listings_for_sale">
                    <div class="container mt-5 pt-5 mb-4">
                        <h2 tabindex="0">View nearby similar homes for sale</h2>
                        <div class="property_loader slider responsive-hidearrows new-listings">
                            <div class="property_loader slider responsive-hidearrows">
     <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 
	
</div>                        </div>
                    </div>
                </div>
            </div>    
            <!--  /Listings for Sale Nearby -->

            <!-- Listings for Rent Nearby -->
            <div class="lazy" data-loader="ajax" data-src="/api/similar_listing?type=rent&amp;mlsnum=74257223" data-contentname="slider_similar_listings_for_rent">
                <div class="container mt-5 pt-5 mb-4">
                    <h2 tabindex="0">View nearby similar homes for rent</h2>
                    <div class="property_loader slider responsive-hidearrows new-listings">
                        <div class="property_loader slider responsive-hidearrows">
     <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 
	
</div>                    </div>
                </div>
                    </div>
            <!-- / Listings for Rent Nearby -->

            <!-- Sold Listings Nearby -->
            <div class="lazy" data-loader="ajax" data-src="/api/similar_listing?type=sold&amp;mlsnum=74257223" data-contentname="slider_similar_listings_for_sold">
                <div class="container mt-5 pt-5 mb-4">
                    <h2 tabindex="0">View nearby recently sold homes</h2>
                    <div class="property_loader slider responsive-hidearrows new-listings">
                        <div class="property_loader slider responsive-hidearrows">
     <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 <div>
    <div class="p-3" style="width:270px;">
        <div class="card card--listing card--listing--portrait card--listing--reduced card--transparent">
            <div class="row no-gutters card--listing__header">
                <div class="col-7">
                    <div class="agent_signature">
                        <div class="agent_signature agent_signature__photo">
                            <a tabindex="-1" href="#" style="background-image: url(https://content.harstatic.com/media/icons/avatar-placeholder.svg)"></a>
                        </div>
                        <div class="agent_signature agent_signature__info">
                            <a class="agent_signature agent_signature__info agent_signature__info__agent_name" href="#">
                                ---- ----
                            </a>
                            <br />
                            <a class="agent_signature agent_signature__info agent_signature__info__broker_name" href="#">
                                ---- ----
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-5 text-right">
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--favorite btn--small-extra btn--icon--onlyicon"></button>
                    <button role="button" class="btn btn--ordinary btn--medium btn--icon btn--icon--lone btn--icon--dot_dot_dot btn--small-extra btn--icon--onlyicon"></button>
                </div>
            </div>
            <!-- /card--listing__header -->
            <!-- card--listing__body -->
            <div class="card--listing__body">
                <a href="#">
                    <div class="card--listing__body__image_content">
                        <div class="card--listing__body__image_content_image" style="background-image: url(/images/loading_pulse.svg);"></div>
                    </div>
                    <div class="card--listing__body__content p-2">
                        <div class="row no-gutters">
                            <div class="col-8">
                                <div class="color_available pb-2 pt-2 font_weight--semi_bold">-- ---- -- ------</div>
                            </div>
                            <div class="col-4 text-right"></div>
                        </div>

                        <div class="row no-gutters pb-2">
                            <div class="col-8">
                                <h4 class="font_size--medium" tabindex="0">--- -------- --</h4>
                                <span class="color_slate_light">----- -- -----</span>
                            </div>
                            <div class="col-4 text-right">
                                <div class="font_size font_size--small font_weight--bold color_carbon">- ----</div>
                                <div class="tag_status tag_status--available mt-1">---</div>
                            </div>
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span> -------
                        </div>
                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span> -----
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">-</span>---- ------
                        </div>

                        <div class="card--listing__body__content__description_row p-0 pt-2">
                            <span class="font_weight--semi_bold color_carbon pr-1">----</span>-- -- ----
                        </div>


                        <div class="clearfix"></div>

                    </div>
                </a>
            </div>

        </div>
    </div>
</div>
 
	 
	
</div>                    </div>
                </div>
            </div>
            <!-- / Sold Listings Nearby -->
			
            <!-- google ad row -->
                        <!-- / google ad row -->



			<!-- RentVsBuy -->
						<!-- RentVsBuy -->





                
                <!--Street Info-->
                <div class="mb-4 mb-md-2 pb-4 pb-md-5 border-bottom border-color--cement_light font_size--medium ">
                     <p>4017 Robertson St, Houston, TX 77009. View photos, map, tax, nearby homes for sale, home values, school info...</p>
                                        <p>View all <a class="font_weight--bold font_size--medium" href="/robertson-st-houston-tx/real-estate-by-street">homes on <span class="text-capitalize">Robertson</span></a></p>
                                    </div>
                <!--Street Info-->


                <div class="mb-md-5 mb-0 pt-md-4 pt-0">
                <!-- tools and resources-->
                <!-- Resource Links -->

<div class="pt-4 mb-5">
    <h2 tabindex="0">More tools and resources</h2>
    <div class="row font_size--large">
                                <div class="dropdown col-12 col-md-6 pb-4">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Print Flyer
            </a>
            <div class="dropdown-menu" aria-labelledby="dropdownMenuLink" x-placement="bottom-start" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(15px, 22px, 0px);">
                                                <a class="dropdown-item" target="_blank" href="https://www.har.com/flyer/2/mlnum/74257223">Printable Flyer (Primary Photo)</a>
                                                <a class="dropdown-item" target="_blank" href="https://www.har.com/flyer/1/mlnum/74257223">Printable Flyer (Multiple Photos)</a>
                                                <a class="dropdown-item" target="_blank" href="https://www.har.com/search/flyer/74257223?customer=1">Print Flyer for Consumer or Buyer</a>
                                                <a class="dropdown-item" target="_blank" href="https://www.har.com/search/flyer/74257223?customer=1&simple=1">Print Simplified Flyer</a>
                            </div>
        </div>
                
                        <div class="col-12 col-md-6 pb-4"><a    href="#calculatorInfo">Calculator</a></div>
        
                        <div class="col-12 col-md-6 pb-4"><a class=CurrencyConverter data-amount="998500" data-amountformat="998,500"  href="javascript:void(0);">Currency Converter</a></div>
        
                        <div class="dropdown col-12 col-md-6 pb-4">
              <a class="dropdown-toggle dropdownSwitchUnit" href="#" role="button" id="dropdownSwitchUnit" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Measurement in Ft-in
            </a>
            <div class="dropdown-menu" aria-labelledby="dropdownSwitchUnit" x-placement="bottom-start" style="position: absolute; will-change: transform; top: 0px; left: 0px; transform: translate3d(15px, 22px, 0px);">
                <a class="dropdown-item dropdownOptionSwitchUnitftin" onclick="javascript:switchUnit(1)" href="javascript:void(0)">Measurement in Ft-in</a>
                <a class="dropdown-item dropdownOptionSwitchUnitmetric" onclick="javascript:switchUnit(2)" href="javascript:void(0)">Measurement in Metric</a>
            </div>
        </div>
        
                        <div class="col-12 col-md-6 pb-4"><a    href="/zipcode_77009/realestate">Homes for Sale 77009 TX</a></div>
        
                        <div class="col-12 col-md-6 pb-4"><a    href="/houses-for-rent-77009">Homes for Rent 77009 TX</a></div>
        
                        <div class="col-12 col-md-6 pb-4"><a    href="/pricetrends/de-noyle-realestate/7997">De Noyle Real Estate</a></div>
        
                        <div class="col-12 col-md-6 pb-4"><a    href="/houston/realestate/">Homes for Sale Houston, TX</a></div>
        
                        <div class="col-12 col-md-6 pb-4"><a    href="/houses-for-rent-houston">Homes for Rent Houston, TX</a></div>
        
                        <div class="col-12 col-md-6 pb-4"><a    href="/harris-county-tx/realestate/48201/for_sale">Harris County, TX Homes for Sale</a></div>
        
                        <div class="col-12 col-md-6 pb-4"><a    href="/harris-county-tx/houses-rent/48201/for-rent/">Harris County, TX Homes for Rent</a></div>
        
                        <div class="col-12 col-md-6 pb-4"><a    href="/robertson-st-houston-tx/real-estate-by-street">All Homes on Robertson</a></div>
        
            </div>
</div>
<!-- /Resource Links -->                </div>


                <!-- all lazy loading modal popup can add here,becuase include inscript does not work on lazy loading ajaxcall-->
                					</div>
					</div>
									</div>
			</div>
            <!-- matomo for buyeraware -->
                            <!-- / padding div -->
                		</div>
		<div class="ldc_right  ">
			<div class="right_sticky">
				<div id="AgentInfo" class="pl-4 pr-4 pt-5 pb-4">
											<div class="pb-4 mb-4 border-bottom border-color--cloudy-sky-light" style="border-width:3px !important;">
	<div class="font_weight--bold font_size--large pb-2 pt-2">
	   
	  	Listing agent
	  	
	</div>
	<div class="agent_signaturev2 agent_signaturev2__medium">

	<a class="agent_signaturev2__medium__photo"
        href="https://www.har.com/patrick-burbridge/agent_STEVENB"
     title="View Patrick Burbridge's profile" style=background-image:url(https://pics.harstatic.com/agent/476135_112x112.jpg?ts=2020-08-14T17:20:00)
								></a>

	<div class="agent_signaturev2__info">
		<div class="agent_signaturev2__info__agent_name" title="View Patrick Burbridge's profile" aria-label="View Patrick Burbridge's profile">
			<div class="agent_signaturev2 agent_signaturev2__medium agent_signaturev2__info__agent_name">
				<a class="row no-gutters"
								href="https://www.har.com/patrick-burbridge/agent_STEVENB"
								>
				<span class="col-auto pr-2 text-truncate color_carbon" style="min-width:100px;">Patrick Burbridge</span>
										<div class="col-auto"><img alt="Platinum" class="ml-1" style="width:51px;" src="https://content.harstatic.com/media/icons/label-platinum.svg" width="51" height="16" /></div>
									</a>
			</div>
		</div>
		
															<div title="View CitiQuest Properties's page" aria-label="View CitiQuest Properties's page">
					<a class="agent_signaturev2__info__broker_name" href="/citiquest-properties/broker_SIDE37" title="View CitiQuest Properties's page" aria-label="View CitiQuest Properties's page"> CitiQuest Properties</a>
				</div>
								</div>
</div>
	<div class="row no-gutters mb-4">
		<div class="col-auto" style="width:64px;"></div>
		<div class="col">
			<div class="dropdown dropdown--custom dropdown--custom__simple">
				<a href="javascript:void(0);" class="dropdown-toggle mt-2 font_weight--semi_bold" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><span class="color_auxiliary">Transactions &amp; Profile</span></a>
				<div class="dropdown-menu" aria-labelledby="dropdownMenuButton" x-placement="bottom-end">
					<a class="dropdown-item" href="https://www.har.com/patrick-burbridge/agent_STEVENB">Agent Profile</a>
																	<a class="dropdown-item" href="/patrick-burbridge/website_stevenb" target="_blank">Agent Website</a>
																					<a class="dropdown-item" href="/patrick-burbridge/listings_stevenb">Agent Listings</a>
														
																					<a class="dropdown-item" href="/STEVENB/sold_by_agent_patrick-burbridge">My Transactions</a>
																					
																																	<a class="dropdown-item" target="_blank" href="http://www.instagram.com/citiquestproperties"> Instagram Profile</a>							
																																									<a class="dropdown-item" target="_blank" href="http://www.facebook.com/citiquest/"> Facebook Profile</a>							
																			</div>
								<div class="mt-1">
					<!-- phone icon-->
<img alt="" src="https://content.harstatic.com/media/icons/map_popup/phone.svg"  class='' style="width:14.7px;height:14.7px;" />
<a data-target='target_phone_presentedby-1' id="source_phone_presentedby-1" href="javascript:void(0);" data-track-type=AGENT_PHONE_CLICKED class="font_weight--semi_bold text-decoration-none hideAgentPhone doListhubTracking" onclick="showPhone('source_phone_presentedby-1','(*************','STEVENB','agent','','9664623')">
  View phone
</a>


<!--target control -->
<a href="tel:(*************" id='target_phone_presentedby-1' class='color_auxiliary' style="display:none;">(*************</a>
<!--source control -->
				</div>
				
			</div>
		</div>
	</div>
</div>

	 <div class="font_weight--bold font_size--large pb-2">Schedule a tour
</div>
	<div class="pb-4">
						 <div data-date="04/11/2025" data-format="Friday, 11 April" class="date_card dept color_auxiliary border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
			<div class="date_card_top">						Today
							
			</div>
			<div class="date_card_date">11</div>
			<div class="date_card_bottom">Apr</div>
		</div>
				 <div data-date="04/12/2025" data-format="Saturday, 12 April" class="date_card dept color_auxiliary border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
			<div class="date_card_top">						Tomorrow
							
			</div>
			<div class="date_card_date">12</div>
			<div class="date_card_bottom">Apr</div>
		</div>
				 <div data-date="04/13/2025" data-format="Sunday, 13 April" class="date_card dept color_auxiliary border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
			<div class="date_card_top">						Sun
							
			</div>
			<div class="date_card_date">13</div>
			<div class="date_card_bottom">Apr</div>
		</div>
				 <div data-date="04/14/2025" data-format="Monday, 14 April" class="date_card dept color_auxiliary border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
			<div class="date_card_top">						Mon
							
			</div>
			<div class="date_card_date">14</div>
			<div class="date_card_bottom">Apr</div>
		</div>
			<a href="/schedule_showing/74257223"  data-event="AgentEmailClick" target="_blank" class="date_card dept color_auxiliary border_radius--default text-center position-relative overflow-hidden d-flex border-0 text-decoration-none doListhubTracking HARTracker" data-track-type="AGENT_EMAIL_CLICKED">
		<div class="align-self-center color_har_blue">Other dates</div>
	</a>
	<div class="clearfix"></div>
	</div>
	
	<div class="mb-2">
		<div class="font_weight--bold font_size--large pb-2">Ask the listing agent a question!</div>
				<a href="tel:(*************" class="btn btn--ordinary btn--small btn--icon btn--icon--phone mb-3 doListhubTracking" data-track-type="AGENT_PHONE_CLICKED">Call</a>
				<a href="javascript:void(0);" class="btn btn--ordinary btn--small btn--icon btn--icon--email mb-3 attachedLead-for-STEVENB doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED" data-toggle="modal" data-target="">Email</a>
			</div>

	<label for="AgentDescpInput"></label>
	<textarea type="text" class="form-control mb-2 ask_agent" id="AgentDescpInput" placeholder="Your comments..."></textarea>

					
	<div class="row no-gutters mt-3 border-color--cloudy-sky-light  pb-4 ">
		<div class="d-flex flex-wrap justify-content-between align-items-center" style="gap: 10px;">
				<button class="btn btn--primary attachedLead-for-STEVENB doListhubTracking HARTracker attachedLead-for" type="button" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED" data-toggle="modal" data-target="">
					Request info
				</button>
						</div>
	</div>

		
		 
	<div class="border-bottom border-color--cloudy-sky-light pl-5 pr-5 pb-4 mb-4 pt-4">
		<div class="font_weight--bold font_size--large pb-1 ">Are you working with a buyer agent?</div>
		<a
    data-msgid="17"
    data-appid="17"
    data-referer="https://www.har.com/homedetail/4017-robertson-st-houston-tx-77009/3011563"
    data-anchor-to="#ConnectToAnAgent"
    data-jump-to=""
    data-content="Sign In to your HAR Account to connect with an agent."
    data-heading="Sign In Required"
    href="javascript:void(0);"
    tabindex="0" 
    class="registerLogin font_weight--bold font_size--medium"
    
>Connect with your agent now -></a>

<script>
    var registerLoginData = Array();
    var appId = 0;

    function initAppLogin(params) {
        appId = params.appid;

        if(typeof registerLoginData[appId] == 'undefined') {
            registerLoginData[appId] = {
                msgId: 0,
                appId: 0,
                referer: '',
                onLoginAnchorTo: '',
                onLoginJumpTo: '',
                nextUrl: '',
                content: '',
                heading: ''
            };
        }

        registerLoginData[appId].msgId = params.msgid;
        registerLoginData[appId].appId = params.appid;
        registerLoginData[appId].referer = params.referer;

        // If onLoginJumpTo (priority over onLoginAnchorTo) is set, then onLoginAnchorTo will be ignored
        if(typeof params.jump_to !== 'undefined') {
            registerLoginData[appId].onLoginAnchorTo = '';
            registerLoginData[appId].onLoginJumpTo = params.jump_to;
        } else if(typeof params.anchor_to !== 'undefined') {
            registerLoginData[appId].onLoginAnchorTo = params.anchor_to;
            registerLoginData[appId].onLoginJumpTo = '';
        } else {
            registerLoginData[appId].onLoginAnchorTo = '';
            registerLoginData[appId].onLoginJumpTo = '';
        }

        registerLoginData[appId].nextUrl = encodeURIComponent(registerLoginData[appId].referer + registerLoginData[appId].onLoginAnchorTo + registerLoginData[appId].onLoginJumpTo);
        registerLoginData[appId].content = params.content;
        registerLoginData[appId].heading = params.heading;

        $.get( `https://www.har.com/api/applogin/flash?msgid=${registerLoginData[appId].msgId}&appid=${registerLoginData[appId].appId}`, function( data ) {
            $('#registerLogin').modal('show');
        });
    }
</script>










			</div>					
				
        									</div>
				
			</div>
		</div><!-- ldc_right -->

				<!-- schedule a showing -->
		<div class="schedule_showing_cntr w-100">
			<div class="mb-5 pb-5 ml-auto mr-auto" style="max-width:776px;">
				<div id="ScheduleFooter">
					<h3 tabindex="0" class="font_size--large_extra_extra_extra font_weight--bold color_carbon mb-5 pb-2">Schedule a Tour</h3>
					<div class="pb-3 date_slider2">
																		<div data-elem="appt_date1" data-date="04/11/2025" data-format="Friday, 11 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Today
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">11</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												<div data-elem="appt_date1" data-date="04/12/2025" data-format="Saturday, 12 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Tomorrow
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">12</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												<div data-elem="appt_date1" data-date="04/13/2025" data-format="Sunday, 13 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Sun
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">13</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												<div data-elem="appt_date1" data-date="04/14/2025" data-format="Monday, 14 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Mon
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">14</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												<div data-elem="appt_date1" data-date="04/15/2025" data-format="Tuesday, 15 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Tue
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">15</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												<div data-elem="appt_date1" data-date="04/16/2025" data-format="Wednesday, 16 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Wed
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">16</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												<div data-elem="appt_date1" data-date="04/17/2025" data-format="Thursday, 17 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Thu
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">17</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												<div data-elem="appt_date1" data-date="04/18/2025" data-format="Friday, 18 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Fri
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">18</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												<div data-elem="appt_date1" data-date="04/19/2025" data-format="Saturday, 19 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Sat
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">19</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												<div data-elem="appt_date1" data-date="04/20/2025" data-format="Sunday, 20 April" class="date_card card_appt_date1 dept color_auxiliary border border_radius--default text-center position-relative overflow-hidden doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">
							<div class="date_card_top">
																Sun
								
								<div class="clearfix"></div>
							</div>
							<div class="date_card_date">20</div>
							<div class="date_card_bottom">Apr</div>
						</div>
												</div>
						<a  onclick="window.open('/schedule_showing/74257223')" class="btn btn--prominent w-100 HARTracker" data-event="AgentEmailClick">Continue to Schedule</a>
			</div>
		</div>
     </div>
		<!-- / schedule a showing -->
				
				<div class="agent_resp_btn d-md-none" >
	<div class="agent_contact ml-auto mr-auto" style="max-width:320px;display:none;">
		<a id="AgentContentClose" class="position-absolute" style="right:10px;" href="javascript:void(0);"><img src="https://content.harstatic.com/media/icons/close.svg" alt="Close"></a>
		<div class="ml-auto mr-auto pb-1 color_slate font_size--medium text-center" style="max-width:190px;">
			
			<a tabindex="-1" href="https://www.har.com/patrick-burbridge/agent_STEVENB" title="View Patrick Burbridge's profile">
				                <img class="border_radius--default overflow-hidden lazy" style="max-width:100%;" data-src="https://pics.harstatic.com/agent/476135.jpg?ts=2020-08-14T17:20:00" alt="Agent placeholder"></a>
                		</div>
		<div class="text-center">

			<div class="mb-0 pb-2 font_size--large_extra_extra font_weight--bold color_black text-center">Patrick Burbridge</div>
												<a class="agent_signaturev2__info__broker_name pb-2" href="https://www.har.com/citiquest-properties/broker_SIDE37" title="View CitiQuest Properties's page" aria-label="View CitiQuest Properties's page"> CitiQuest Properties</a>
										<div class="pb-2">
												<img alt="Platinum" class="mr-2" style="width:51px;" src="https://content.harstatic.com/media/icons/label-platinum.svg" width="51" height="16">
												
					</div>

					<div class="pb-3">

												<!-- phone icon-->
<a data-target='target_phone_presentedby-2' id="source_phone_presentedby-2" href="javascript:void(0);"  class="circle_btn font_weight--semi_bold m-2 hideAgentPhone" onclick="showPhone('source_phone_presentedby-2','(*************','STEVENB','agent','','9664623')">
  <div><img alt="" src="https://content.harstatic.com/media/icons/phone_blue_small.svg" width="22" height="22" /></div><span class="font_weight--semi_bold">Call</span>
</a>


<!--target control -->
<a href="tel:(*************" id='target_phone_presentedby-2' class='color_auxiliary' style="display:none;">(*************</a>
<!--source control -->
																		<a href="sms:+17135816000" class="circle_btn  m-2">
							<div><img src="https://content.harstatic.com/media/icons/comment_blue.svg" alt=""></div><span class="font_weight--semi_bold">Message</span>
						</a>
												<a href="javascript:void(0);" data-toggle="modal" data-target="" class="circle_btn  m-2 attachedLead-for-STEVENB HARTracker" data-event="AgentEmailClick">
							<div><img src="https://content.harstatic.com/media/icons/email.svg" alt="Email"></div><span class="font_weight--semi_bold">Email</span>
						</a>
						<a href="https://www.har.com/patrick-burbridge/agent_STEVENB" class="circle_btn m-2">
							<div><img alt="" style="width:22px;" src="https://content.harstatic.com/media/icons/profile_blue.svg"></div><span class="font_weight--semi_bold">Profile</span>
						</a>
													<a href="/patrick-burbridge/website_stevenb" target="_blank" class="circle_btn m-2">
								<div><img src="https://content.harstatic.com/media/icons/computer.svg" alt="Website"></div><span class="font_weight--semi_bold">Website</span>
							</a>
												</div>
		</div>
		<label for="Agent"></label>
		<input type="text" class="form-control mb-2  ask_agent" id="Agent" aria-describedby="Agent" placeholder="Ask anything you want...">
		<button class="btn btn--primary w-100 attachedLead-for-STEVENB" type="button" data-toggle="modal" data-target="">Request Information</button>
			</div>
	<div id="RespBtn">
		<div class="row no-gutters">
			<div class="col OpenAgentContact removeLink">
									<div class="agent_signature--large">
						<div class="agent_signature agent_signature--large__photo d-block overflow-ellipsis overflow-hidden" style="">
							<a tabindex="-1" href="https://www.har.com/patrick-burbridge/agent_STEVENB" title="View Patrick Burbridge's profile" style=background-image:url(https://pics.harstatic.com/agent/476135_112x112.jpg?ts=2020-08-14T17:20:00)
								></a>
						</div>
						<div class="agent_signature--large__info pt-0" style="white-space:normal">

							<a class="agent_signature--large__info__agent_name mb-0" href="https://www.har.com/patrick-burbridge/agent_STEVENB" title="View Patrick Burbridge's profile">
								<span class="color_har_blue_dark font_size--medium">Patrick Burbridge</span>
							</a>
							<div class="">
																<img alt="Platinum" class="mr-2" style="width:51px;" src="https://content.harstatic.com/media/icons/label-platinum.svg">
																							</div>
															<a style="width:150px;" class="text-truncate d-block color_slate text-decoration-none" href="https://www.har.com/citiquest-properties/broker_SIDE37" title="View CitiQuest Properties's page" aria-label="View CitiQuest Properties's page" class="text-truncate d-block color_slate text-decoration-none">CitiQuest Properties</a>
								
						</div>
					</div>
			</div>
			<div class="col-auto text-right">
				<button id="OpenAgentContact" type="button" tabindex="0" class="btn btn--primary mb-3 OpenAgentContact  HARTracker" data-event="AgentEmailClick">Contact</button>
								<a onclick='window.open("/schedule_showing/74257223")' href="javascript:void(0);" class="font_weight--bold d-block font_size--medium  doListhubTracking HARTracker" data-event="AgentEmailClick" data-track-type="AGENT_EMAIL_CLICKED">Take a tour  →</a>
							</div>
		</div>

	</div>
</div>
<div class="agent-resp-overlay" style="background:#000;opacity:0.5;left:0px;right:0px;width:100%;height:100%;display:none;position:fixed;z-index:1;top: 0px;"></div>
			</div><!-- listing_detail_cntr -->
</div>
					</div>





    </div>

        <style>
    @media (max-width:600px) {
        .for-consumer a.mr-3 { display: inline-block; margin-bottom:0.6rem !important;margin-right:0.6rem !important; }
        .for-consumer a.mr-3 img { width: 32px !important; }
    }
</style>
<div id="registerLogin" class="modal fade modal--medium" tabindex="-1" role="dialog" aria-labelledby="registerLogin" style="display: none; " aria-hidden="true">
    <div class="modal-dialog" role="document" style="max-width:580px;">
        <div class="modal-content ">
            <div class="modal-header">
                <h5 class="rlHeading modal-title pt-3">Sign In Required</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true"></span>
                </button>
            </div>
            
            <div class="modal-body p-2 text-left">
                <div class="p-3 font_size--large border-bottom">
                    <div class="font-weight-normal">
                        <div class="rlLoginContent">Create an account and enjoy all the benefits of HAR.com!</div>
                    </div>
                </div>
                <div class="p-3 p-md-4 over font_size--large">
                    <h3 tabindex="0" class="mb-2">Sign In</h3>
                    <div class="">
                        <div class="mb-1 mb-md-4 for-consumer">
                            <div class="pb-3">Sign in using social account</div>
                            <a class="socialLinks text-decoration-none mr-2 d-none" data-social-link="" href="#">
                                <img alt="HAR" src="https://content.harstatic.com/resources/images/common/logo-blue.svg" style="width: 46px;">
                            </a>
                                                        <a class="socialLinks text-decoration-none mr-3" data-social-link="https://har-auth.auth0.com/authorize?response_type=code&amp;client_id=mZD0aRdYa38XXcmWyb8NpGsRaOhlcNJH&amp;connection=google-oauth2&amp;scope=openid profile email&amp;redirect_uri=https://www.har.com/login/social" href="">
                                <img style="width:42px;" src="https://content.harstatic.com/media/icons/social_colored/googleg_colored.svg">
                            </a>
                                                        <a class="socialLinks text-decoration-none mr-3" data-social-link="https://har-auth.auth0.com/authorize?response_type=code&amp;client_id=mZD0aRdYa38XXcmWyb8NpGsRaOhlcNJH&amp;connection=facebook&amp;scope=openid profile email&amp;redirect_uri=https://www.har.com/login/social" href="">
                                <img style="width:42px;" src="https://content.harstatic.com/media/icons/social_colored/facebook_colored.svg">
                            </a>
                                                        <a class="socialLinks text-decoration-none mr-3" data-social-link="https://har-auth.auth0.com/authorize?response_type=code&amp;client_id=mZD0aRdYa38XXcmWyb8NpGsRaOhlcNJH&amp;connection=apple&amp;scope=openid profile email&amp;redirect_uri=https://www.har.com/login/social" href="">
                                <img style="width:42px;" src="https://content.harstatic.com/media/icons/social/social_icon_apple.svg">
                            </a>
                                                        <a class="socialLinks text-decoration-none mr-3" data-social-link="https://har-auth.auth0.com/authorize?response_type=code&amp;client_id=mZD0aRdYa38XXcmWyb8NpGsRaOhlcNJH&amp;connection=windowslive&amp;scope=openid profile email&amp;redirect_uri=https://www.har.com/login/social" href="">
                                <img style="width:42px;" src="https://content.harstatic.com/media/icons/map_popup/windows-copy-3.svg">
                            </a>
                                                        <a class="socialLinks text-decoration-none mr-3" data-social-link="https://har-auth.auth0.com/authorize?response_type=code&amp;client_id=mZD0aRdYa38XXcmWyb8NpGsRaOhlcNJH&amp;connection=amazon&amp;scope=openid profile email&amp;redirect_uri=https://www.har.com/login/social" href="">
                                <img style="width:42px;" src="https://content.harstatic.com/media/icons/social/social_icon_amazon.svg">
                            </a>
                                                        <a class="socialLinks text-decoration-none mr-3" data-social-link="https://har-auth.auth0.com/authorize?response_type=code&amp;client_id=mZD0aRdYa38XXcmWyb8NpGsRaOhlcNJH&amp;connection=linkedin&amp;scope=openid profile email&amp;redirect_uri=https://www.har.com/login/social" href="">
                                <img style="width:42px;" src="https://content.harstatic.com/media/icons/social_colored/linkedin_colored.svg">
                            </a>
                                                        <a class="socialLinks text-decoration-none mr-3" data-social-link="https://har-auth.auth0.com/authorize?response_type=code&amp;client_id=mZD0aRdYa38XXcmWyb8NpGsRaOhlcNJH&amp;connection=twitter&amp;scope=openid profile email&amp;redirect_uri=https://www.har.com/login/social" href="">
                                <img style="width:42px;" src="https://content.harstatic.com/media/icons/social/icons-social-network-twitter-color.svg">
                            </a>
                                                    </div>
                    </div>
            
                    <div class="pt-2">
                        <div class="mb-3 d-none">Sign in using HAR Account</div>
                        <a href="#" class="rlSingIn btn btn-primary mb-4">Sign in using HAR Account</a>
                        <div class="row small-gutters">
                            <div class="col-auto">Don&#039;t have an account?</div>
                            <div class="col-auto"><a href="#" class="rlCreateAccount">Create one for free today!</a></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>    
                <footer class="color_snow_white footer_v2 ">
	<div class="container--v2 container">
		<div class="pb-5" style="border-bottom: 1px solid #042280;">
			<div class="row">
				<div class="col-md-4 col-12 pb-5 pb-md-0">
					<div style="max-width:300px;">
						<a class="mb-5 d-inline-block" href="https://www.har.com/"><img loading="lazy" fetchPriority="low" alt="HAR" style="width:72px;" src="https://content.harstatic.com/resource_2019/imgs/common/har_logo_white_footer.svg" width="72" height="72"></a>
						<div class="font_size--large pb-md-5 pb-4">
							COPYRIGHT© 2025<br> HOUSTON REALTORS®<br>
							INFORMATION SERVICE, INC.<br>
							All Rights Reserved.
						</div>
						<div class="pb-3">
							<a rel="nofollow" href="https://www.facebook.com/HARFans" class="mr-2 mr-md-4 me-2 me-md-4 pr-1 pe-1 hover-opacity"><img loading="lazy" fetchPriority="low" alt="facebook" style="width:24px;height:24px;" width="24" height="24" src="https://content.harstatic.com/resource_2019/imgs/icons/social_icons_footer/facebook_footer.svg"></a>
							<a rel="nofollow" href="https://www.linkedin.com/company/harmembers/" class="mr-2 mr-md-4 me-2 me-md-4 pr-1 pe-1 hover-opacity"><img loading="lazy" fetchPriority="low" alt="linkedin" style="width:24px;height:24px;"  width="24" height="24" src="https://content.harstatic.com/resource_2019/imgs/icons/social_icons_footer/linkedin_footer.svg"></a>

							<!-- https://content.harstatic.com/resource_2019/imgs/icons/social_icons_footer/twiter_footer.svg -->
							<!-- style="width:24px;height:24px;" -->
							<!-- width="24" height="24" -->
							<a rel="nofollow" href="https://twitter.com/harmembers" class="mr-2 mr-md-4 me-2 me-md-4 pr-1 pe-1 hover-opacity text-decoration-none">
								<img loading="lazy" fetchPriority="low" alt="twiter" src="https://content.harstatic.com/media/icons/social_recommendation/twitter.svg" style="filter: brightness(0) invert(1);">
							</a>


							<a rel="nofollow" href="https://www.youtube.com/user/hartv" class="mr-2 mr-md-4 me-2 me-md-4 pr-1 pe-1 hover-opacity"><img loading="lazy" fetchPriority="low" alt="googleplay" style="width:28.4px;height:20px;"  width="28.5" height="20" src="https://content.harstatic.com/resource_2019/imgs/icons/social_icons_footer/googleplay_footer.svg"></a>
							<a rel="nofollow" href="https://www.instagram.com/harmembers/" class="mr-2 mr-md-4 me-2 me-md-4 pr-1 pe-1 hover-opacity"><img loading="lazy" fetchPriority="low" alt="instagram" style="width:24px;height:24px;" width="24" height="24" src="https://content.harstatic.com/resource_2019/imgs/icons/social_icons_footer/instagram_footer.svg"></a>
						</div>
						<div class="d-flex pt-md-4 pt-3">
							<a class="align-self-center"><img loading="lazy" fetchPriority="low" src="https://content.harstatic.com/media/icons/logo-equal-housing-opportunity-small-white.svg" alt="Equal Housing Opportunity"></a>
							<div class="color_snow_white font_size--small_extra pl-4 ps-4" style="opacity:0.48;">Equal Housing Opportunity – Disclaimer: All information on this site is subject to change and should be independently verified.
							<br>
							<a href="https://cms.har.com/code-of-ethics" style="font-size:11px">Code Of Ethics</a></div>
							
						</div>
					</div>
				</div>

				<div class="col-md-8 col-12 pt-5 pt-md-0">
					<!-- footer contact cols -->
					<div class="row pt-2 pt-md-0">
						<div class="col-6 col-sm-6 col-md-3 mb-5 mb-md-2">
							<div class="font_size--large_extra text-uppercase font-weight-bold fw-bold mb-4">Company</div>
							<ul class="list-unstyled font_size--large">
								<li><a href="https://cms.har.com/about-us/">About Us</a></li>
								<!--
						<li><a class="color_snow_white" href="#">Benefits</a></li>
						-->
								<li><a href="https://www.har.com/content/department/newsroom">Newsroom</a></li>
								<!--
						<li><a href="#">Advertising Info</a></li>
						-->
								<li><a href="https://cms.har.com/help-center/">Help Center</a></li>
								<li><a href="https://www.har.com/contact-us">Contact Us</a></li>
							</ul>
						</div>
						<div class="col-6 col-sm-6 col-md-3 mb-5 mb-md-2">
							<div class="font_size--large_extra text-uppercase font-weight-bold fw-bold mb-4">Real Estate</div>
							<ul class="list-unstyled font_size--large">
								<li><a href="https://www.har.com/texasrealestate/">Texas Real Estate</a></li>
								<li><a href="https://www.har.com/texasrealestate/?tab=county">Counties</a></li>
								<li><a href="https://www.har.com/texasrealestate/?tab=city">Cities</a></li>
								<li><a href="https://www.har.com/neighborhoods">Neighborhoods</a></li>
								<li><a href="https://www.har.com/texasrealestate/?tab=zip">Zip Codes</a></li>
							</ul>
						</div>
						<div class="col-6 col-sm-6 col-md-3 mb-5 mb-md-2">
							<div class="font_size--large_extra text-uppercase font-weight-bold fw-bold mb-4">Resources</div>
							<ul class="list-unstyled font_size--large">
								<li><a href="https://www.har.com/insight">RealInsight</a></li>
								<li><a href="https://www.har.com/answers">Ask a Pro</a></li>
								<!--
						<li><a class="color_snow_white" href="#">Articles & Blogs</a></li>
						-->
								<li><a href="https://www.har.com/dictionary">Knowledge Base</a></li>
								<li><a href="https://www.har.com/content/consumer_knowledge">Knowledge Videos</a></li>
								<li><a href="https://www.har.com/realestatepro/serviceproviders">Service Providers</a></li>
								<li><a href="https://cms.har.com/tools/">Other Resources</a></li>
							</ul>
						</div>
						<div class="col-6 col-sm-6 col-md-3 mb-5 mb-md-2">
							<div class="font_size--large_extra text-uppercase font-weight-bold fw-bold mb-4">Product</div>
							<ul class="list-unstyled font_size--large">
								<li><a href="https://www.har.com/mobile">HAR Mobile App</a></li>
								<li><a href="https://www.har.com/rentals">HAR Rentals</a></li>
								<li><a href="https://www.har.com/realestatepro">Find a Pro</a></li>
								<li><a href="https://www.har.com/openhouse">Open Houses</a></li>
								<li><a href="https://www.har.com/content/page/platinum_services">MLS Platinum</a></li>
							</ul>
						</div>

					</div>
					<!-- footer contact cols -->
					<div class="row">
						<div class="col-md-7 ml-auto ms-auto">
							<div class="ml-md-auto text-lg-right text-left pt-md-5 ms-md-auto text-lg-end text-start">
								<a rel="nofollow" class="d-inline-block pr-4 pb-3 pe-4" href="https://itunes.apple.com/us/app/har-com-houston-real-estate/id386981161"><img loading="lazy" fetchPriority="low" style="max-width:197px;" src="https://content.harstatic.com/media/icons/download-on-the-app-store-black.svg" width="197" height="64" alt="Download on the App Store"></a>
								<a rel="nofollow" class="d-inline-block pr-4 pb-3 pe-4" href="https://play.google.com/store/apps/details?id=com.har.androidapp&amp;hl=en"><img loading="lazy" fetchPriority="low" style="max-width:197px;" src="https://content.harstatic.com/media/icons/google-play-badge_black.svg" width="197" height="59" alt="Get it on Google Play"></a>
								<p class="text-left ml-2 ms-2 text-start">
									<a href="https://www.har.com/mobile">HAR Mobile Apps</a>
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>





		</div>
	</div><!-- container -->

	<!-- footer app row -->
	<div id="SocialRow">
		<div class="container container--v2 pt-md-3 pb-md-4">
			<a href="https://cms.har.com/accessibility/" class="d-inline-block mr-md-5 me-md-5">Accessibility</a>
			<a href="https://cms.har.com/privacy-policy/" class="d-inline-block mr-md-5 me-md-5">Privacy Policy</a>
						<a href="javascript:void(0)" class="d-inline-block mr-md-5 me-md-5" id="manageketch"><img class="mr-2 me-2" src="https://content.harstatic.com/media/icons/ketch/privacyoption.svg" style="width:29px;border:0px" alt="privacy check icon">Your Privacy Choices</a>
						
			<a href="https://cms.har.com/termsofuse/" class="d-inline-block mr-md-5 me-md-5">Terms of Use</a>
			<a href="https://www.har.com/contact-us" class="d-inline-block mr-md-5 me-md-5">Contact Us</a>
		</div>
	</div>
	<!-- footer app row ended -->
	 

</footer>
<div class="menu_overlay"></div>

            
    <div id="SharingPopup" class="modal fade modal--medium" tabindex="-1" role="dialog" aria-labelledby="SharingPopupMembersOnly" style="display: none;" aria-hidden="true">
	<div class="modal-dialog modal-dialog-scrollable modal-lg modal-dialog-top" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 tabindex="0" class="modal-title" id="exampleModalCenteredScrollableTitle">Share</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close"></button>
			</div>
			<div class="modal-body p-4 p-md-5">
				<div class="share-form">

				</div>
				<div class="share-form-loading text-center">
					<img alt="loading" src="https://content.harstatic.com/img/common/loading1.gif">
				</div>
			</div>
		</div>
	</div>
</div>
<div id="shareToClient" class="modal fade modal--medium" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenteredScrollableTitle" style="display: none;" aria-hidden="true">
	<div class="modal-dialog modal-dialog-scrollable  modal-lg modal-dialog-top" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 tabindex="0" class="modal-title" id="exampleModalCenteredScrollableTitle"> Send To Clients</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close"></button>
			</div>
			<div class="modal-body text-left">
				<div class="shareToClient-form">

				</div>
				<div class="shareToClient-form-loading">
					<img alt="loading" src="https://content.harstatic.com/img/common/loading1.gif">
				</div>
			</div>

		</div>
	</div>
</div>
    <script src="/js/jquery-2.2.4.min.js"></script>
    <div class="modal fade modal--small modal_fulldescription" tabindex="-1" role="dialog" aria-labelledby="fullDescription" aria-hidden="true">
	<div class="modal-dialog modal-dialog-scrollable modal-lg modal-dialog-centered" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 tabindex="0" class="modal-title" id="exampleModalCenteredScrollableTitle">Photo description</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg"></button>
			</div>
			<div class="modal-body text-left">
			</div>
		</div>
	</div>
</div>
<!-- modal popup for Tags -->
<div class="modal fade modal--small modal_fullTags" tabindex="-1" role="dialog" aria-labelledby="fullDescription" aria-hidden="true">
	<div class="modal-dialog modal-dialog-scrollable modal-lg modal-dialog-centered" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h5 tabindex="0" class="modal-title" id="exampleModalCenteredScrollableTitle">All Tags</h5>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg"></button>
			</div>
			<div class="modal-body text-left mg-tags-list">

			</div>
		</div>
	</div>
</div>
<script src="/plugins/slider/slick.min.js"></script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
        
    	<script src="/js/jquery-ui-thin.min.js"></script>
        <script type="text/javascript" src="/js/harload.js"></script>
  
    <script src="/js/bootstrap.bundle.min.js"></script>

            <script src="/js/har-core.min.js?v=*********"></script>
        <script src="/plugins/bootbox/bootbox.min.js"></script>
    <script defer src="/js/share.js?v=*********"></script>
	
    			<div id="ReportProblem" class="modal fade modal--small custom_report_modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;min-height: 300px;">
		<div class="modal-dialog modal-dialog-scrollable modal-sm modal-dialog-centered" role="document">
		<div class="modal-content">
		  <div class="modal-header">
		   <h5 tabindex="0" class="modal-title" id="myModalLabel">Report Problem</h5>
			<button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="opacity:1;"><img src="https://content.harstatic.com/resources/images/icons/close-blue.png" alt="Close"></button>
		  </div>
		  <div class="modal-body text-left pt-3 pl-5 pr-5 pb-5">
			  <div class="report_issue">
					<div class="color_har_blue_dark mb-2 mt-4" tabindex="0">Who are you? <span id="report_type_required" style="display:none;color:red;"><img title="Please select the type!" src="https://content.harstatic.com/media/icons/icon-field-required_input.svg" alt=""></span></div>
					<label id="Consumer" class="">
					   <input type="radio" class="custom_radio" value="I’m a Consumer" name="report_type">
					   <span>I'm a Consumer</span>
					</label>
					<label id="Professional" class="pb10">
					   <input type="radio" class="custom_radio" value="I’m a Real Estate Professional" name="report_type">
					   <span>I'm a Real Estate Professional</span>
					</label>

					<div class="color_har_blue_dark mb-2 mt-4">What's the issue? <span id="report_availability_required" style="display:none;color:red;"><img title="Please select the issue!" src="https://content.harstatic.com/media/icons/icon-field-required_input.svg" alt=""></span></div>
					<label id="NotAvailable" class="">
					   <input type="radio" class="custom_radio" value="Home is no longer available" name="report_availability">
					   <span>Home is no longer available</span>
					</label>
					<label id="Incorrect" class="">
					   <input type="radio" class="custom_radio" value="Incorrect information or photos" name="report_availability">
					   <span>Incorrect information or photos</span>
					</label>
					<label id="Fraudulent" class="pb10">
					   <input type="radio" class="custom_radio" value="Fraudulent listing or spam" name="report_availability">
					   <span>Fraudulent listing or spam</span>
					</label>
					<div class="font18 dark bold pt20 pb10">Name</div>
					<input type="text" class="form-control mb-3" id="report_name" placeholder="Enter your name">

					<div class="font18 dark bold pt20 pb10">Any additional comments?</div>
					<textarea id="report_comments" class="form-control mb-3" rows="3" placeholder="Add comments, if you want…"></textarea>

					<div class="alert alert-danger report_alert" role="alert" style="display:none;"></div>

				</div>

				<div class="report_msg" style="padding:26px 24px 100px 24px;font-size:16px;line-height:24px;color:#4f6672;display:none;">

                    Thank you for notifying us about the issue with 4017 Robertson St. Our team will look into your reported issue soon. Thank you for your patience.
                    
				</div>
				 <div style="background-color: #eceff3;padding:6px 24px;font-size:12px;">
				Can't find the issue here? Check our <a href="mailto:<EMAIL>">Support Center.</a>
			  </div>
		  </div>
		  <div class="modal-footer text-center report_footer">
			  <div class="text-right pb10" style="padding:10px 24px 10px 24px;">
				<button type="button" class="btn btn--primary btn--medium" onclick="reportIssueClick()">Submit</button>
				<button type="button" class="btn btn--shapeless btn--medium" data-dismiss="modal">Back</button>
			  </div>
			  <div style="background-color: #eceff3;padding:6px 24px;font-size:12px;display:none;">
				Can't find the issue here? Check our <a href="mailto:<EMAIL>">Support Center</a>
			  </div>
		  </div>
		  </div>
		  </div>
		</div>
<script>
		function reportIssue(){
			$('#report_name').val('');
			$('#report_comments').val('');
			$('.report_issue').show();
			$('.report_msg').hide();
			$('.report_footer').show();
			$('#ReportProblem').modal('show');
		}
		function reportIssueClick(){
			var names = []
			$('.report_issue input:radio').each(function () {
				var rname = $(this).attr('name');
				if ($.inArray(rname, names) == -1) names.push(rname);
			});
			var radioError = false;
			$.each(names, function (i, name) {
				if ($('.report_issue input[name="' + name + '"]:checked').length == 0) {
					radioError = true;
					$('#' + name + '_required').show();
				}else{
					$('#' + name + '_required').hide();
				}
			});

			//check for error
			if(radioError) {
				//$('.report_alert').html('Please select the required fields to proceed!').show();
				return;
			}
			//$('.report_alert').hide();
			$('.report_issue').hide();
			$('.report_footer').hide();
			$('.report_msg').show();
			href = "mailto:<EMAIL>?subject=Problem reported with a listing&body=";
			href += getBody();
			window.location.href = href;

		}
		function getBody() {
			body = "Address: 4017 Robertson St %0D%0A";
			body += "Who you are: " + $("input[name='report_type']:checked").val() + "%0D%0A";
			body += "What's the issue: " + $("input[name='report_availability']:checked").val() + "%0D%0A";
			if($('#report_name').val() != '')body += 'Name: ' + $('#report_name').val() + "%0D%0A";
			if($('textarea#report_comments').val() != '')body += 'Comments: ' + $('textarea#report_comments').val() + "%0D%0A";

			return body;
		}
</script>
<div id="infomodal" class="modal fade modal--small" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;min-height: 300px;">
	<div class="modal-dialog modal-dialog-scrollable modal-sm modal-dialog-centered" role="document">
		<div class="modal-content">
		  <div class="modal-header">
		   <h5 tabindex="0" class="modal-title" id="infomodalheading"></h5>
			<button type="button" class="close" data-dismiss="modal" aria-hidden="true" style="opacity:1;"><img width="16" height="16"  src="https://content.harstatic.com/resources/images/icons/close-blue.png"></button>
		  </div>
		  <div class="modal-body text-left pt-3 pl-5 pr-5 pb-5">

				<div id="infomodalbody" style="font-size:16px;line-height:24px;color:#4f6672;" ></div>
		  </div>

		  <div class="modal-footer text-center report_footer">
			  <div class="text-right">
				<button type="button" class="btn btn--shapeless btn--medium" data-dismiss="modal">Ok</button>
			  </div>
		  </div>
		</div>
	</div>
</div>
<script>
   if (typeof $.ui == 'undefined' || typeof $.ui.datepicker == 'undefined' ) {
            jQuery.getScript("https://code.jquery.com/ui/1.12.1/jquery-ui.min.js")
                .done(function() {
                   $("#duedate").datepicker();
            });
        } else  {
               $("#duedate").datepicker();
        }
</script>
                        <div id="ContactOffice" class="modal fade modal--small" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenteredScrollableTitle" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-sm  modal-dialog-centered" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h5 tabindex="0" class="modal-title" id="exampleModalCenteredScrollableTitle">Contact</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close">
                </button>
            </div>
            <div class="modal-body text-left">
                <div class="mb-3">
                    <div id="lead_result" style=" display:none;">Your message has been successfully delivered. If you
                        need immediate assistance, please contact HAR.com Support <b>(713-629-1900 x 374)</b>.
                        <br><br>Wish you the best!<br><br>HAR Team<br>
                    </div>
                    <div id="lead_form_div">
                                                <input type="hidden" id="lead_mlsnum" name="mlsnum" value="74257223">
                        <input type="hidden" id="lead_listingid" name="listingid" value="9664623">
                        <input type="hidden" id="lead_harid" name="harid" value="3011563">
                        <input type="hidden" id="lead_pAddress" name="pAddress" value="4017 Robertson St ,Houston ,TX ,77009">
                        <input type="hidden" id="lead_photoUrl" name="photoUrl" value="https://photos.harstatic.com/411360813/lr/img-1.jpeg?ts=2025-04-07T13:11:39.830">
                        <input type="hidden" name="subject" id="lead_subject" value="Consumer requests more information for 4017 Robertson St">
                        <input type="hidden" id="lead_listhubTrack" name="listhubTrack" value="OFFICE_EMAIL_SENT">
                        
                        <input type="hidden" id="lead_officekey" name="officekey" value="SIDE37">
                        <input type="hidden" id="lead_pAddress" name="pAddress" value="">
                        <input type="hidden" id="lead_photoUrl" name="photoUrl" value="">
                        <input type="hidden" id="lead_lead_source_id" name="lead_source_id" value="100">
                        <input type="hidden" id="lead_portalid" name="portalid" value="HAR">
                        <input type="hidden" name="leadsource" id="lead_leadsource" value="https://www.har.com/homedetail/4017-robertson-st-houston-tx-77009/3011563">
                        <input type="hidden" id="lead_lead_type" name="lead_type" value="1">
                        <input type="hidden" id="lead_ab_type" name="ab_type" value="b">
                         
                        <input type="hidden" name="hsource" id="lead_hsource" value="1">
                                                <div class="row pt-3">
                            <div class="col-md-6 col-12 pb-3 pb-md-0">
                                <label for="Contact_FirstName">First Name</label>
                                <input type="text" class="form-control" id="lead_firstname" name="firstname" aria-describedby="FirstName" placeholder="First Name" value="" maxlength="54" required />
                            </div>
                            <div class="col-md-6 col-12 pb-3 pb-md-0">
                                <label for="Contact_LastName">Last Name</label>
                                <input type="text" class="form-control" id="lead_lastname" name="lastname" aria-describedby="LastName" placeholder="Last Name" value="" maxlength="54" required />
                            </div>
                        </div>
                        <div class="row pt-3 pb-3">
                            <div class="col-md-6 col-12 pb-3 pb-md-0">
                                <label for="Contact_Email">Email</label>
                                <input type="email" class="form-control" id="lead_email" name="email" aria-describedby="Contact_Email" placeholder="Email" value="" maxlength="54" required />
                            </div>
                            <div class="col-md-6 col-12 pb-3 pb-md-0">
                                <label for="Contact_Phone">Phone</label>
                                <input type="tel" class="form-control  phone-number-only" id="lead_phone" name="phone" aria-describedby="Phone" placeholder="Phone" value="" />
                            </div>
                        </div>
                        <label for="IWouldLike">I would like to</label>
                        <select id="lead_messageid" name="messageid" class="custom-select custom-select_large" required>
                            <option value="1" selected>Have a REALTOR® call me back</option>

                        </select>

                        <label class="mt-3" for="Contact_YourMessage">Your Message</label>
                        <textarea id="lead_comment" name="comment" rows="3" class="form-control" placeholder="Your Message" onkeyup="updateCount('lead_comment','lead_CommentCount',500);"></textarea>
                        <div id="lead_CommentCount">Please limit your comment to 500 characters.</div>

                        <div id="office-lead-email-hcaptcha" class="lead_hCaptchaLoader mt-3"></div>   

                    </div> <!-- end of form -->
                </div>
            </div>
            <div class="modal-footer text-center h-auto">
                 <button role="button" id="lead_ebutton" onclick="$('#lead_ebutton').hide(); $('#lead_canclbutton').hide(); SendAgentLead('lead');" class="btn btn--primary btn--medium" aria-label="Apply">Submit</button>
                <button role="button" id="lead_canclbutton" class="btn btn--shapeless btn--medium" data-dismiss="modal" aria-label="Cancel">Cancel</button>
                <button role="button" id="lead_anotherbtn" style="display:none;" onclick="hcaptcha.reset();$('#lead_anotherbtn').hide();$('#lead_result').hide();$('#lead_form_div').show();$('#lead_canclbutton').show();$('#lead_ebutton').show();" class="btn btn--primary btn--medium " aria-label="Apply">Send another email</button>
            </div>
        </div>
    </div>
</div>
            <!-- Gallery HTML starts here -->
<div class="gallery_v2 with_remodel" style=display:none;>
	<!-- all Photos Starts here -->
	<div class="all_photos">
		<div class="photo_full_view_header">
						<a href="#" class="d-inline-flex align-items-center text-decoration-none cursor--pointer  MG-back-button pl-4"><img class="mr-3" src="https://content.harstatic.com/media/icons/slider/arrow_bold_white.svg" style="transform: rotate(180deg);width:8px;"> </a>
						<div class="photo_full_view_header__title cursor--pointer header__address align-self-center MG-back-button ">
				<span class="font_weight--bold">
										Back
					
				</span>
			</div>

			
		
			<div class="photo_full_view_header__title header__back align-self-center" style="display: none">
				<a class="d-inline-flex align-items-center text-decoration-none  "><span class="font_weight--bold color_snow_white cursor--pointer">Back</span></a>
			</div>

			<div class="photo_full_view_header__center align-content-end text-center header_tabs xl_tabs_cntr" style="display: none;">
								<ul class="nav nav-tabs nav-tabs--negative mt-auto mb-0 gallery_top_tabs" id="myTab" role="tablist">
										<li class="nav-item">
						<a class="nav-link photo-mg-tab photo-Tab active" id="photo-tab" data-toggle="tab" href="#photo-mg-tab" aria-controls="Photos" aria-selected="1">Photos</a>
					</li>
										<li class="nav-item">
						<a class="nav-link audio-mg-tab " id="audio-tab" data-toggle="tab" href="#audio-mg-tab" aria-controls="Audio Tour" aria-selected="">Audio Tour</a>
					</li>
										<li class="nav-item">
						<a class="nav-link StreetlViewTabC " id="streetview-tab" data-toggle="tab" href="#streetview-mg-tab" aria-controls="Street View" aria-selected="">Street View</a>
					</li>
										<li class="nav-item">
						<a class="nav-link AerialViewTabA " id="aerialView-tab" data-toggle="tab" href="#aerialView-mg-tab" aria-controls="Aerial View" aria-selected="">Aerial View</a>
					</li>
									</ul>
							</div>
			<div class="photo_full_view_header__controls align-self-center d-inline-flex d-none">

				<button role="button" class="btn btn--ordinary btn--small shareMGBtn p-0 d-flex align-items-center mr-md-3 mr-2"><img src="https://content.harstatic.com/media/icons/share_white.svg" style="width:16px;" /></button>
														<button data-appid="5" data-icon="https://content.harstatic.com/media/icons/icon_heart_white.svg" class=" btn  sharebtn  btn--icon btn--icon--lone d-md-inline-block fav--9664623 cmd--fav  cmd--fav-mg btn--icon--favorite px-md-3 mr-md-3 py-0" data-edit_node="hide" data-toggle="tooltip" data-placement="top" title="Bookmark" style="width:auto;" data-lid="9664623" data-image="https://photos.harstatic.com/411360813/hr/img-1.jpeg?ts=2025-04-07T13:11:39.830" data-mls="74257223" data-address="4017 Robertson St, Houston TX 77009" data-bookmarked="0" aria-label="Save 4017 Robertson St, Houston TX 77009 as favorite">
				</button>
				

		

													<a href="/schedule_showing/74257223" target="_blank" class="btn btn--ordinary btn--small d-block d-md-none mx-2 " style="line-height:30px !important;">Tour</a>
							<a href="/schedule_showing/74257223" target="_blank" class="btn btn--ordinary btn--small mr-3 d-md-block d-none">Schedule a Tour</a>
																			<a role="button" data-toggle="modal" data-event="AgentEmailClick" class="btn btn--prominent HARTracker  mr-0 mr-md-3 btn--small attachedLead-for-STEVENB">Request Info</a>
												
													<a class="align-self-center pl-md-4 pl-3 MG-close-cross d-none d-md-block cursor--pointer" href="javascript:void(0);"><img style="width:16px;" src="https://content.harstatic.com/media/icons/cross_white_small_2.svg"></a>
										
			</div>
		</div>


		<div class="lightbox mg-galleryLoading" style="height: 100%;z-index: 1000;background: #000;padding-top: 50px;">
			<div class="text-center p-2 m-2">
				<svg id="general_loading" width="90px" height="90px" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
					<title>har-loading</title>
					<g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
						<g id="Select-Listings---Default-state" transform="translate(-660.000000, -340.000000)">
							<g id="har-symbol" transform="translate(660.000000, 340.000000)">
								<circle class="circle" fill="" cx="60" cy="60" r="58" pathLength="1"></circle>
								<path class="letter" d="M93.3520203,77.0865641 C93.3520203,73.1660624 90.1374302,70.0064025 86.2470524,70.0064025 C83.411085,70.0064025 80.9675952,70.975031 79.8513152,73.4106875 C78.7343186,70.9752718 76.2910677,70.0064025 73.4551003,70.0064025 C69.5647224,70.0064025 66.3463103,73.1858058 66.3463103,77.106789 L66.3501323,89.5417351 L69.1297244,89.5417351 L69.1297244,78.5776693 C69.1297244,78.2974099 69.1536123,77.6704378 69.1536123,77.6220425 C69.1536123,75.0464971 71.225168,72.9580348 73.780453,72.9580348 C76.3354991,72.9580348 78.4072937,75.0464971 78.4072937,77.6220425 C78.4072937,77.6367296 78.440259,78.2141025 78.440259,78.5047152 L78.440259,89.5417351 L79.129185,89.5417351 L80.5736843,89.5417351 L81.239678,89.5417351 L81.239678,78.5047152 C81.239678,78.3872181 81.2692989,77.7226855 81.2692989,77.6685116 C81.2692989,75.0840576 83.3482599,72.9881314 85.9126223,72.9881314 C88.4769847,72.9881314 90.5784002,75.0924847 90.5784002,77.6771795 C90.5784002,77.762413 90.5731449,78.3901074 90.5731449,78.5776693 L90.5731449,89.5417351 L93.352737,89.5417351 L93.352737,77.0865641 L93.3520203,77.0865641 Z" id="letter-m" fill="#fff"></path>
								<path class="letter" d="M54.493629,70.1682017 C55.8891581,70.1682017 57.1831638,70.4291993 58.3742128,70.9502314 C59.5655007,71.4722266 60.5943511,72.1757644 61.460764,73.0608448 C62.3271769,73.9464067 63.0005758,74.9771546 63.4823941,76.1538107 C63.9637345,77.3304668 64.2042854,78.5856148 64.2042854,79.9197362 L64.2042854,79.9922088 C64.2042854,81.3268118 63.9570459,82.5879791 63.4642393,83.7764331 C62.970716,84.9653686 62.2908673,86.0083959 61.4244544,86.9055149 C60.5580415,87.8033563 59.523458,88.5129134 58.3202262,89.0339455 C57.1165167,89.5554591 55.8170168,89.8164567 54.4212488,89.8164567 C53.0257197,89.8164567 51.7379248,89.5554591 50.5585809,89.0339455 C49.379237,88.5129134 48.3568363,87.8093756 47.4904234,86.9238136 C46.6240105,86.0382517 45.9498949,85.0079854 45.4687933,83.8308477 C44.9869751,82.6546732 44.7466631,81.3990436 44.7466631,80.0649222 L44.7466631,79.991968 C44.7466631,78.6583281 44.9872139,77.3966793 45.4687933,76.2082253 C45.9498949,75.0197713 46.6240105,73.9762625 47.4904234,73.0791435 C48.3568363,72.1813021 49.3856867,71.4722266 50.5772134,70.9502314 C51.7680235,70.4291993 53.0732565,70.1682017 54.493629,70.1682017 Z M54.4214877,72.6423818 C53.4110309,72.6423818 52.4841818,72.8371669 51.6421345,73.2245702 C50.7993706,73.6129365 50.0836901,74.1339686 49.4941376,74.7891111 C48.9043462,75.4442536 48.4411605,76.2144854 48.1045804,77.0995658 C47.7672838,77.9856093 47.5991132,78.9251043 47.5991132,79.9197362 L47.5991132,79.9922088 C47.5991132,81.0111588 47.7732557,81.9636556 48.1224963,82.8484952 C48.4710203,83.7340571 48.9530774,84.5038074 49.5665178,85.1589499 C50.1799582,85.8140923 50.9073437,86.3360875 51.7505854,86.7234908 C52.5923938,87.1118571 53.5065824,87.3056791 54.493629,87.3056791 C55.5040857,87.3056791 56.4302183,87.1118571 57.27346,86.7234908 C58.1155072,86.3360875 58.8373985,85.8140923 59.4391339,85.1589499 C60.0406303,84.5038074 60.5095492,83.7400764 60.8470847,82.8667939 C61.1839036,81.9935114 61.3525519,81.0600357 61.3525519,80.0649222 L61.3525519,79.991968 C61.3525519,78.9734996 61.1774539,78.0214844 60.8291688,77.1361632 C60.4799282,76.2506013 59.9926157,75.4748317 59.3669925,74.8074098 C58.7408916,74.1404695 58.0072952,73.6129365 57.1652479,73.2245702 C56.3222452,72.8371669 55.4082954,72.6423818 54.4214877,72.6423818 Z" id="letter-o" fill="#fff"></path>
								<path class="letter" d="M36.0591241,89.8164567 C34.6874828,89.8164567 33.4176039,89.5554591 32.2509205,89.0339455 C31.0832816,88.5129134 30.072586,87.8093756 29.218356,86.9238136 C28.3641259,86.0382517 27.6959823,85.0014845 27.2153584,83.812549 C26.7333013,82.6245766 26.4932282,81.3747256 26.4932282,80.0651629 L26.4932282,79.9922088 C26.4932282,78.6585689 26.7335402,77.3969201 27.2153584,76.2084661 C27.6959823,75.0200121 28.3641259,73.9765033 29.218356,73.0793843 C30.072586,72.1822652 31.0835205,71.4724674 32.2509205,70.9504722 C33.4178428,70.4294401 34.6874828,70.1684425 36.0591241,70.1684425 C36.9496637,70.1684425 37.7558792,70.253676 38.4777705,70.4229392 C39.1996618,70.5931655 39.8613557,70.8296043 40.4633299,71.1324963 C41.0645875,71.4363515 41.6185471,71.7936581 42.1237755,72.2061017 C42.6292427,72.6185453 43.0984004,73.055307 43.5314874,73.5156644 L41.6904496,75.4803695 C40.9444315,74.7043591 40.132483,74.0376595 39.2541261,73.4793077 C38.3750527,72.9216782 37.2981877,72.6421411 36.0232923,72.6421411 C35.084738,72.6421411 34.2061423,72.8301845 33.3882218,73.2060307 C32.5698235,73.5825992 31.8598762,74.0976119 31.2583797,74.7527544 C30.6564055,75.4074154 30.1874867,76.1776472 29.8504289,77.0632091 C29.5133711,77.948771 29.3454394,78.9005455 29.3454394,79.9194955 L29.3454394,79.991968 C29.3454394,81.0109181 29.519582,81.9634148 29.8685837,82.8482544 C30.2171076,83.7338163 30.6989259,84.5100675 31.3123663,85.1770078 C31.9258067,85.8441889 32.6481757,86.3659433 33.4780402,86.7415487 C34.3086213,87.1176357 35.2168379,87.3054384 36.2036457,87.3054384 C37.4068775,87.3054384 38.4715597,87.038903 39.3986477,86.50511 C40.3243025,85.9717985 41.1608556,85.3046173 41.9073514,84.5038074 L43.676009,86.1778999 C42.7374547,87.245486 41.6725337,88.1187685 40.4812458,88.7972659 C39.2897191,89.4764857 37.81536,89.8164567 36.0591241,89.8164567" id="letter-c" fill="#fff"></path>
								<path class="letter" d="M89.2209775,32.4064408 C91.4933137,32.4064408 93.3610498,32.6650264 94.8239457,33.1812434 C96.2863617,33.6984146 97.4648856,34.4014144 98.3597575,35.2904813 C99.1332014,36.0600358 99.7134642,36.925248 100.100546,37.8856406 C100.486668,38.847226 100.680809,39.9769874 100.680809,41.3467278 C100.680809,43.1976186 100.233253,44.7593897 99.3388611,46.0327568 C98.4439892,47.3070781 97.2229895,48.3161345 95.6758618,49.0613572 L95.6758618,49.0613572 L101.5872,57.6673626 L91.9046332,57.6673626 L87.0451117,50.4308589 L85.3043232,50.4308589 L85.3043232,57.6673626 L76.7363296,57.6673626 L76.7363296,32.4073626 Z M89.0033189,39.4722207 L85.3043232,39.4722207 L85.3043232,44.4108717 L88.9668425,44.4108717 C89.9824225,44.4108717 90.7801039,44.1942705 91.3603668,43.7620222 C91.9406296,43.3292969 92.2307611,42.7291111 92.2307611,41.9590794 L92.2307611,41.9590794 L92.2307611,41.8875152 C92.2307611,41.0707282 91.9406296,40.463386 91.3603668,40.0666813 C90.7801039,39.6702152 89.9941813,39.4722207 89.0033189,39.4722207 L89.0033189,39.4722207 Z" id="letter-r" fill="#fff"></path>
								<path class="letter" d="M60.1452007,32.4 C61.5673006,32.4 62.9380457,32.6120688 64.2334381,32.9973232 L64.2334381,32.9973232 L64.2490366,33.0347752 L75.018456,57.5984987 L68.0857787,57.5984987 C66.5103339,54.8625963 63.5471057,53.0181462 60.1485603,53.0181462 C56.750255,53.0181462 53.7872668,54.8625963 52.2118219,57.5984987 L52.2118219,57.5984987 L45.3242602,57.5984987 L55.4781403,34.3074266 L55.4671014,34.306711 L56.0248064,33.0042411 C57.3295579,32.613023 58.7120618,32.4 60.1452007,32.4 Z M60.1562396,41.3087987 C60.1562396,41.3087987 58.141398,46.0222607 57.6780036,47.1164786 C58.4831244,46.9828919 59.309363,46.9098963 60.1531199,46.9098963 C60.9997565,46.9098963 61.828155,46.9828919 62.6356754,47.1181484 C62.4620285,46.7055565 62.0306662,45.6936185 61.5755136,44.6277954 L61.28194,43.9405677 C60.6974453,42.5727349 60.1564864,41.3090441 60.1562396,41.3087987 Z" id="letter-a" fill="#fff"></path>
								<polygon class="letter" id="letter-h" fill="#fff" points="34.9664001 32.4064408 34.9664001 43.2188493 27.2058044 43.2188493 27.2058044 32.4064408 27.2070042 32.4064408 18.7211999 32.4064408 18.72 32.4064408 18.72 57.6404831 27.2058044 57.6404831 27.2058044 50.5009919 34.9664001 50.5009919 34.9664001 57.6404831 43.4524445 57.6404831 43.4524445 32.4064408"></polygon>
								<circle id="red-dot" fill="#FD4B78" cx="60.12" cy="62.52" r="5.64"></circle>
							</g>
						</g>
					</g>
				</svg>
				<div id="loadertext" class="color_snow_white font_size--small pt-5">Loading..</div>
			</div>
		</div>

		

		<!-- this tabs will display only on smaller devices -->
		
				<div class="resp_tabs_cntr" style="display: none`;">
			<ul class="nav nav-tabs nav-tabs--negative gallery_top_tabs mt-auto mb-0" id="myTab" role="tablist">
								<li class="nav-item">
					<a class="nav-link photo-mg-tab photo-Tab active resp-mg-tab" id="photo-tab" data-toggle="tab" href="#photo-mg-tab" role="tab" aria-controls="Photos" aria-selected="1">Photos</a>
				</li>
								<li class="nav-item">
					<a class="nav-link audio-mg-tab  resp-mg-tab" id="audio-tab" data-toggle="tab" href="#audio-mg-tab" role="tab" aria-controls="Audio Tour" aria-selected="">Audio Tour</a>
				</li>
								<li class="nav-item">
					<a class="nav-link StreetlViewTabC  resp-mg-tab" id="streetview-tab" data-toggle="tab" href="#streetview-mg-tab" role="tab" aria-controls="Street View" aria-selected="">Street View</a>
				</li>
								<li class="nav-item">
					<a class="nav-link AerialViewTabA  resp-mg-tab" id="aerialView-tab" data-toggle="tab" href="#aerialView-mg-tab" role="tab" aria-controls="Aerial View" aria-selected="">Aerial View</a>
				</li>
							</ul>
		</div>
		
		




		<!-- all Photos Ended here -->

		<!-- Full view photo starts here -->
		<div class="photo_full_view_content" >
			<div class="tab-content" id="myTabContent">
				<div class="tab-pane flex-grow-1 position-relative fade show active color_snow_white" id="photo-mg-tab" role="tabpanel" aria-labelledby="photo-mg-tab">
					<div class="spotlight-container" style="display:none">
						<div class="jrx-slider">
							<div class="swiper mySwiper" style="touch-action:auto !important;">
								<div class="swiper-wrapper">
								</div>
							</div>
							<div class="jrxArrow swiper-button-prev prev"></div>
							<div class="jrxArrow swiper-button-next next"></div>
						</div>
						<div class="jrx_slider_thumbs opened">
							<div class="position-relative w-100">
								<div class="position-absolute pt-2" style="left:10px;top:20px;">
									<a class="font-size--large d-none cursor--hand">
										<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-play-circle" viewBox="0 0 16 16">
											<path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14m0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16" />
											<path d="M6.271 5.055a.5.5 0 0 1 .52.038l3.5 2.5a.5.5 0 0 1 0 .814l-3.5 2.5A.5.5 0 0 1 6 10.5v-5a.5.5 0 0 1 .271-.445" />
										</svg> <span class="pl-2 pt-1">Auto Slide</span>
									</a>
								</div>
								<div class="position-absolute pt-2 d-none" style="right:20px;top:20px;">
									<span id="Mg--photo--on--spot"></span> / <span id="Mg--photo--total--count"></span>
								</div>
							</div>
							<div class="slider_arrow"><img src="https://content.harstatic.com/media/icons/listing_details/up_2_arrows.svg">
							</div>
							<div class="jrx_slider_thumbs_slider">
								<div class="jrx_slider_thumbs_slider_scroll"></div>
							</div>
						</div>
					</div>
					<div class="all_photos_container">
												<div class="info_block">			
							<div><img style="width:22px;" src="https://content.harstatic.com/media/icons/a_all/magic_wand_white.svg"></div>
							<div class="pl-2">Redesign </div>
						</div>
												<ul class="ph_gallery_cntr">

						</ul>
					</div>

				</div><!-- photos tab ended -->
				<div class="tab-pane fade color_snow_white" id="video-mg-tab" role="tabpanel" aria-labelledby="video-mg-tab">
					<div class="pt-5  justify-content-center row video-mg-container">

					</div>
				</div>
								<div class="tab-pane fade color_snow_white" id="audio-mg-tab" role="tabpanel">
					<div class="pt-5 container audio-mg-container">
						<style>
	.controls__total-time {
		display: none;
	}
	
</style>

<style>
	.for_dark_theme_color {
		color: #fff !important;
	}
	</style>
	<div class="mt-md-5 mt-md-4 mt-3 pt-md-3 ml-auto mr-auto p-md-0 p-4" style="max-width:900px;">
				    <div class="border-bottom border-color--cloudy-sky-light pb-5 mb-5">
						<div class="row">
							<div class="col-auto align-self-center"><h3 class="text-left for_dark_theme_color" style="color:#fff !important;" tabindex="0">Listen to the Audio Tour <img class="ml-2" style="width:24px;" src="https://content.harstatic.com/media/icons/audio_tour_blue.svg"></h3></div>
							<div class="col text-right pb-3 pt-2 d-none d-md-block">
								
							</div>
						</div>					
						<p id="mvAudiospeech-description" style="display:none">4017 Robertson St is listed for sale at $ 998,500. This is a Single-Family property. 940 square feet and was built in 1930. The lot size is 26933 Square feet. ##BR##Expand your investment portfolio with this incredible opportunity! Located just minutes from downtown, this property comes with preliminary proposed plans for a development featuring 16 patio homes with floor plans included. Currently, the site boasts multiple single-family, income-producing rental units. Contact us today for the latest rent rolls and more details!</p>
						<div class="row">
							<div class="col-md col-12">
								<div class="player_container green_theme">
												
								<div class="media-gallery-audio-player">
									<audio id="media-gallery-audioElem" crossorigin preload="none">
										<source id="media-gallery-audioControl" src="" type="audio/mpeg">
									</audio>
								</div>
																</div>
							</div>
							<div class="col-md-auto col-12 align-self-center d-none d-md-block">
																<div class="dropdown dropdown--quickselect changevalue__single d-inline-block mb-0">
								  <button class="btn btn--ordinary btn--small dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" value="Mid / High-Rise"><span class="MG-selectedLanguage">Choose from 12 Languages</span></button>
								  <div class="dropdown-menu respnsive_right depth depth--above_all" aria-labelledby="dropdownMenuButton" style="">
								 	<a class="dropdown-item MG-speech-translation" href="#" data-language="ar">Arabic</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="zh-CN">Cantonese</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="en">English</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="fr">French </a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="de">German</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="hi">Hindi</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="it">Italian</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="ja">Japanese</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="ko">Korean</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="zh-TW">Mandarin</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="pt">Portuguese</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="es">Spanish</a>
								  </div>
								</div>
																<button role="button" class="btn btn--primary btn--small color_snow_white mb-0 audio-share" aria-label="Search"><img style="width:16px;" class="mr-2" src="https://content.harstatic.com/media/icons/icon-share-white.svg"><span>Share</span></button>
							</div>
						</div>
						
					
						<div class="pb-3 pt-3 d-block d-md-none">
														<div class="dropdown dropdown--quickselect changevalue__single d-inline-block">
							  <button class="btn btn--ordinary btn--small dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" value="Mid / High-Rise"><span class="MG-selectedLanguage">Choose from 12 Languages</span></button>
							  <div class="dropdown-menu respnsive_right depth depth--above_all" aria-labelledby="dropdownMenuButton" style="">
							  <a class="dropdown-item MG-speech-translation" href="#" data-language="ar">Arabic</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="zh-CN">Cantonese</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="en">English</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="fr">French </a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="de">German</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="hi">Hindi</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="it">Italian</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="ja">Japanese</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="ko">Korean</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="zh-TW">Mandarin</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="pt">Portuguese</a>
									<a class="dropdown-item MG-speech-translation" href="#" data-language="es">Spanish</a>
								  </div>
							</div>
														
							<button role="button" class="btn btn--primary btn--small color_snow_white mb-0 audio-share" aria-label="Search"><img style="width:16px;" class="mr-2" src="https://content.harstatic.com/media/icons/icon-share-white.svg"><span>Share</span></button>
						</div>
			  
				    </div>
					    
						<div class="d-none">
							<h3 class="mb-3" tabindex="0">Social Networks</h3>
							<div class="pb-4 color_auxiliary">Choose Social Network you want to share on.</div>
							<a href="https://www.facebook.com/sharer.php?u=https%3A%2F%2Fwww.har.com%2Fs%2FOQ8VA46Ve9b" target="_blank" class="hover-opacity"><img class="m-2" style="width:56px;" src="https://content.harstatic.com/media/icons/social/social_icon_facebook.svg" alt="Facebook"></a>
							<a href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fwww.har.com%2Fs%2FOQ8VA46Ve9b&amp;text=Share information from HAR.com" target="_blank" class="hover-opacity"><img class="m-2" style="width:56px;" src="https://content.harstatic.com/media/icons/social/social_icon_twitter.svg" alt="Twitter"></a>
							<a href="https://www.instagram.com/?url=https%3A%2F%2Fwww.har.com%2Fs%2FOQ8VA46Ve9b" target="_blank" class="hover-opacity" style="display:none;"><img class="m-2" style="width:56px;" src="https://content.harstatic.com/media/icons/social/social_icon_svginstagram.svg" alt="Instagram"></a>
							<a href="https://www.linkedin.com/sharing/share-offsite/?url=https%3A%2F%2Fwww.har.com%2Fs%2FOQ8VA46Ve9b" target="_blank" class="hover-opacity"><img class="m-2" style="width:56px;" src="https://content.harstatic.com/media/icons/social/social_icon_linkedin.svg" alt="Linkedin"></a>
							<a href="https://pinterest.com/pin/create/bookmarklet/?url=https%3A%2F%2Fwww.har.com%2Fs%2FOQ8VA46Ve9b" target="_blank" class="hover-opacity"><img class="m-2" style="width:56px;" src="https://content.harstatic.com/media/icons/social/pinterest.svg" alt="pinterest"></a>
							<a href="mailto:?body=Check out this site:https://www.har.com/s/OQ8VA46Ve9b" target="_blank" class="hover-opacity"><img class="m-2" style="width:56px;" src="https://content.harstatic.com/media/icons/social/social_icon_email.svg" alt="email"></a>
							<a href="https://reddit.com/submit?url=https%3A%2F%2Fwww.har.com%2Fs%2FOQ8VA46Ve9b" target="_blank" class="hover-opacity"><img class="m-2" style="width:56px;" src="https://content.harstatic.com/media/icons/social/social_icon_reddit.svg" alt="reddit"></a>
						</div>
				  </div>

					</div>
				</div>
								
												<div class="tab-pane fade media-street-view" id="streetview-mg-tab" style="position: relative; height:calc(100vh - 56px);" role="tabpanel" aria-labelledby="Street-tab">
					<div id="gallery-street-view"></div><!-- Use as reference -->




<script type="text/javascript">
    Object.defineProperty(window, "galleryStreetView", {
        configurable:true,
        get:function() {
            var value = StreetViewComponent.register("gallery-street-view", "29.797257000000000", "-95.357419000000000");
            Object.defineProperty(window, "galleryStreetView", { value });
            return value;
        }
    })
</script>				</div>
				
								<div class="tab-pane fade" id="aerialView-mg-tab" data-latitude="29.797257000000000" data-longitude="-95.357419000000000" role="tabpanel" aria-labelledby="aerialView-mg-tab">
					<div id="arialviewmap" style="height: 100vh"></div>
				</div>
				<!-- {-- ArialView tab --} -->
				
			</div>
		</div>
		<!-- Full view photo starts here -->

				<!-- Full view videos starts here -->
		<div class="video_content" style="display: none;">
			<div class="m-md-5 m-4">
				<div class="row justify-content-center ml-auto mr-auto" style="max-width:600px;">
					<div class="col-lg-6 col-md-6 col-12 pb-4 pb-md-0">
						<div class="card card--hover p-1 h-100 mb-0">
							<a href="javascript:void(0);" data-video="Video_1" class="open_video play_image border_radius--default text-center" style="background-image: url('https://photos.harstatic.com/181946852/hr/img-3.jpeg?ts=2020-01-31T15:32:04.560');">
								<div class="video_overlay"><img src="https://content.harstatic.com/media/icons/icon-play-video-gallery.svg">
								</div>
							</a>
							<a href="javascript:void(0);" data-video="Video_1" class="open_video d-block pb-md-4 pb-2 pt-md-4 pt-2 pl-2 pr-2 font_weight--bold font_size--large font_size--medium_sm text-center text-decoration-none">Video
								title will be here</a>
						</div>
					</div>
					<div class="col-lg-6 col-md-6 col-12 pb-4 pb-md-0">
						<div class="card card--hover p-1 h-100 mb-0">
							<a href="javascript:void(0);" data-video="Video_2" class="open_video play_image border_radius--default text-center" style="background-image: url('https://photos.harstatic.com/181946852/hr/img-1.jpeg?ts=2020-01-29T17:46:09.740');">
								<div class="video_overlay"><img src="https://content.harstatic.com/media/icons/icon-play-video-gallery.svg">
								</div>
							</a>
							<a href="javascript:void(0);" data-video="Video_2" class="open_video d-block pb-md-4 pb-2 pt-md-4 pt-2 pl-2 pr-2 font_weight--bold font_size--large font_size--medium_sm text-center text-decoration-none">Video
								title will be here</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Full view videos ended here -->
			</div>




	<!-- full screen image with slider starts here -->
	<div class="video_full_pre_view">

	</div>


</div>



</div>
<!-- gallery html stops here -->
    <script  async defer  type="text/javascript" src="/js/myaccount.js?v=*********"></script>
    <script>
        if(typeof myHAR == 'undefined')
            myHAR = {};
            myHAR.login_type = 'guest';
                    harload.configure({
            environment:'production',
            buildNumber:"*********"         });
    </script>
        <script>$.ajax({ url: "/api/detail/status", data: { kt:"523e855ccea8a1c3199492824d0f23f7", lid: 9664623, stat: 5 }, xhrFields: { withCredentials: true  }});</script>
<script type="text/javascript" src="/js/leads.js?v=*********"></script>
<script language='javascript' src="/js/form_thin_min.js"></script>

    







<div id="CurrencyConverter" class="modal fade modal--small" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenteredScrollableTitle" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-dialog-scrollable modal-sm modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 tabindex="0" class="modal-title" id="exampleModalCenteredScrollableTitle">Currency Converter</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close"></button>
      </div>
      <div class="modal-body text-left">
        <div class="border-bottom pb-4 mb-4">
          <label for="CurrencyOptions">Currency</label>
          <input id="USDamount" type="hidden" value="" />
          <select name="CurrencyOptions" id="CurrencyOptions" class="custom-select custom-select_large pb-2" onchange="convertToCurrency();">
            <option value="">Convert Listing Price To</option>
         
          </select>
        </div>
        <div id="sourceCurrencyDiv" class="font_size--large font_weight--semi_bold pt-2">$<span id="USDamountformatted"></span> US Dollar is equal to</div>
        <div id="targetCurrencyDiv" class="font_size--large_extra_extra pb-3 pt-4">
          <div class="font_weight--bold" id="targetCurrencyValue"></div>
          <div class="pb-2" id="targetCurrencyName"></div>
        </div>
      </div>
      <div class="modal-footer color_slate_light font_size--small pl-5 pt-3 pb-3 pr-3">
        <div class="text-left w-100 pl-2">Source: xe.com</div>
      </div>
    </div>
  </div>
</div>
<script>
  function showHideCurrencyResult() {
    $("#sourceCurrencyDiv").show();
    $("#targetCurrencyDiv").show();
    if ($("#CurrencyOptions").prop('selectedIndex') == 0 ) {
      $("#sourceCurrencyDiv").hide();
      $("#targetCurrencyDiv").hide();
    }
  }
  $(document).ready(function() {

  });

  function convertToCurrency() {
    amount = $('#USDamount').val()
    showHideCurrencyResult();
    if ($("#CurrencyOptions").prop('selectedIndex') > 0 || localStorage.setItem("currency")) {
      var targetCurrencySymbol = $("#CurrencyOptions option:selected").text().split("(")[1].substring(0, 3);
      var total = parseFloat(amount) * parseFloat($("#CurrencyOptions").val());
      localStorage.setItem("currency", $("#CurrencyOptions").val());
      $("#targetCurrencyName").html($("#CurrencyOptions option:selected").text());
      $("#targetCurrencyValue").html(formatCurrency(total) + " " + targetCurrencySymbol);
    }
  }
  function getAllCurrencies(){
    $.ajax({
        url: '/api/getCurrencyCode',
        type: "GET",
        datatype: "json",
        success: function(json) {
             var items = "";
              $.each( json, function( key, obj ) {
                selected = (obj.rate == localStorage.getItem("currency")) ? 'selected="selected"' : ''
                items += "<option value='" + obj.rate + "' "+selected+" >" + obj.country_name + " ("+ obj.country_code + ")</option>";
              });
              $("#CurrencyOptions").append(items);
              setTimeout(function(){
                convertToCurrency()
              }, 500);
        }
    })
  } 
</script>
<div class="lightbox" style="display:none" id="lightbox">
  <div class="text-center p-5 m-5">
   <svg id="general_loading" width="90px" height="90px" viewBox="0 0 120 120" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <title>har-symbol</title>
        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="Select-Listings---Default-state" transform="translate(-660.000000, -340.000000)">
                <g id="har-symbol" transform="translate(660.000000, 340.000000)">

                    <circle class="circle" fill="" cx="60" cy="60" r="58" pathLength="1"></circle>

                    <path class="letter" d="M93.3520203,77.0865641 C93.3520203,73.1660624 90.1374302,70.0064025 86.2470524,70.0064025 C83.411085,70.0064025 80.9675952,70.975031 79.8513152,73.4106875 C78.7343186,70.9752718 76.2910677,70.0064025 73.4551003,70.0064025 C69.5647224,70.0064025 66.3463103,73.1858058 66.3463103,77.106789 L66.3501323,89.5417351 L69.1297244,89.5417351 L69.1297244,78.5776693 C69.1297244,78.2974099 69.1536123,77.6704378 69.1536123,77.6220425 C69.1536123,75.0464971 71.225168,72.9580348 73.780453,72.9580348 C76.3354991,72.9580348 78.4072937,75.0464971 78.4072937,77.6220425 C78.4072937,77.6367296 78.440259,78.2141025 78.440259,78.5047152 L78.440259,89.5417351 L79.129185,89.5417351 L80.5736843,89.5417351 L81.239678,89.5417351 L81.239678,78.5047152 C81.239678,78.3872181 81.2692989,77.7226855 81.2692989,77.6685116 C81.2692989,75.0840576 83.3482599,72.9881314 85.9126223,72.9881314 C88.4769847,72.9881314 90.5784002,75.0924847 90.5784002,77.6771795 C90.5784002,77.762413 90.5731449,78.3901074 90.5731449,78.5776693 L90.5731449,89.5417351 L93.352737,89.5417351 L93.352737,77.0865641 L93.3520203,77.0865641 Z" id="letter-m" fill="#fff"></path>
                    <path class="letter" d="M54.493629,70.1682017 C55.8891581,70.1682017 57.1831638,70.4291993 58.3742128,70.9502314 C59.5655007,71.4722266 60.5943511,72.1757644 61.460764,73.0608448 C62.3271769,73.9464067 63.0005758,74.9771546 63.4823941,76.1538107 C63.9637345,77.3304668 64.2042854,78.5856148 64.2042854,79.9197362 L64.2042854,79.9922088 C64.2042854,81.3268118 63.9570459,82.5879791 63.4642393,83.7764331 C62.970716,84.9653686 62.2908673,86.0083959 61.4244544,86.9055149 C60.5580415,87.8033563 59.523458,88.5129134 58.3202262,89.0339455 C57.1165167,89.5554591 55.8170168,89.8164567 54.4212488,89.8164567 C53.0257197,89.8164567 51.7379248,89.5554591 50.5585809,89.0339455 C49.379237,88.5129134 48.3568363,87.8093756 47.4904234,86.9238136 C46.6240105,86.0382517 45.9498949,85.0079854 45.4687933,83.8308477 C44.9869751,82.6546732 44.7466631,81.3990436 44.7466631,80.0649222 L44.7466631,79.991968 C44.7466631,78.6583281 44.9872139,77.3966793 45.4687933,76.2082253 C45.9498949,75.0197713 46.6240105,73.9762625 47.4904234,73.0791435 C48.3568363,72.1813021 49.3856867,71.4722266 50.5772134,70.9502314 C51.7680235,70.4291993 53.0732565,70.1682017 54.493629,70.1682017 Z M54.4214877,72.6423818 C53.4110309,72.6423818 52.4841818,72.8371669 51.6421345,73.2245702 C50.7993706,73.6129365 50.0836901,74.1339686 49.4941376,74.7891111 C48.9043462,75.4442536 48.4411605,76.2144854 48.1045804,77.0995658 C47.7672838,77.9856093 47.5991132,78.9251043 47.5991132,79.9197362 L47.5991132,79.9922088 C47.5991132,81.0111588 47.7732557,81.9636556 48.1224963,82.8484952 C48.4710203,83.7340571 48.9530774,84.5038074 49.5665178,85.1589499 C50.1799582,85.8140923 50.9073437,86.3360875 51.7505854,86.7234908 C52.5923938,87.1118571 53.5065824,87.3056791 54.493629,87.3056791 C55.5040857,87.3056791 56.4302183,87.1118571 57.27346,86.7234908 C58.1155072,86.3360875 58.8373985,85.8140923 59.4391339,85.1589499 C60.0406303,84.5038074 60.5095492,83.7400764 60.8470847,82.8667939 C61.1839036,81.9935114 61.3525519,81.0600357 61.3525519,80.0649222 L61.3525519,79.991968 C61.3525519,78.9734996 61.1774539,78.0214844 60.8291688,77.1361632 C60.4799282,76.2506013 59.9926157,75.4748317 59.3669925,74.8074098 C58.7408916,74.1404695 58.0072952,73.6129365 57.1652479,73.2245702 C56.3222452,72.8371669 55.4082954,72.6423818 54.4214877,72.6423818 Z" id="letter-o" fill="#fff"></path>
                    <path class="letter" d="M36.0591241,89.8164567 C34.6874828,89.8164567 33.4176039,89.5554591 32.2509205,89.0339455 C31.0832816,88.5129134 30.072586,87.8093756 29.218356,86.9238136 C28.3641259,86.0382517 27.6959823,85.0014845 27.2153584,83.812549 C26.7333013,82.6245766 26.4932282,81.3747256 26.4932282,80.0651629 L26.4932282,79.9922088 C26.4932282,78.6585689 26.7335402,77.3969201 27.2153584,76.2084661 C27.6959823,75.0200121 28.3641259,73.9765033 29.218356,73.0793843 C30.072586,72.1822652 31.0835205,71.4724674 32.2509205,70.9504722 C33.4178428,70.4294401 34.6874828,70.1684425 36.0591241,70.1684425 C36.9496637,70.1684425 37.7558792,70.253676 38.4777705,70.4229392 C39.1996618,70.5931655 39.8613557,70.8296043 40.4633299,71.1324963 C41.0645875,71.4363515 41.6185471,71.7936581 42.1237755,72.2061017 C42.6292427,72.6185453 43.0984004,73.055307 43.5314874,73.5156644 L41.6904496,75.4803695 C40.9444315,74.7043591 40.132483,74.0376595 39.2541261,73.4793077 C38.3750527,72.9216782 37.2981877,72.6421411 36.0232923,72.6421411 C35.084738,72.6421411 34.2061423,72.8301845 33.3882218,73.2060307 C32.5698235,73.5825992 31.8598762,74.0976119 31.2583797,74.7527544 C30.6564055,75.4074154 30.1874867,76.1776472 29.8504289,77.0632091 C29.5133711,77.948771 29.3454394,78.9005455 29.3454394,79.9194955 L29.3454394,79.991968 C29.3454394,81.0109181 29.519582,81.9634148 29.8685837,82.8482544 C30.2171076,83.7338163 30.6989259,84.5100675 31.3123663,85.1770078 C31.9258067,85.8441889 32.6481757,86.3659433 33.4780402,86.7415487 C34.3086213,87.1176357 35.2168379,87.3054384 36.2036457,87.3054384 C37.4068775,87.3054384 38.4715597,87.038903 39.3986477,86.50511 C40.3243025,85.9717985 41.1608556,85.3046173 41.9073514,84.5038074 L43.676009,86.1778999 C42.7374547,87.245486 41.6725337,88.1187685 40.4812458,88.7972659 C39.2897191,89.4764857 37.81536,89.8164567 36.0591241,89.8164567" id="letter-c" fill="#fff"></path>
                    <path class="letter" d="M89.2209775,32.4064408 C91.4933137,32.4064408 93.3610498,32.6650264 94.8239457,33.1812434 C96.2863617,33.6984146 97.4648856,34.4014144 98.3597575,35.2904813 C99.1332014,36.0600358 99.7134642,36.925248 100.100546,37.8856406 C100.486668,38.847226 100.680809,39.9769874 100.680809,41.3467278 C100.680809,43.1976186 100.233253,44.7593897 99.3388611,46.0327568 C98.4439892,47.3070781 97.2229895,48.3161345 95.6758618,49.0613572 L95.6758618,49.0613572 L101.5872,57.6673626 L91.9046332,57.6673626 L87.0451117,50.4308589 L85.3043232,50.4308589 L85.3043232,57.6673626 L76.7363296,57.6673626 L76.7363296,32.4073626 Z M89.0033189,39.4722207 L85.3043232,39.4722207 L85.3043232,44.4108717 L88.9668425,44.4108717 C89.9824225,44.4108717 90.7801039,44.1942705 91.3603668,43.7620222 C91.9406296,43.3292969 92.2307611,42.7291111 92.2307611,41.9590794 L92.2307611,41.9590794 L92.2307611,41.8875152 C92.2307611,41.0707282 91.9406296,40.463386 91.3603668,40.0666813 C90.7801039,39.6702152 89.9941813,39.4722207 89.0033189,39.4722207 L89.0033189,39.4722207 Z" id="letter-r" fill="#fff"></path>
                    <path class="letter" d="M60.1452007,32.4 C61.5673006,32.4 62.9380457,32.6120688 64.2334381,32.9973232 L64.2334381,32.9973232 L64.2490366,33.0347752 L75.018456,57.5984987 L68.0857787,57.5984987 C66.5103339,54.8625963 63.5471057,53.0181462 60.1485603,53.0181462 C56.750255,53.0181462 53.7872668,54.8625963 52.2118219,57.5984987 L52.2118219,57.5984987 L45.3242602,57.5984987 L55.4781403,34.3074266 L55.4671014,34.306711 L56.0248064,33.0042411 C57.3295579,32.613023 58.7120618,32.4 60.1452007,32.4 Z M60.1562396,41.3087987 C60.1562396,41.3087987 58.141398,46.0222607 57.6780036,47.1164786 C58.4831244,46.9828919 59.309363,46.9098963 60.1531199,46.9098963 C60.9997565,46.9098963 61.828155,46.9828919 62.6356754,47.1181484 C62.4620285,46.7055565 62.0306662,45.6936185 61.5755136,44.6277954 L61.28194,43.9405677 C60.6974453,42.5727349 60.1564864,41.3090441 60.1562396,41.3087987 Z" id="letter-a" fill="#fff"></path>
                    <polygon class="letter" id="letter-h" fill="#fff" points="34.9664001 32.4064408 34.9664001 43.2188493 27.2058044 43.2188493 27.2058044 32.4064408 27.2070042 32.4064408 18.7211999 32.4064408 18.72 32.4064408 18.72 57.6404831 27.2058044 57.6404831 27.2058044 50.5009919 34.9664001 50.5009919 34.9664001 57.6404831 43.4524445 57.6404831 43.4524445 32.4064408"></polygon>
                    <circle id="red-dot" fill="#FD4B78" cx="60.12" cy="62.52" r="5.64"></circle>
                </g>
            </g>
        </g>
    </svg>
    <div id="loadertext" class="color_snow_white font_size--small pt-5">Loading</div>
    </div>
</div>
<div id="LargeMapModal2" class="modal fade modal--full-width" tabindex="-1" role="dialog" aria-labelledby="AVMCompareHomeValue" style="display: none;" aria-hidden="true" style="margin:30px 0px 40px 0px;">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
      <div class="modal-content" style="min-height:calc(100vh - 70px);">
        <div class="modal-header p-0 h-auto">
          <h3 class="mt-3 ml-4 mb-3">Street View</h3>
          <button type="button" class="close mt-0" style="padding: 1.2rem;right: 15px;" data-dismiss="modal" aria-label="Close"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close"></button>
        </div>
        <div class="modal-body p-0">
            <div class="map mapboxgl-map position-absolute" style="top:0px; bottom:0px;left:0px;right:0px;">
			<iframe id="LargeMapStreetView" data-src="https://www.har.com/streetview.php?lat=29.797257000000000&amp;lng=-95.357419000000000&height=100%&color=black" width="100%" height="100%" frameborder="0" sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-top-navigation"
                        allowtransparency="true"></iframe>
         </div>
        </div>
      </div>
    </div>
</div>
	
<div class="menu_overlay"></div>
<script src="/js/typeahead.bundle.min.js?v=*********"></script>
<script src="/js/popper.min.js"></script>
<script src="/js/share.js?v=*********"></script>


<script src="/plugins/har-lazy/jquery.lazy.bundle.min.js"></script>
<script src="/js/helpers.js?v=*********"></script>
<script>
(function () {
    window.onpageshow = function(event) {
        if (event.persisted) {
            window.location.reload();
        }
    };
})();
$(window).on('popstate', function(event) {
	if(window.location.href.indexOf("homedetail") > -1){	
	} else{
		window.location = window.location.href;
	}
});
</script>
<script src="/plugins/audioplayer/green-audio-player.min.js"></script>

	<script src="https://tracking.listhub.net/scripts/la.js" type="text/javascript"></script>
	<script type="text/javascript">
		function ListHubTrack(Type) {
			try {
				var _lh = new ListHubTracker({
					provider: "M-2143",
					test: false
				});
				_lh.submit(Type, "74257223");
			} catch (err) {}


		}

	</script>
		<script>
			function OpenStreetView() {
				if(window.StreetView) {
					ReactiveMap.initialize();
					$('#LargeMapModal').modal('show');
					window.StreetView(true);
					return;
				}

					if(!$('#LargeMapStreetView')[0].hasAttribute('src')){
						$('#LargeMapStreetView').attr('src',$('#LargeMapStreetView').data('src'))
						setTimeout(function(){
							$('#LargeMapModal2').modal('show');
						},500);
					} else {
						$('#LargeMapModal2').modal('show');
					}
	       }
	</script>
	
	<script src="/js/leads.js?v=*********"></script>
	<script type="text/javascript" src="https://www.google.com/jsapi"></script>
		<script>
		window.addEventListener('touchmove',event => {
			//console.log(event)
			//event.preventDefault()
		},{passive:true})
	</script>

			<script>
			function OpenShare($param1, $param2) {
				share.app_id = $param1
								share.listing_id = $param2
								share.message = ''
				share.noads = "1"
				share.init();
				$('#SharingPopup').modal('show')

			}
			;
			;
			$('.listing_detail_cntr').scroll(function() {
							});
		</script>

				<!-- <script src="/maps/harmap.js"></script> -->
<script>
		window.yearly_drawn = false;
		window.monthly_drawn = false;
        if (typeof window['google'] == 'undefined' || typeof window['google']['visualization'] == 'undefined' ) {

            jQuery.getScript("https://www.gstatic.com/charts/loader.js")
                .done(function() {
                    google.charts.load('current', {packages: ['corechart','line']});
                    google.charts.setOnLoadCallback(drawogreCharts);
                    if($("#chart_div").length ==1 && typeof drawChart =="function")
                        google.charts.setOnLoadCallback(drawChart);
            });
        } else  {
                drawogreCharts();
                drawogreYearlyCharts();
                //google.charts.setOnLoadCallback(drawogreYearlyCharts);
        }
        function drawogreCharts() {
                        if($("#EvergyOgre").length > 0)
            {
                window.ogredata = new google.visualization.DataTable();
                window.ogredata.addColumn('string', 'X');
                window.ogredata.addColumn('number', 'Average Market Cost');
                window.ogredata.addColumn({'type': 'string', 'role': 'tooltip', 'p': {'html': true}});
                window.ogredata.addColumn('number', 'Energy Ogre Member Cost');
                window.ogredata.addColumn({'type': 'string', 'role': 'tooltip', 'p': {'html': true}});

                var ogmonthly = [];
                                                                     ogmonthly.unshift(['Apr',126.4300,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$126.43 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $7.43 Higher</div></div>',119.0000,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$119.00 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$7.43 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Mar',118.7400,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$118.74 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $7.63 Higher</div></div>',111.1100,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$111.11 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$7.63 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Feb',105.4000,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$105.40 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $6.97 Higher</div></div>',98.4300,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$98.43 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$6.97 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Jan',117.1400,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$117.14 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $8.49 Higher</div></div>',108.6500,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$108.65 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$8.49 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Dec',114.6400,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$114.64 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $8.70 Higher</div></div>',105.9400,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$105.94 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$8.70 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Nov',115.1100,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$115.11 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $8.81 Higher</div></div>',106.3000,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$106.30 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$8.81 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Oct',153.3000,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$153.30 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $11.45 Higher</div></div>',141.8500,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$141.85 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$11.45 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Sep',197.0900,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$197.09 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $15.03 Higher</div></div>',182.0600,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$182.06 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$15.03 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Aug',240.6100,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$240.61 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $21.23 Higher</div></div>',219.3800,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$219.38 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$21.23 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Jul',240.1500,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$240.15 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $27.07 Higher</div></div>',213.0800,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$213.08 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$27.07 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['Jun',211.6100,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$211.61 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $25.69 Higher</div></div>',185.9200,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$185.92 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$25.69 Savings</div></div></div>']);
                                                     ogmonthly.unshift(['May',168.5900,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$168.59 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $22.26 Higher</div></div>',146.3300,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$146.33 &nbsp;Monthly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$22.26 Savings</div></div></div>']);
                                                                var maxcount = 1;
                                window.ogredata.addRows(ogmonthly);
                var isSmallScreen = window.innerWidth <= 768;
                var options = {
                    title: ' ',
                    tooltip: {isHtml: true},
                    // height: '100%',
                    height: isSmallScreen ? 300 : '100%',
                    width: '100%',
                    // tooltip: {isHtml: true},
                    chartArea: {
                    height: '100%',
                    width: '100%',
                    top: 32,
                    left: 50,
                    bottom: 32,
                    // right: 130
                    right: isSmallScreen ? 20 : 130
                    },
                    hAxis: {slantedText: false, title:'',},
                    vAxis: { maxValue: maxcount,format: '$#,###'},
                    bar: {groupWidth: "45%"},
                    colors: ['#0738cd', '#8cc63f', ],
                    // legend: { position: 'right' }
                    legend: { position: isSmallScreen ? 'top' : 'right' }
                };

                window.ogrechart = new google.visualization.ColumnChart(document.getElementById('EvergyOgre'));
                window.ogrechart.draw(window.ogredata, options);
                window.monthly_drawn = true;
            }
        }

      function drawogreYearlyCharts()
	  {
          
          if($("#EvergyOgreYearly").length >0)
          {
                window.ogredatayear = new google.visualization.DataTable();
                window.ogredatayear.addColumn('string', 'X');
                window.ogredatayear.addColumn('number', 'Average Market Cost');
                window.ogredatayear.addColumn({'type': 'string', 'role': 'tooltip', 'p': {'html': true}});
                window.ogredatayear.addColumn('number', 'Energy Ogre Member Cost');
                window.ogredatayear.addColumn({'type': 'string', 'role': 'tooltip', 'p': {'html': true}});

                var ogyearly = [];
                                                                 
                    ogyearly.unshift(['2026',2082.2400,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$2082.24 &nbsp;Yearly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $269.36 Higher</div></div>',1812.8800,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$1812.88 &nbsp;Yearly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$269.36 Savings</div></div></div>']);
                                             
                    ogyearly.unshift(['2025',1865.2300,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$1865.23 &nbsp;Yearly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $104.42 Higher</div></div>',1760.8100,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$1760.81 &nbsp;Yearly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$104.42 Savings</div></div></div>']);
                                             
                    ogyearly.unshift(['2024',1890.4700,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$1890.47 &nbsp;Yearly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $205.07 Higher</div></div>',1685.4000,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$1685.40 &nbsp;Yearly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$205.07 Savings</div></div></div>']);
                                             
                    ogyearly.unshift(['2023',1912.6000,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark">$1912.60 &nbsp;Yearly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Difference</div><div class=\"smallvalue\">+ $344.06 Higher</div></div>',1568.5400,'<div style="margin:10px"><div class="font_size font_size--small color_har_blue_dark ">$1568.54 &nbsp;Yearly</div><div class="font_size font_size--small color_auxiliary font_weight--regular">Savings vs Market</div><div class="font_size font_size--small"><div class="font_weight--bold" style="color:#7c9e3d">$344.06 Savings</div></div></div>']);
                    
                    
                                        var maxcount = 1;
                    
                //window.ogredatayear.addRows(window.ogyearly);
                window.ogredatayear.addRows(ogyearly);
                var isSmallScreen = window.innerWidth <= 768;
                var options = {
                title: ' ',
                height: isSmallScreen ? 300 : '100%',
                width: '100%',
                tooltip: {isHtml: true},
                chartArea: {
                    height: '100%',
                    width: '100%',
                    top: 32,
                    left: 50,
                    bottom: 32,
                    right: isSmallScreen ? 20 : 130
                },
                hAxis: {slantedText: false, title:'',},
                vAxis: { maxValue: maxcount,format: '$#,###'},

                    colors: ['#0738cd', '#8cc63f', ],
                legend: { position: isSmallScreen ? 'top' : 'right' }
                };
                window.ogrechartyear = new google.visualization.ColumnChart(document.getElementById('EvergyOgreYearly'));
                window.ogrechartyear.draw(window.ogredatayear, options);
                    window.yearly_drawn = true;
        }
    }

                $('#monthlyview-tab').on('shown.bs.tab', function (e) {
                    if(!window.monthly_drawn)
                        drawogreCharts();
                });

                $('#yearlyview-tab').on('click', function (e) {
                    if(!window.yearly_drawn)
                        {
                        drawogreYearlyCharts();
                        window.dispatchEvent(new Event('resize'));

                        }
                });

        $(window).resize(function() {
    if(this.resizeTO) clearTimeout(this.resizeTO);
    this.resizeTO = setTimeout(function() {
        $(this).trigger('resizeEnd');
    }, 500);
});

$(window).on('resizeEnd', function() {
      if($("#EvergyOgreYearly").length >0)
      {
        window.yearly_drawn = false;
        window.monthly_drawn = false;

        if($("#MonthlyCost-tab").hasClass("active")){
            drawogreCharts();
        } else{
            drawogreYearlyCharts();


        }
      }
});
$(document).on("click touchStart","#powerchecker",function(){
			var testzip = $("#zipcode").val();
			if( (/^\d{5}(-\d{4})?$/).test(testzip) == false){
			 		//$("#zipcode").css("background-yellow","yellow");
			 		$("#infomodalheading").text("Please Enter Zip Code");
			 		$("#infomodalbody").html("<center>Please enter a valid 5 digit US zip code</center>");
			 	    $("#infomodal").modal("show");
			 		return false;
			}

			var data = {
				"Zipcode":$("#zipcode").val(),
				"DueDate":$("#duedate").val(),
				"AmountDue":$("#energyamount").val(),
				"TotalUsage":$("#totalusage").val(),
				"ElectricHeat":false
			}
			var test=$("#heatoption").val();
			if(test==0)
				data.ElectricHeat=false;
			else
				data.ElectricHeat=true;

				//window.open("https://www.energyogre.com/check/external?src=har&data="+btoa(JSON.stringify(data)));
                window.open("https://www.energyogre.com/check/external?utm_source=website&utm_medium=affiliate&utm_term=har-listing-power-check&utm_content=har-listing-power-check&ref=16yy8&src=har&data="+btoa(JSON.stringify(data)));

                ""
		});
    </script>
				    
        
                    <script type="text/javascript" src="https://www.roomvo.com/static/scripts/b2b/har.js?v=*********" async></script>
     
                            <script>
                    //added by naved for for Open house notification.
                    function doOpenHouseNotify(ntype, otype, lid, eid, extra, DateTime) {
                        var mnum = $('#notify_mnum').val();
                        var uid = $('#notify_userid').val();
                        var cont = $('#notify_cont' + extra).val();
                        var email = '';
                        var phone = '';
                        if (cont.indexOf('@') > 0) {
                            email = cont;
                            // add email validation
                            var validemail = /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,10}(?:\.[a-z]{2})?)$/i;
                            if (!validemail.test(email)) {
                                email = '';
                                alert('Please enter valid email address.');
                                return false;
                            }
                        } else {
                            phone = cont;
                            // add phone validation
                            // remove all non alpha numeric and check it is 10 digits
                            phone = phone.replace(/\D/g, '');
                            if (phone.length != 10) {
                                phone = '';
                                alert('Please enter 10 digit phone number.');
                                return false;
                            }
                        }
                        if (email != '' || phone != '') {
                            var url = '/api/addOpenHouseNotify?phone=' + phone + '&email=' + email + '&ntype=' + ntype + '&otype=' + otype + '&lid=' + lid + '&eid=' + eid + '&mnum=' + mnum + '&uid=' + uid + '&datetime=' + DateTime;
                            $.ajax({
                                type: "GET",
                                dataType: "json",
                                url: url + '&rnd=' + Math.random()
                            }).done(function(data) {
                                if (data.status == 'success') {
                                    alert(data.message);
                                    $('#notify_cont' + extra).val('');
                                } else {
                                    alert(data.message);
                                    $('#notify_cont' + extra).val('');
                                }
                            }).fail(function(jqXHR, textStatus) {
                                alert("There was an error, please try again later");
                            });
                        } else {
                            alert(' Please provide email or phone.')
                        }
                    }
                </script>
                <script src="/js/speech.js?v=*********"></script>
<script src="/js/highlight.js?v=*********"></script>

<div id="LargeMapModal" class="modal fade modal--full-width" tabindex="-1" role="dialog" aria-labelledby="AVMCompareHomeValue" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-xl" role="document">
      <div class="modal-content" style="min-height:calc(100vh - 70px);">
        <div class="modal-header p-0">
            <div class="w-100 text-center align-self-center pl-4" style="padding-right:50px;">
                <div class="scroll pt-3 pr-md-5">
                    <div class="row flex-row flex-nowrap pt-1 pb-3 small-gutters scroll--horizental">
                        <div class="col-auto">
                            <select class="custom-select custom-select__rounded custom-select_medium map-type">
                                <option selected value="roadmap">Map View</option>
                                <option value="satellite">Satellite View</option>
                                <option value="hybrid">Hybrid View</option>
                                <option value="terrain">Terrain View</option>
                                <option value="streetView">Street View</option>
                            </select>
                        </div>
                        <div class="col-auto">
                            <select class="custom-select custom-select__rounded custom-select_medium school" name="school_boundaries">
                                <option selected value="">Schools</option>
                                <option value="elementary">Elementary School</option>
                                <option value="middle">Middle School</option>
                                <option value="high">High School</option>
                            </select>
                        </div>
                        <div class="col-auto">
                                <select class="custom-select custom-select__rounded custom-select_medium nearby-dropdown" name="nearby_places">
                                <option selected value="">Places</option>
                                                                    <option value="food">Restaurants</option>
                                                                    <option value="gas_station">Gas Stations</option>
                                                                    <option value="groceries">Groceries</option>
                                                                    <option value="pharmacy">Pharmacies</option>
                                                                    <option value="atm">ATMs</option>
                                                                    <option value="convenience_store">Convenience stores</option>
                                                                    <option value="post_office">Post offices</option>
                                                                    <option value="florist">Florists</option>
                                                                    <option value="libraries">Libraries</option>
                                                                    <option value="car_wash">Car wash</option>
                                                                    <option value="copy_shop">Copy shops</option>
                                                                    <option value="parking">Parking</option>
                                                                    <option value="hospital">Hospitals</option>
                                                            </select>
                        </div>
                                                <div class="col-auto">
                            <a class="btn btn--ordinary d-none d-md-block" target="_blank" href="https://www.bing.com/maps/default.aspx?cp=29.797257~-95.357419&style=b&dir=0&sp=Point.29.797257000000000_-95.357419000000000_">Bird&#039;s Eye View</a>

                            
                        </div>
                        
                    </div>
                </div>
            </div>
          <button type="button" class="close mt-2" data-dismiss="modal" aria-label="Close"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close"></button>
        </div>
        <div class="modal-body p-0">
            <div class="map mapboxgl-map position-absolute" style="top:0px; bottom:0px;left:0px;right:0px;"></div>
            <div id="reactive-street-view"></div><!-- Use as reference -->




<script type="text/javascript">
    Object.defineProperty(window, "reactiveStreetView", {
        configurable:true,
        get:function() {
            var value = StreetViewComponent.register("reactive-street-view", "29.797257000000000", "-95.357419000000000");
            Object.defineProperty(window, "reactiveStreetView", { value });
            return value;
        }
    })
</script>        </div>
      </div>
    </div>
</div>


<script>

var ReactiveMap = { initialization:null };
var largeMap;
var largeMapPromise;

ReactiveMap.initialize = function() {
    if(ReactiveMap.initialization) { return ReactiveMap.initialization; }
    return ReactiveMap.initialization = harload('harmapLoader', 'ready').then(function() {
        var simpleMapPromise = ReactiveMap.initializeSimpleMap();
        ReactiveMap.initializeLargeMap();
        return simpleMapPromise;
    });
}

ReactiveMap.initializeSimpleMap = function() {

            return $.when();
    }

ReactiveMap.initializeLargeMap = function() {
    
        var ListingID =   '';         var defer = $.Deferred();
        largeMapPromise = defer.promise();
        
        var mapInitialized;
        var popupInitialized = false;

        if($('#LargeMapModal').hasClass('show')) {
            handleModalShow();
        }

        $('#LargeMapModal').on('shown.bs.modal', function (e) {
            handleModalShow();
        });
        
        function handleModalShow(){
            if(mapInitialized) { 
                largeMap.resize();
                return; 
            }

            var isStreetView = $('#LargeMapModal .map-type').val() == 'streetView';

            if(!popupInitialized){
                popupInitialized = true;
                // if(isStreetView) { 
                    initializePopup(); 
                // }
            }

            if(!isStreetView){
                HARLargeMap();
            }
        }


        function initializePopup(){
            $('#LargeMapModal .map-type').on('change.initializePopup', function(e) {
                var type = $(this).val();
                var isStreetView = type == 'streetView';
                reactiveStreetView.toggle(isStreetView);
                if(isStreetView) { return; }
             
                $('#LargeMapModal .map-type').off('change.initializePopup');
                var loaded = mapInitialized? $.when(): HARLargeMap();
                loaded.then(function() {
                    if(largeMap.isReady() && largeMap.mapTypeId === type && !isStreetView) { return; }
                    largeMap.mapTypeId = type;
                    largeMap.resize();
                });

                    // if(type == 'streetView'){
                    //     //window.StreetView(true,type);
                    //     reactiveStreetView.show();
                    // }else{
                    //     var loaded = mapInitialized? $.when(): HARLargeMap();
                    //     loaded.then(function() {
                    //         var isStreetViewVisible = $('#map-popup-street-view-iframe').is(':visible');
                        
                    //         if(largeMap.isReady() && largeMap.mapTypeId == type && !isStreetViewVisible) { return; }
                    //         window.StreetView(false,type);
                    //         largeMap.mapTypeId = type;
                    //         largeMap.resize();
                    //     });

                    // }
            });

            $('#LargeMapModal .school').on('change', function(e) {
                var type = $(this).val();
                HARLargeMap().then(function (map) {
                    if(type !=='' ) {
                        map.layers.school.on({ type:type, forceZoom:true });
                        if($('#LargeMapModal .map-type').val() == 'streetView'){
                            reactiveStreetView.hide();
                            $('#LargeMapModal .map-type').val(map.mapOptions.mapTypeId);
                            //map.mapTypeId = 'roadmap';
                            map.resize();
                        }
                    }
                    else {
                        map.layers.school.off();
                    }
                });
                
            });

            $('#LargeMapModal .nearby-dropdown').on('change', function(e) {
                var type = $(this).val();
                HARLargeMap().then(function (map) {
                    if(type !== '') {
                        if($('#LargeMapModal .map-type').val() == 'streetView'){
                            reactiveStreetView.hide();
                            $('#LargeMapModal .map-type').val(map.mapOptions.mapTypeId);
                            //map.mapTypeId = 'roadmap';
                            map.resize();
                        }
                        map.layers.places.on({ type:type });
                    }
                    else {
                        map.layers.places.off();
                    }
                });
            });
        }

        function HARLargeMap(){
            if(mapInitialized) {
                return mapInitialized;
            }
            

            var latitude = 29.797257000000000;
            var longitude = -95.357419000000000;
            if(!latitude || !longitude) {
                defer.reject(new Error('Coordinates are not valid.'));
                return $.when();
            }

            var center = { latitude:latitude, longitude:longitude };

          return  mapInitialized = HARMap.initialize({ element:"#LargeMapModal .map", mapTypeControl:false, zoom:19, center:center,token:"default" }).then(function(response) {
                var map = largeMap = response.map;
                var Marker = response.module.Marker;
                map.mapTypeId = 'roadmap';
                map.mapOptions.zoomControlPosition = 'RIGHT_BOTTOM';
                var exclude =  ListingID ? [ListingID] : [];
                map.layers.parcels.on({ type:'avm',exclude:exclude });

                var marker = new Marker(center);
                marker.icon = 'https://content.harstatic.com/media/icons/icon-property-map-pin.svg';
                marker.addToMap(map);
               

                $('#LargeMapModal .map-type').on('change', function(e) {
                    var type = $(this).val();
                    var isStreetView = type == 'streetView';
                    reactiveStreetView.toggle(isStreetView);
                    if(map.mapTypeId === type || isStreetView) { return; }

                    map.mapTypeId = type;
                    map.resize();
                });

                defer.resolve({ map:largeMap, module:response.module });
                return largeMap;
            });
        }

    }

// Legacy mode
window.StreetView = function(state)
{
    reactiveStreetView.toggle(state);
    if(state) { 
        $('#LargeMapModal .map-type').val('streetView');
        $('#LargeMapModal .school').val('');
        $('#LargeMapModal .nearby-dropdown').val('');
    }
}

</script>
<div id="divAmortize" class="modal fade modal--small" tabindex="-1" role="dialog" aria-labelledby="RequestCallBack" style="display: none;" aria-hidden="true">
  <div class="modal-dialog modal-dialog-scrollable modal-lg  modal-dialog-centered" role="document" style="min-width:70%;">
    <div class="modal-content h-100">
			<div class="modal-header">
                <h5 tabindex="0" class="modal-title">Amortization</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close">
                </button>
            </div>
      <div class="modal-body p-lg-5 p-0 overflow-hidden text-left">
        <div class="mb-3 h-100">
			<iframe id="AmortizeFrame" style="height:100%; width:100%;border:0px; min-height:550px;"></iframe>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="streetview-container" style="position:absolute; left:0; right:0; top:0; bottom:0; background-color: black; z-index: 100;"></div>
<iframe class="google-streetview" frameborder="0" style="height:100%;width:100%;"></iframe>
<script type="text/javascript">
(function() {
    const MAPILLARY_TOKEN = 'MLY|6342117919134033|b3f732c4d0c418e9cf96ca98c6f4ab43';

    var streetViewContainerModel = detachElement('.streetview-container');
    var googleStreetView = detachElement('.google-streetview');
    var mapillaryLoad = undefined; // Promise<void>
    var mapillaryResultMap = {}; // { [locationKey]:Promise<IMapillaryImagesResult> }


    function StreetViewInstance(id, latitude, longitude)
    {
        var instance = this;
        var elementPromise;

        this.id = id;
        this.mapId = `${id}-map`;
        this.latitude = latitude;
        this.longitude = longitude;
        this.locationKey = latitude + '_' + longitude;
        this.container = document.getElementById(id);

        this.toggle = function(state)
        {
            return state? this.show(): this.hide();
        }

        this.show = function()
        {
            $(instance).triggerHandler('showing');
            return loadElement().then(function(element) {
                $(element).show();
                $(instance).triggerHandler('show');
            });
        }

        this.hide = function()
        {
            if(!elementPromise) { return; }

            $(instance).triggerHandler('hiding');
            return elementPromise.then(function(element) {
                $(element).hide();
                $(instance).triggerHandler('hide');
            });
        }

        function loadElement()
        {
            if(elementPromise) { return elementPromise; }

            var id = instance.id;
            var element = createElement(instance.mapId, instance.container);
            return elementPromise = loadMapillary(instance)
                .catch(function(err) { insertGoogleIframe(instance, element); })
                .then(function() { return element; })
        }
    }

    function showStreetView(instance)
    {
        // if (view === 'popup') {
        //     $('#map-popup-street-view-iframe').show();
        //     $('#LargeMapModal .map').hide();
        //     $('#LargeMapModal .map-type').val('streetView');
        // }
    }

    function insertGoogleIframe(instance, container)
    {
        var lat = instance.latitude;
        var long = instance.longitude;
        var url = `/api/streetview?lat=${lat}&lng=${long}&width=100%&height=100%&color=black`;
        var iframe = googleStreetView.cloneNode();
        iframe.src = url;
        container.appendChild(iframe);
    }

    function createElement(id, parent)
    {
        var container = streetViewContainerModel.cloneNode();
        container.id = id;
        parent.appendChild(container);
        return container;
    }

    function loadMapillary(instance)
    {
        var data;
        return makeMapillaryCall(instance)
            .then(function(result) { data = result; })
            .then(function() { return loadMapillaryAssets(); })
            .then(function() { return createViewer(instance, data); });
    }

    function makeMapillaryCall(instance)
    {
        var key = instance.locationKey;
        if(mapillaryResultMap[key]) { return mapillaryResultMap[key]; }

        var bbox = getBoundBox(instance.latitude, instance.longitude, 0.0003);
        var url = `https://graph.mapillary.com/images?access_token=${MAPILLARY_TOKEN}&bbox=${bbox}&fields=camera_type,geometry`;
        return mapillaryResultMap[key] = ajaxPromise({ url }).then(function(result) {
            if (result.data.length === 0) { throw new Error('Empty results.'); }
            return result;
        });
    }
    
    function createViewer(instance, result)
    {
        var data = result.data.filter(item => item.camera_type === 'equirectangular' || item.camera_type === 'fisheye');
        var sorted = sortByDistance(data, instance.latitude, instance.longitude);
        var id = sorted[0].id;
        var { Viewer, RenderMode } = mapillary;
        var viewer = new Viewer({
            accessToken: MAPILLARY_TOKEN,
            container: instance.mapId, // the ID of our container defined in the HTML body
            imageId: id,
            renderMode: RenderMode.Letterbox
        });
        viewer.setFilter(['!=', 'cameraType', 'perspective']);
        return viewer;
    }

    function sortByDistance(data, latitude, longitude) {
        data.sort(function(a, b) {
            var aDist = distance(a.geometry.coordinates[1], a.geometry.coordinates[0], latitude, longitude);
            var bDist = distance(b.geometry.coordinates[1], b.geometry.coordinates[0], latitude, longitude);
            return aDist - bDist;
        });
        return data;
    }

    function distance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Radius of the earth in km
        const dLat = deg2rad(lat2 - lat1);  // deg2rad below
        const dLon = deg2rad(lon2 - lon1);
        const a =
            Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);

        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const d = R * c; // Distance in km
        return d;
    }

    function deg2rad(deg) {
        return deg * (Math.PI/180)
    }

    function getBoundBox(lat,long,radius){
        var lat1  = parseFloat(Number(lat)-radius);
        var long1 = parseFloat(Number(long)-radius);
        var lat2  = parseFloat(Number(lat)+radius);
        var long2 = parseFloat(Number(long)+radius);
        return `${long1},${lat1},${long2},${lat2}`;
    }

    function loadMapillaryAssets()
    {
        if(mapillaryLoad) { return mapillaryLoad; }
        return mapillaryLoad = loadScriptAndStyle("mapillary.js", "mapillary.css")
    }

    function loadScriptAndStyle(scriptUrl, styleUrl, callback)
    {
        var baseUrl = "https://unpkg.com/mapillary-js@4.1/dist/";
        var script = document.createElement('script');
        script.src = baseUrl + scriptUrl;
        script.type = "text/javascript";

        var style = document.createElement('link');
        style.rel = 'stylesheet';
        style.href = baseUrl + styleUrl;
        style.type = "text/css";

        var promise = Promise.all([
            onLoad(script),
            onLoad(style)
        ]);

        document.head.appendChild(script);
        document.head.appendChild(style);
        return promise;
    }

    function onLoad(node)
    {
        return new Promise(function(resolve, reject) {
            node.onload = function() { resolve(); }
            node.onerror = function() { reject(); }
        });
    }

    function ajaxPromise()
    {
        var args = arguments;
        return new Promise(function(resolve, reject) {
            $.ajax.apply($, args).then(resolve, reject);
        });
    }

    function detachElement(selector)
    {
        var element = document.querySelector(selector);
        element.remove();
        return element;
    }

    window.StreetViewComponent = {};
    window.StreetViewComponent.register = function(id, latitude, longitude) {
        return new StreetViewInstance(id, latitude, longitude);
    }

})();
</script>
<script src="/plugins/audioplayer/green-audio-player.min.js"></script>
<script src="/plugins/swiper/js/swiper-bundle.min.js"></script>
<div id="lead2073629195_RequestInformation" class="modal fade modal--small modal-agent-lead" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenteredScrollableTitle" style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-sm modal-dialog-centered" role="document">
        <div id="lead2073629195__canclbutton" class="modal-content agent-modal">
            <div class="modal-header">
                <h5 tabindex="0" class="modal-title pb-3" id="exampleModalCenteredScrollableTitle">Contact Agent</h5>

                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close"></button>
            </div>
       
            <div class="modal-body text-left pt-1">
                <div class="agent_signature--large pb-4 pt-4 mt-2 text-center" style="width: 291px;">
                    <div class="agent_signaturev2 agent_signaturev2__medium">

	<a class="agent_signaturev2__medium__photo"
        href="https://www.har.com/patrick-burbridge/agent_STEVENB"
     title="View Patrick Burbridge's profile" style=background-image:url(https://pics.harstatic.com/agent/476135_112x112.jpg?ts=2020-08-14T17:20:00)
								></a>

	<div class="agent_signaturev2__info">
		<div class="agent_signaturev2__info__agent_name" title="View Patrick Burbridge's profile" aria-label="View Patrick Burbridge's profile">
			<div class="agent_signaturev2 agent_signaturev2__medium agent_signaturev2__info__agent_name">
				<a class="row no-gutters"
								href="https://www.har.com/patrick-burbridge/agent_STEVENB"
								>
				<span class="col-auto pr-2 text-truncate color_carbon" style="min-width:100px;">Patrick Burbridge</span>
										<div class="col-auto"><img alt="Platinum" class="ml-1" style="width:51px;" src="https://content.harstatic.com/media/icons/label-platinum.svg" width="51" height="16" /></div>
									</a>
			</div>
		</div>
		
															<div title="View CitiQuest Properties's page" aria-label="View CitiQuest Properties's page">
					<a class="agent_signaturev2__info__broker_name" href="/citiquest-properties/broker_SIDE37" title="View CitiQuest Properties's page" aria-label="View CitiQuest Properties's page"> CitiQuest Properties</a>
				</div>
								</div>
</div>
                                        <div class="d-block text-center" style="position: absolute;margin-left:60px;">
                        <!-- phone icon-->
<img alt="" src="https://content.harstatic.com/media/icons/map_popup/phone.svg"  class='' style="width:12px;height:12px;" />
<a data-target='target_phone_presentedby_lead' id="source_phone_presentedby_lead" href="javascript:void(0);" data-track-type=AGENT_PHONE_CLICKED class="font_weight--semi_bold hideAgentPhone doListhubTracking" onclick="showPhone('source_phone_presentedby_lead','(*************','STEVENB','agent','','9664623')">
  Click to view phone
</a>


<!--target control -->
<a href="tel:(*************" id='target_phone_presentedby_lead' class='color_auxiliary' style="display:none;">(*************</a>
<!--source control -->
                    </div>
                                    </div>
                <div id="lead2073629195_form_div" class="text-left p-2">
                    <div class="loadinglead m-auto" style="display:none;">
                        <center><img alt="loading" src="https://content.harstatic.com/img/common/loading1.gif" alt="Loading"></center>
                    </div>
                                        <input type="hidden" id="lead2073629195_mlsnum" name="mlsnum" value="74257223">
                    <input type="hidden" id="lead2073629195_listingid" name="listingid" value="9664623">
                    <input type="hidden" id="lead2073629195_debug_priority" name="debug_priority" value="1">
                    <input type="hidden" id="lead2073629195_harid" name="harid" value="3011563">
                    <input type="hidden" id="lead2073629195_pAddress" name="pAddress" value="4017 Robertson St ,Houston ,TX ,77009">
                    <input type="hidden" id="lead2073629195_photoUrl" name="photoUrl" value="https://photos.harstatic.com/411360813/lr/img-1.jpeg?ts=2025-04-07T13:11:39.830">
                    <input type="hidden" id="lead2073629195_officekey" name="officekey" value="SIDE37">
                    <input type="hidden" name="subject" id="lead2073629195_subject" value="Consumer requests more information for 4017 Robertson St">
                    <input type="hidden" id="lead2073629195_listhubTrack" name="listhubTrack" value="AGENT_EMAIL_SENT">
                    <input type="hidden" name="ab_type" id="lead2073629195_ab_type" value="ab" />
                                        <input type="hidden" id="lead2073629195_lead_source_id" name="lead_source_id" value="101">
                    
                                        <input type="hidden" id="lead2073629195_agentkey" name="agentkey" value="STEVENB" />
                    <input type="hidden" id="lead2073629195_member_number" name="member_number" value="476135">
                    <input type="hidden" id="lead2073629195_contact_name" name="contact_name" value="Patrick Burbridge">
                    <input type="hidden" id="lead2073629195_contact_phone" name="contact_phone" value="(*************">
                    <input type="hidden" id="lead2073629195_portalid" name="portalid" value="HAR">
                    <input type="hidden" name="leadsource" id="lead2073629195_leadsource" value="https://www.har.com/homedetail/4017-robertson-st-houston-tx-77009/3011563" />
                    <input type="hidden" name="lead_type" id="lead2073629195_lead_type" value="1">
                                        <input type="hidden" name="hsource" id="lead2073629195_hsource" value="1">
                                        <div class="row pt-3">
                        <div class="col-md-6 col-12 pb-3 pb-md-0">
                            <label for="lead2073629195_firstname">First Name</label>
                            <input type="text" class="form-control mb-3" id="lead2073629195_firstname" value="" name="firstname" aria-describedby="name" maxlength="54" placeholder="Your First Name" required />
                        </div>
                        <div class="col-md-6 col-12 pb-3 pb-md-0">
                            <label for="lead2073629195_lastname">Last Name</label>
                            <input type="text" class="form-control mb-3" id="lead2073629195_lastname" value="" name="lastname" aria-describedby="lastname" maxlength="54" placeholder="Your Last Name" required />
                        </div>
                    </div>

                    <div class="row pt-3 pb-3">
                        <div class="col-md-6 col-12 pb-3 pb-md-0">
                            <label for="Contact_Email">Email</label>
                            <input type="email" class="form-control" id="lead2073629195_email" name="email" aria-describedby="Contact_Email" placeholder="Email" value="" maxlength="54" required />
                        </div>
                        <div class="col-md-6 col-12 pb-3 pb-md-0">
                            <label for="Contact_Phone">Phone</label>
                            <input type="tel" class="form-control  phone-number-only" id="lead2073629195_phone" name="phone" aria-describedby="Phone" placeholder="Phone" value="" />
                        </div>
                    </div>
                                        <div class="py-2" style="font-size: 12px !important;">
                        <input type="hidden" id="lead2073629195_agent_connnect_id" name="agent_connect_id">
                        <input type="hidden" id="lead2073629195_agent_connnect_name" name="agent_connect_name">
                        <div class="color_auxiliary font_weight--bold pb-3">Are you currently working with an agent?</div>
<div id="AgentYesNo_Options" role="radiogroup">
    <div role="radio" class="custom-control custom-radio custom-control-inline">
        <input type="radio" id="lead2073629195_agent_yn" name="agent_yn" class="custom-control-input" value="true">
        <label class="custom-control-label" for="lead2073629195_agent_yn">Yes</label>
    </div>
    <div role="radio" class="custom-control custom-radio custom-control-inline">
        <input type="radio" id="lead2073629195_agent_ny" name="agent_yn" class="custom-control-input" value="false" checked="checked">
        <label class="custom-control-label" for="lead2073629195_agent_ny">No</label>
    </div>
</div>

<div id="IfAgentYes" class="pt-3" style="display:none;">
    <label class="" for="first_name">Agent name</label>
    <input type="text" class="form-control js_typeahead" data-source="member" id="first_name" aria-describedby="showing_1" placeholder="First or Last Name" autocomplete="off">
        <div class="border_radius--default pt-2 pb-2 pl-3 pr-3 color_auxiliary bg_color_cloudy_sky_light font_size--small"><a href="/login">Sign in</a> if you’ve already connected with an agent</div>
    </div>

<!-- this is agent block will display when user type and select any thing from typeahead -->
<div id="AgentBox" class="pt-3" style="display:none;">
    <div class="border_radius--default border_radius--default border p-3 position-relative">
        <a id="CloseAgentBlock" href="javascript:void(0);" class="d-block position-absolute" style="top:5px;right:5px;"><img style="width:16px;" src="https://content.harstatic.com/media/icons/icon-close-circle.svg" alt="Close"></a>
        <div class="agent_signature agent-card-jr"></div>

    </div>
    <div class="bg_color_cloudy_sky_light color_auxiliary text-center px-3 mt-2 py-2">
         
	 	  The agent above, instead of the listing agent, will receive your lead request.
            </div>
</div>

                    </div>
                                     

                    <div class="row pt-3">
                        <div class="col-md-12 col-12">
                            <label for="IWouldLike">I would like to</label>
                            <select id="lead2073629195_messageid" name="messageid" class="custom-select custom-select_large" required>
                                <option value="1">Have a REALTOR® call me back</option>
                                                                <option value="2">Make an appointment to view this property </option>
                                <option value="3">Receive more information about this property </option>
                                <option value="4">Sell my house</option>
                                <option value="5">Buy a new house</option>
                                <option value="6">Sell and Buy a house</option>
                                                            </select>
                        </div>
                    </div>
                   

                    <div class="row pt-3">
                        <div class="col-md-12 col-12">
                            <label for="lead2073629195_comment"> Your Message</label>
                            <textarea rows="3" class="form-control mb-3 lead_comment_box" id="lead2073629195_comment" name="comment" onkeyup="updateCount('lead2073629195_comment','lead2073629195_CommentCount',500);" aria-describedby="descr" placeholder="Your Message"></textarea>
                            <div id="lead2073629195_CommentCount">Please limit your comment to 500 characters.</div>
                        </div>
                    </div>

                    <div id="agent-lead-email-hcaptcha" class="lead2073629195_hCaptchaLoader pt-3"></div>

                </div>
            </div>
            <div class="modal-footer text-center h-auto">
                <button role="button" id="lead2073629195_ebutton" class="btn btn--primary btn--medium sendLead-lead2073629195" data-lead="lead2073629195" aria-label="Apply">Submit</button>
                <button role="button" id="lead2073629195_canclbutton" class="btn btn--shapeless btn--medium" data-dismiss="modal" aria-label="Cancel">Cancel</button>

            </div>

        </div>
        <div id="lead2073629195_result" class="modal-content no-message" style="display: none;">
            <button type="button" class="close mt-3 reloadFormOnList" data-dismiss="modal" aria-label="Close" style="z-index: 1;"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close"></button>
            <div class="modal-body text-center">
                <div class="text-center mb-2"><img src="https://content.harstatic.com/media/icons/illustration-message-sent.svg" alt="Message illustration"></div>
                <h5 tabindex="0" class="modal-title mb-2" id="exampleModalCenteredScrollableTitle">Message sent</h5>
                <div class="font_size--large pb-4 mb-2">You should hear from Patrick soon.</div>

                <button class="btn btn--ordinary ml-auto d-block mr-auto reloadFormOnList" data-dismiss="modal" type="button" style="min-width:160px;">Ok</button>
            </div>
        </div>
                <div id="lead2073629195_agent-connect-request" class="modal-content no-message" style="display: none;">
            <button type="button" class="close mt-3 reloadFormOnList" data-dismiss="modal" aria-label="Close" style="z-index: 1;"><img src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg" alt="Close"></button>
            <div class="modal-body text-center">
                <div class="text-center mb-2"><img src="https://content.harstatic.com/media/icons/illustration-message-sent.svg" alt="Message illustration"></div>
                <h5 tabindex="0" class="modal-title mb-3" id="exampleModalCenteredScrollableTitle">Message sent</h5>
                <div class="loading-lead m-auto" style="display:none;">
                    <center><img alt="loading" src="https://content.harstatic.com/img/common/loading1.gif" alt="Loading"></center>
                </div>
                <div class="lead-result"></div>
            </div>
        </div>
            </div>
</div>



        <script>
        var isNSectionlDiv = true;

        isScrolledIntoView = function(elem) {
            if (elem.length == 0) return false;
            var docViewTop = jQuery(window).scrollTop();
            var docViewBottom = docViewTop + jQuery(window).height();
            var elemTop = jQuery(elem).offset().top;
            var elemBottom = elemTop + jQuery(elem).height();
            return ((elemBottom >= docViewTop) && (elemTop <= docViewBottom) && (elemBottom <= docViewBottom) && (elemTop >= docViewTop));
        }

        function NeighborhoodSection() {
            $.ajax({
                type: "GET",
                dataType: "json",
				data:{'_ptID': "7997", '_gmaID': "109", '_highriseID': "0", '_moHoodID': "0", '_mpcID': "0", '_city': "Houston", '_Zip': "77009", '_HARID': "3011563"}, 
                url: '/api/neighborhood-section',
                beforeSend: function() {
                    $('#NeighborhoodSectionLoader').show();
                },
                complete: function() {
                    $('#NeighborhoodSectionLoader').hide();
                },
                success: function(response) {
                    $('.load_near_school').hide()
                    $('.neighborhood_section').html(response.html);

                    $('#mupdate').insertAfter($('#put_market_after_this'));
                    // const targetElement = $('#put_subdivision_after_this').length > 0 ? $('#put_subdivision_after_this') : $('#put_neighborhood_after_this');

                    // $('#subdfacts').insertAfter(targetElement);
                    
                    setTimeout(function() {
                        $('#mupdate > .border-bottom').removeClass('border-bottom');
                        $('#mupdate > .mb-4').removeClass('mb-4');
                        $('#mupdate > .pb-5').removeClass('pb-5');
                        // $('#subdfacts > .border-bottom').removeClass('border-bottom');
                        // $('#subdfacts > .mb-4').removeClass('mb-4');
                        // $('#subdfacts > .pb-5').removeClass('pb-5');
                        $('#subdfacts').addClass("d-none");
                    }, 3000);
                }
            });
        }
                    $(window).scroll(function() {
                if (isScrolledIntoView($('#loadNeighborhoodSection'))) {
                    if (isNSectionlDiv) {
                        isNSectionlDiv = false;
                        NeighborhoodSection()
                    }
                }
            });
            $('.listing_detail_cntr').scroll(function() {
                if (isScrolledIntoView($('#loadNeighborhoodSection'))) {
                    if (isNSectionlDiv) {
                        isNSectionlDiv = false;
                        NeighborhoodSection()
                    }
                }
            });
            </script>
        <script>
        var isSchoolDiv = true;
                var url_assign_school = '/api/assign-schools?elementary=101912197&middle=101912061&high=101912003'
        
        $(document).on('click', '.expendNew', function(e) {
            var exp_container = $(this).data('container');
            $('.expendNew').find('img').toggleClass('rotate_icon');
            if ($('.' + exp_container).length) {
                $('.' + exp_container).toggle()
            } else {
                NearBySchool()
            }

        });

        isScrolledIntoView = function(elem) {
            if (elem.length == 0) return false;
            var docViewTop = jQuery(window).scrollTop();
            var docViewBottom = docViewTop + jQuery(window).height();
            var elemTop = jQuery(elem).offset().top;
            var elemBottom = elemTop + jQuery(elem).height();
            //console.log(elemTop, elemBottom, docViewTop, docViewBottom, (elemBottom >= docViewTop), (elemTop <= docViewBottom), (elemBottom <= docViewBottom), (elemTop >= docViewTop));
            return ((elemBottom >= docViewTop) && (elemTop <= docViewBottom) && (elemBottom <= docViewBottom) && (elemTop >= docViewTop));
        }


        function AssignSchool() {
            
            $.ajax({
                type: "GET",
                dataType: "json",
                data:{'target_action':'0','gateway':""},
                url: url_assign_school,
                beforeSend: function() {
                    $('#LoaderAssignImage').show();
                },
                complete: function() {
                    $('#LoaderAssignImage').hide();
                },
                success: function(response) {
                    $('.load-assign-school').html(response.html)

                }
            });
        }

        function NearBySchool() {
            $.ajax({
                type: "GET",
                dataType: "json",
				data:{'target_action':'0','gateway':""},
                url: '/api/near-by-schools?lat=29.797257000000000&lng=-95.357419000000000',
                beforeSend: function() {
                    $('#NearByLoader').show();
                },
                complete: function() {
                    $('#NearByLoader').hide();
                },
                success: function(response) {
                    $('.load_near_school').hide()
                    $('.nearby_school').html(response.html)

                }
            });

        }

        
        $('.load_near_school').click(function() {
            NearBySchool()

        });

        $('.listing_detail_cntr').scroll(function() {
            if (isScrolledIntoView($('#loadAssignSchool'))) {
                if (isSchoolDiv) {
                    isSchoolDiv = false;
                    AssignSchool()
                }
            }
        });
        $(window).scroll(function() {
            if (isScrolledIntoView($('#loadAssignSchool'))) {
                if (isSchoolDiv) {
                    isSchoolDiv = false;
                    AssignSchool()
                }
            }
        });


            </script>
    
	    <script>
        $('.loader-icon').hide();

        $(document).ready(function() {
                           setTimeout(function(){
                var BugHerdConfig = {"feedback":{"hide":true},"reporter": {"required":"true"}};
                (function (d, t) {
                    var bh = d.createElement(t), s = d.getElementsByTagName(t)[0];
                    bh.type = 'text/javascript';
                    bh.src = '//www.bugherd.com/sidebarv2.js?apikey=**********************';
                    s.parentNode.insertBefore(bh, s);
                })(document, 'script');
                 setTimeout(function(){
                    if(typeof window._bugHerd != 'undefined')
                      $("#bugherdbox").show();
                    else
                    {
                        setTimeout(function(){
                        if(typeof window._bugHerd != 'undefined')
                            $("#bugherdbox").show(); }, 2000);
                    }
                }, 800);
            },12000);
            
            $('.get-location').click(function() {
                getGpsLocation();
            });

            $('#NearByMapImage').click(function() {
                $('.loader-icon').show();
                getGpsLocation().then(function(){
                    submitFindAgentForm();
                    $('.loader-icon').hide();
                });
            });

            	$(".lang_picker").on("click", function(){
        var expiration = new Date();
        expiration.setDate(expiration.getDate()+365);
        var test = $(this).data();
        if(typeof test.langid != 'undefined'){
            if ('URLSearchParams' in window) {
                sfn.showLoader("Loading..");
                var loc = new URLSearchParams(window.location.search);
                loc.set("lang", test.langid);
                window.location.search = loc.toString();
            }
        } else {
             if ('URLSearchParams' in window) {
                sfn.showLoader("Loading..");
                var loc = new URLSearchParams(window.location.search);
                loc.set("lang", "en");
                window.location.search = loc.toString();
            }
        }
    });
	$(window).scroll(function() {
        if($('.listing_detail_standalone').length >0){
            try {
                var distance = $('.listing_detail_standalone').offset().top;
                if ($(window).scrollTop() >= distance) {
                    $('#InpageNav').fadeIn();
                    $(this).addClass('scrolling_now');
                } else {
                    $('#InpageNav').hide();
                    $(this).removeClass('scrolling_now');
                }
            } catch (e){ }
        }
	});

	/* inpage nav options */
	$('body').scrollspy({ target: '#InpageNav' })
	$('#InpageNav a').click(function(){
		$('#InpageNav a').removeClass('active');
		$(this).addClass('active');
	});
	/* / inpage nav options*/
	$(document).on('click', '#InpageNav a[href^="#"],.calculatorInfo', function(e) {
	    // target element id
	    var id = $(this).attr('href');
	    // target element
	    var $id = $(id);
	    if ($id.length === 0) {
	        return;
	    }
	    // prevent standard hash navigation (avoid blinking in IE)
	    e.preventDefault();
	    // top position relative to the document
	    var pos = $id.offset().top;
	    // animated top scrolling
	    $('body, html,.listing_detail_cntr').animate({scrollTop: pos});
	});

	$('[data-toggle="tooltip"]').tooltip();
	$('.popover-dismiss').popover({
	trigger: 'toggleEnabled'
	})
				ListHubTrack('DETAIL_PAGE_VIEWED')
		$('.doListhubTracking').click(function(){
		type = $(this).data('track-type')
		ListHubTrack(type)
		});
				/* price detection popover */
		var tempPriceContent = $('#PriceReductionPopup').html();
		$(".PriceReducedPopover").popover({
		title: '<h3 tabindex="0" class="custom-title">Reduced 35%</h3>',
		content: tempPriceContent,
		template: '<div class="popover price-popover" role="tooltip">'+'<div class="arrow"></div>'+'<h3 class="popover-header"></h3>'+'<div class="popover-body"></div>'+'</div>',
		html: true
		});
		/* / price detection popover */

		// handle links with @href  started with '#' only
		$('a[href*="#"]')
		// Remove links that don't actually link to anything
		.not('[href="#"]')
		.not('[href="#0"]')
		.click(function(event) {
		// On-page links
		if (
		location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '')
		&&
		location.hostname == this.hostname
		) {

		// Figure out element to scroll to
		var target = $(this.hash);

		target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
		// Does a scroll target exist?
		if (target.length) {
		// Only prevent default if animation is actually gonna happen
		event.preventDefault();
		var $target = $(target);

		var container = $('.listing_detail_cntr');
		var scrollTo = $target;

		// Calculating new position of scrollbar
		var position = scrollTo.offset().top
		- container.offset().top
		+ container.scrollTop() - 60;

		// Setting the value of scrollbar
		container.scrollTop(position);

		if ($target.is(":focus")) { // Checking if the target was focused
		return false;
		};

		}
		}
		});

		$(".openStreetView").on('click', function(e) {
			e.preventDefault();
			OpenStreetView();
		});

		/* main menu js */
		/*$('#HamIcon').click(function() {
		$('body').toggleClass('menu_opened');
		});

		$('.nav-link').click(function() {
		$(this).toggleClass('menu_item_open');
		});*/

		/* / main menu js */

		/* for corona message */
		$('#CloseMessageBox').click(function() {
		$('.imp_message').fadeOut();
		$('body').removeClass('message_active');
		});
		/* / for corona message */

		/* slider */
		$('.date_slider').slick({
		slidesToShow: 3,
		slidesToScroll: 1,
		centerPadding: '5px',
		initialSlide: 0,
		infinite: false,
		responsive: [
		{
		breakpoint: 1005, // tablet breakpoint
		settings: {
		slidesToShow: 2,
		}
		},
		{
		breakpoint: 780, // tablet breakpoint
		settings: {
		arrows: false
		}
		}
		]
		});
		$('.date_slider').show();

		$('.date_slider2').slick({
		slidesToShow: 5,
		slidesToScroll: 1,
		centerPadding: '5px',
		responsive: [
		{
		breakpoint: 1205, // tablet breakpoint
		settings: {
		slidesToShow: 3,
		}
		},
		{
		breakpoint: 780, // tablet breakpoint
		settings: {
		arrows: false
		}
		},
		{
		breakpoint: 420, // tablet breakpoint
		settings: {
		slidesToShow: 2,
		}
		}
		]
		});
		$('.date_slider2').show();



		/* / slider */
		$('.listing_detail_overlay').on("click",function(event){
		if (event.target == $('.listing_detail_overlay').get(0)){
		var zip = " 77009 ";
		window.location.href = "/search/dosearch/?zip_code=" + zip.trim() + "&view=map";
		$('.listing_detail_overlay').fadeOut();
		}
		});

		/* listing popup open close */
		$('.close_property_popup,.close_prop_popup,.close_property_popup_2').click(function() {
		$('.listing_detail_overlay').fadeOut();
		});
		/* / listing popup open close */



		$('.close_mls_popup').click(function() {
		$('#MLSBlock').hide();
		$(this).hide();
		$('#MLSLingBlock').fadeIn();
		});

		$('#ShowMLSInfo').click(function() {
		$('#MLSLingBlock').hide();
		$('#MLSBlock').fadeIn();
		$('.close_mls_popup').fadeIn();

		});
		//on switching to agent/customer view (mobile)
		$("#MlsSwitcherMobile").click(function() {
		if (parseInt($(this).attr("data-mode")) == 1) //if already agent view
		{
		$('#ShowMLSInfo').removeClass("d-block");
		$('#ShowMLSInfo').hide();
		$(this).attr("data-mode", 0);
		$("#ViewAsTextMobile").html("Agent View");
		} else if (parseInt($(this).attr("data-mode")) == 0) //if already customer view
		{
		$('#ShowMLSInfo').show();
		$('#ShowMLSInfo').addClass("d-block");
		$(this).attr("data-mode", 1);
		$("#ViewAsTextMobile").html("Customer View");
		}
		});

		$('.date_card').click(function() {
		day = $(this).data('date') ? $(this).data('date') : '';
					window.open("/schedule_showing/74257223?day=" + day,"_blank");
				
		});

		$(".ask_agent").keyup(function() {
		var dInput = $(this).val();
		$(".lead_comment_box").val(dInput);
		});


		$(".property_loader").slick({
		dots: false,
		infinite: false,
		slidesToShow: 1,
		slidesToScroll: 1,
		appendDots: false,
		});

		/*open agent contact*/
		$('#OpenAgentContact,.OpenAgentContact').on('click',function(){
		$('#RespBtn').fadeOut(function(){
			$('.agent_contact').fadeIn();
			$('.agent-resp-overlay').fadeIn();
			$('body').addClass('no-scroll');
		});
		});

		$('#AgentContentClose').on('click',function(){

		$('.agent_contact').fadeOut(function(){
			$('#RespBtn').fadeIn();
			$('.agent-resp-overlay').fadeOut();
			$('body').removeClass('no-scroll');
		});
		});


		;

		;
            
var image = new Image()
image.src = 'https://photos.harstatic.com/411360813/hr/img-1.jpeg?ts=2025-04-07T13:11:39.830';

image.onerror = function() {
image.src = 'https://content.harstatic.com/img/common/no_image_bg.jpg';
}
image.onload = function () {
$('.LoadingSliderComponent').hide();
$('.pd_banner_slides').show()
var listingDetailSliderDK = $('.pd_banner_slides')
listingDetailSliderDK.on('init', function(event, slick){
var currentSlide = slick.$slides[0]
var mid = $(currentSlide).data('mid')
       



});
listingDetailSliderDK.slick({
centerMode: true,
lazyLoad: 'ondemand',
centerPadding: '47px',
slidesToShow: 1,
nextArrow: '<button class="slick-next prnx-listingDK-Group slick-arrow" aria-label="Next" type="button" style="display: block;">Next</button>',
prevArrow: '<button class="slick-prev prnx-listingDK-Group  slick-arrow" aria-label="Previous" type="button" style="display: block;">Previous</button>',
responsive: [{
breakpoint: 767, // tablet breakpoint
settings: {
arrows: false,
centerPadding: '25px',

}
},
{
breakpoint: 460, // tablet breakpoint
settings: {
arrows: false,
centerPadding: '15px',
}
}
]
});

listingDetailSliderDK.on('afterChange', function(event, slick, currentSlide) {
var slide = $(slick.$slides[currentSlide]);
var mid = slide.data('mid');
if(window.mediaTag && window.mediaTag.listingHaveTags && mid){
window.mediaTag.getTags(mid,'mediaTagContainerLDk',false,undefined,true)
}
})

$(".pd_banner_slides").on("lazyLoaded", function(e, slick, image, imageSource) {
parentSlide = $(image).parent();
imageSource.src = image.attr("src"); //get source
parentSlide.css("background-image", 'url(' + imageSource + ')').addClass("loaded"); //replace with background instead
image.remove(); // remove source
});

}

var mediaGallery;
var resource_url = '/api/getMediaGallery/74257223?ng=1'
var query = '?'

mediaGallery = harload('MG').then(()=> {
MG.resource_url = resource_url
MG.listing_id = '9664623'
MG.app_id = '5'
MG.ref_id = '9664623'
MG.shareMessage = ''
MG.ads = '1'
MG.source = 'https://www.har.com/homedetail/4017-robertson-st-houston-tx-77009/3011563'
MG.init()
MG.isImagining = '1'
 
	MG.isImaginingSource = 'https://www.roomvo.com/static/scripts/b2b/har.js?v=*********'
MG.loadSpeech()
MG.locationMap()
})
$("#lead2073629195_RequestInformation").on('shown.bs.modal', function () {
    if($('#agent-lead-email-hcaptcha').length === 0){
        $('#lead2073629195_form_div').append('<div id="agent-lead-email-hcaptcha" class="lead2073629195_hCaptchaLoader pt-3"></div>');
    }

if(typeof hcaptcha === 'undefined'){
$.ajax({
url: "https://js.hcaptcha.com/1/api.js?render=explicit",
dataType: "script",
cache: true,
success: function(){
setTimeout(function(){
hcaptcha.render('agent-lead-email-hcaptcha', {
"sitekey": "802aa4a8-3568-409c-b66d-095448795f6c"
});
},1200);
}
});
} else {
setTimeout(function(){
hcaptcha.render('agent-lead-email-hcaptcha', {
"sitekey": "802aa4a8-3568-409c-b66d-095448795f6c"
})
},1500);
}
});

$("#lead2073629195_RequestInformation").on('hidden.bs.modal', function (e) {
$('#agent-lead-email-hcaptcha').remove()
})



$('.sendLead-lead2073629195').click(function(){
lead = $(this).data('lead')
$(this).prop('disabled','true');
$('#'+lead+'_canclbutton').prop('disabled','true');
        SendAgentLead(lead,(status,data) => {
        let tracking = {}
        tracking['lid'] = 9664623
        tracking['event'] = 'AgentEmailSent'
        tracking['_token'] = 'ZXXNcRKfNJkknqZKwmf8R45NHe0quWE5thuyTqFu'
        if(window.HarTracker){
            window.HarTracker.doTrack(tracking)
        } else {
            HarTracker = harload('HarTracker').then(()=> {
                HarTracker.doTrack(tracking)
            })
        }
    });
    });

$(document).on('click', '.connect_agent', function(){
agentLeadConnect($(this)[0])
})


$('.reloadFormOnList').click(function(){
setTimeout(function() {
$('#lead2073629195_result').hide()
$('#lead2073629195_agent-connect-request').hide()
$('#lead2073629195__canclbutton').show()
$('#lead2073629195_form_div').show()
$('#lead2073629195_ebutton').show().prop('disabled', false);
$('#lead2073629195_canclbutton').show().prop('disabled', false);
$('#lead2073629195_firstname').val('')
$('#lead2073629195_lastname').val('')
$('#lead2073629195_email').val('')
$('#lead2073629195_phone').val('')
hcaptcha.render('agent-lead-email-hcaptcha', {
"sitekey": "802aa4a8-3568-409c-b66d-095448795f6c"
})
}, 1100);

});

$('.phone-number-only').keyup(function(){
number = $(this).val()
number = number.replace(/[a-zA-Z!@#$%^&*`~_=]*$/,"")
$(this).val(number)
});



$.each($('.attachedLead-for-STEVENB'),function(ind,elem){
$(elem).attr('data-target','#lead2073629195_RequestInformation')
});


$('.CurrencyConverter').click(function(){
  if($('#CurrencyOptions option').length < 2){
    getAllCurrencies()
  }
$('#USDamount').val($(this).data('amount'))
$('#USDamountformatted').html($(this).data('amountformat'))

$('#CurrencyConverter').modal('show')
});


	HarTracker = harload('HarTracker').then(()=> {
		HarTracker.lid = '9664623'
		HarTracker.init()
	})

                const urlParams = new URLSearchParams(window.location.search);
                const bannerDiv   = $('#primaryMainBannerOnly');
                const primaryUrl  = bannerDiv.data('map-url');
                const fallbackUrl = bannerDiv.data('map-url-fallback');
                const imgLoader   = bannerDiv.find('.img-loader');

                if (primaryUrl) {
                    const img = new Image();
                    img.style.width = '100%'; // Ensure the image takes up 100% of the container width
                    img.style.height = '100%'; // Maintain aspect ratio by default

                    img.onload = () => {
                        // Apply height only for desktop screens
                        if (window.innerWidth >= 992) {
                            //img.style.height = '450px';
                        }

                        
                        $(img).show();
                    };

                    img.onerror = () => {
                        // Fallback to secondary URL if the primary fails
                        if (fallbackUrl) {
                            img.src = fallbackUrl;
                        } else {
                            console.error('Both primary and fallback URLs are undefined.');
                            imgLoader.text('Failed to load image.');
                        }
                    };

                    // Start loading the primary image
                    img.src = primaryUrl;
                }

               

            $('.speech-translation').click(function(event){
event.preventDefault()
code = $(this).data('language')
speech.translate(code)
});
$('.audio-share').on("click",function(event){
event.preventDefault()
share.app_id = 0;
share.app_url = '/audio-detail/9664623';
share.message = 'some important information'
share.init()
$('#SharingPopup').modal('show')

});
speech.listingId = '9664623'
speech.location = 'https://www.har.com/homedetail/4017-robertson-st-houston-tx-77009/3011563'
speech.init()
highlight.loadFeatured('74257223','STEVENB')

var reachToElement = false
$(window).scroll(function() {
    if (isLotIntoView($('.lot-info-wrapper')) && !reachToElement) {
        loadLotInfo();
        reachToElement = true

    } 
 });
 
$("#ContactOffice").on('shown.bs.modal', function () {
    if($('#office-lead-email-hcaptcha').length === 0){
     $('#lead_form_div').append('<div id="office-lead-email-hcaptcha" class="lead_hCaptchaLoader pt-3"></div>');
    }
     if(typeof hcaptcha === 'undefined'){
    $.ajax({
        url: "https://js.hcaptcha.com/1/api.js?render=explicit",
        dataType: "script",
        cache: true,
        success: function(){
               setTimeout(function(){
                hcaptcha.render('office-lead-email-hcaptcha', {
                    "sitekey": "802aa4a8-3568-409c-b66d-095448795f6c"
                });
            },1200);
        }
    });
    } else {
    setTimeout(function(){
        hcaptcha.render('office-lead-email-hcaptcha', {
			"sitekey": "802aa4a8-3568-409c-b66d-095448795f6c"
		})
	},1500);
    }
});

$("#ContactOffice").on('hidden.bs.modal', function (e) {
  $('#office-lead-email-hcaptcha').remove()
})
$('.phone-number-only').keyup(function(){
number = $(this).val()
number = number.replace(/[a-zA-Z!@#$%^&*`~_=]*$/,"")
$(this).val(number)
});



    $('.drive-time-detail').click(function(){
    if(!$('.DriveTimeComponent:visible').length){
    $('.listing_detail_cntr').animate({scrollTop: '+=350px'}, 800);
    }
    $('.DriveTimeComponent').toggle()
    })

                    if(typeof window._paq != "undefined" && typeof window._paq.push != "undefined"){
            _paq.push(['trackEvent', 'Listing', 'Listing detail language English']);
        } else {
             setTimeout(()=> {
                if(typeof window._paq != "undefined" && typeof window._paq.push != "undefined"){
                    _paq.push(['trackEvent', 'Listing', 'Listing detail language English']); 
                }   
            },3000);                        
        }
             
        $("[data-map-url]").each(function() {
            var url = this.getAttribute('data-map-url');
            var fallbackUrl = this.getAttribute('data-map-url-fallback');
            if(!url) { return; }

            var node = this;
            var image = new Image();
            var loader = this.querySelector('.img-loader');
            image.src = url;

            image.onload = function() {
                if(loader) { loader.style.display = 'none'; }
                node.style.backgroundImage = 'url(' + image.src + ')';
            }

            image.onerror = function() {
                if(loader) { loader.style.display = 'none'; }
                node.style.backgroundImage = 'url(' + fallbackUrl + ')';
            }
        });
                        $(".listing_detail_cntr .alazy").Lazy({ delay: 100  });
                    var isPopUp = $('.listing_detail_overlay').length > 0;
                    var appendScholl = isPopUp ? $('.listing_detail_cntr').get(0) : undefined;
                    //console.log('is pop up', isPopUp, $('.listing_detail_cntr').get(0));

                    // $(".listing_detail_cntr .lazy").Lazy({
                    //     appendScroll: appendScholl,
                    //     threshold: 200,
                    // });
                    $('.listing_detail_cntr').on('scroll', function() {

                    });

                    if (isPopUp) {
                        $('.listing_detail_cntr').on('scroll', function() {
                            lazyscroll();
                        });
                    } else {
                        lazyscroll();
                    }

                    // - to show and expend Current Valuations
                    $('.exprow').click(function() {
                        $(this).find('.sp_items').fadeToggle("fast", function() {
                            $(this).parent().toggleClass('open');
                        });
                    });

                    // MAPS
                    // Property Map
                    $('#propertyMapImage').attr('data-loader', 'load');
                    $('#propertyMapImage').Lazy({
                        appendScroll: appendScholl,
                        threshold: 200,
                        load: function(element, response) {
                            var icon = 'https://content.harstatic.com/resources/images/listing_details/placeicons/property.png';
                            var url = "/api/staticmap?maptype=hybrid&markers=icon:" + icon + "|29.797257000000000,-95.357419000000000&size=763x365";
                            var image = new Image();
                            image.onload = function() {
                                $('#propertyMapImage').attr('src', url);
                                response(true);
                            }
                            image.src = url;
                        }
                    });
                    // Lot Information
                    function lazyscroll() {
                        $('.listing_detail_cntr .lazy').Lazy({
                            threshold: 200,
                            beforeLoad: function(element) {},
                            afterLoad: function(element) {

                                if ($(element).data('contentname') && $(element).data('contentname').indexOf('slider_') !== -1) {
                                    //apply slider
                                    $(element).find('.property').slick({
                                        dots: false,
                                        infinite: false,
                                        slidesToShow: 3,
                                        variableWidth: true,
                                        slidesToScroll: 1,
                                        appendDots: false
                                    });
                                }
                            },
                            onError: function(element) {
                                console.log('Issue Reported');
                            },
                            onFinishedAll: function() {}
                        });
                    }


                    $(".property").slick({
                        dots: false,
                        infinite: false,
                        slidesToShow: 3,
                        variableWidth: true,
                        slidesToScroll: 1,
                        appendDots: false,
                    });

                    $(".openMapView").click(function(e) {
                        e.preventDefault();
                        ReactiveMap.initialize().then(function() {
                            $('#LargeMapModal').modal('show');
                            $("#LargeMapModal .map-type option[value='roadmap']").prop('selected', true);
                            reactiveStreetView.hide();
                            //window.StreetView(false);
                            largeMapPromise.then(function(largeMap) {
                                largeMap.map.mapTypeId = 'roadmap';
                                $('#LargeMapModal').one('shown.bs.modal', function(e) {
                                    largeMap.map.resize();
                                });
                            });
                        });
                        /*setTimeout( function(){

                        },300);*/
                    });

                    $(".openSatelliteView").click(function(e) {
                        e.preventDefault();
                        ReactiveMap.initialize().then(function() {
                            $('#LargeMapModal').modal('show');
                            $("#LargeMapModal .map-type option[value='satellite']").prop('selected', true);
                            reactiveStreetView.hide();
                            //window.StreetView(false);
                            largeMapPromise.then(function(largeMap) {
                                largeMap.map.mapTypeId = 'satellite';
                                $('#LargeMapModal').one('shown.bs.modal', function(e) {
                                    largeMap.map.resize();
                                });
                            });
                        });

                        /*setTimeout( function(){
                            largeMap.mapTypeId = 'satellite';
                            largeMap.resize();
                        }, 300);*/
                    });



                    //--Fix refresh issues that do not load lazy content. - Lee--
                                    $(".listing_detail_cntr").trigger('scroll');
                                                                                        			
                        $(document).on('click','.registerLogin',function(e) {
        e.stopPropagation();
        appId = $(this).data('appid');

        if(typeof registerLoginData[appId] == 'undefined') {
            registerLoginData[appId] = {
                msgId: 0,
                appId: 0,
                referer: '',
                onLoginAnchorTo: '',
                onLoginJumpTo: '',
                nextUrl: '',
                content: '',
                heading: ''
            };
        }

        registerLoginData[appId].msgId = $(this).data('msgid');
        registerLoginData[appId].appId = $(this).data('appid');
        registerLoginData[appId].referer = $(this).data('referer');

        // If onLoginJumpTo (priority over onLoginAnchorTo) is set, then onLoginAnchorTo will be ignored
        if($(this).data('jump-to') != '') {
            registerLoginData[appId].onLoginAnchorTo = '';
            registerLoginData[appId].onLoginJumpTo = $(this).data('jump-to');
        } else {
            registerLoginData[appId].onLoginAnchorTo = $(this).data('anchor-to');
            registerLoginData[appId].onLoginJumpTo = '';
        }

        registerLoginData[appId].nextUrl = encodeURIComponent(registerLoginData[appId].referer + $(this).data('anchor-to') + $(this).data('jump-to'));
        registerLoginData[appId].content = $(this).data('content');
        registerLoginData[appId].heading = $(this).data('heading');

        $.get( `https://www.har.com/api/applogin/flash?msgid=${registerLoginData[appId].msgId}&appid=${registerLoginData[appId].appId}`, function( data ) {
            $('#registerLogin').modal('show');
        });
    });

    $('#registerLogin').on('show.bs.modal', function (e) {
        $('.socialLinks').each(function() {
            $(this).attr('href', $(this).data('social-link') + `?nexturl=${registerLoginData[appId].nextUrl}`);
        });
        $('.rlCreateAccount').attr('href', `/login/createaccount?nexturl=${registerLoginData[appId].nextUrl}`);
        $('.rlSingIn').attr('href', `https://www.har.com/login?nexturl=${registerLoginData[appId].nextUrl}`);
        $('.rlLoginContent').html(registerLoginData[appId].content);
        $('.rlHeading').html(registerLoginData[appId].heading);
    });
			$('#CloseAgentBlock').click(function() {
				$('input.js_typeahead').val('')
				$('input[name="agent_connect_id"]').val('')
				$('input[name="agent_connect_name"]').val('')
				$('#AgentBox').hide();
				$('#IfAgentYes').fadeIn();
				if($('#CD-connecting-agent-name').length > 0) {
					$('#CD-connecting-agent-name').html('').hide()
				}
				if($('#CD-listing-agent-name').length > 0) {
					$('#CD-listing-agent-name').show()
				}
			});

			$('input[name="agent_yn"]').change(function() {
				tempValue = $('input[name="agent_yn"]:checked').val();
				if (tempValue == 'true') {
					$('#IfAgentYes').fadeIn();
				} else {
					$('#IfAgentYes').fadeOut();
					$('input[name="agent_connect_id"]').val('')
					$('input[name="agent_connect_name"]').val('')
					if($('#CD-connecting-agent-name').length > 0) {
						$('#CD-connecting-agent-name').html('').hide()
					}
					if($('#CD-listing-agent-name').length > 0) {
						$('#CD-listing-agent-name').show()
					}
					$('#first_name').val('')
					$('.agent-card-jr').html('')
					$('#AgentBox').hide()
				}
			});

			$('input.js_typeahead').keyup(function() {
				if ($(this).val() == '') {
					$('input[name="agent_connect_id"]').val('')
					$('input[name="agent_connect_name"]').val('')
					$('.agent-card-jr').html('')
					$('#AgentBox').hide()


				}
			})
            $('input.js_typeahead').each(function() {
			var myTypeahead = $(this).typeahead({
				classNames: {
					menu: 'depth depth--standed_out overflow-hidden border_radius--default typeahead_custom bg-white',
				},
				hint: false,
				highlight: false,
				minLength: 1,
			}, {
				display: 'myval',
				source: function show(q, cb, cba) {
					inputsource = myTypeahead.attr('data-source');
					if (inputsource == 'cityzip' || inputsource == 'findcityzip') {
						if ($.isNumeric(q) && typeof q != 'undefined') {
							inputsource = 'zip'
						} else {
							inputsource = 'city'
						};
					}
					var url = '/api/typeapp/memberfinder?for=' + inputsource + '&query=' + encodeURIComponent(q);
					$.ajax({
							url: url
						})
						.success(function(data) {
							cba(data);
						})
						.done(function(res) {})
						.fail(function(err) {});
				},
				limit: 10,
				templates: {
					empty: [
						''
					].join('\n'),
					suggestion: function(data) {
						var label = data.mytype;
						if (typeof data.myval != 'undefined' && typeof data.mymember != 'undefined')
							return '<div class="card p-4 mb-0 removeLink">' + data.agentcard + '</div>';
					}
				}
			});
		});
		$('input.js_typeahead').on('typeahead:selected', function(evt, item) {
			$('input[name="agent_connect_id"]').val(item.mymember)
			$('input[name="agent_connect_name"]').val(item.myval)
			if($('#CD-connecting-agent-name').length > 0) {
				$('#CD-connecting-agent-name').html(item.myval).show()
			}
			if($('#CD-listing-agent-name').length > 0) {
				$('#CD-listing-agent-name').hide()
			}
			$('.agent-card-jr').html(item.agentcard)
			$('#AgentBox').show()
			$('#IfAgentYes').hide()
    	});
        $('.agent-request-callback').click(function(){
            window.location.href= '/login?nexturl=https%3A%2F%2Fwww.har.com%2Fhomedetail%2F4017-robertson-st-houston-tx-77009%2F3011563'
        });
         
$("#SharingPopup").on('shown.bs.modal', function () {
    if(typeof hcaptcha === 'undefined'){
    $.ajax({
        url: "https://js.hcaptcha.com/1/api.js?render=explicit",
        dataType: "script",
        cache: true,
        success: function(){
            setTimeout(function(){
                if($("#share-email-hcaptcha").length > 0){
                    hcaptcha.render('share-email-hcaptcha', {
                        "sitekey": "802aa4a8-3568-409c-b66d-095448795f6c"
                    })
                }
                if($("#share-text-hcaptcha").length > 0){
                    hcaptcha.render('share-text-hcaptcha', {
                        "sitekey": "802aa4a8-3568-409c-b66d-095448795f6c"
                    })
                }

            },1500);
        }
    });
    } else {
        setTimeout(function(){
            if($("#share-email-hcaptcha").length > 0){
                hcaptcha.render('share-email-hcaptcha', {
                    "sitekey": "802aa4a8-3568-409c-b66d-095448795f6c"
                })
            }
            if($("#share-text-hcaptcha").length > 0){
                hcaptcha.render('share-text-hcaptcha', {
                    "sitekey": "802aa4a8-3568-409c-b66d-095448795f6c"
                })
            }

        },2000);
    }
});

$('body').on('keyup', '#FromEmail,#Recipientemail,#Recipienttext', function(ev) {
//share.singleValidate($(this))
});

$('body').on('click', '.SharetoClient', function(ev) {
$('#SharingPopup').modal('hide')
form = {}
form['message'] = $('#shareMailMessage').val()
form['url'] = $('input[name="share_url"]').val()
share.shareToClientForm(form)
$('#shareToClient').modal('show')
});

$('body').on('click', '.sendToClientBtn', function(ev) {
	var contacts = [];
	
	
		for (let key in thisCrmLogableShare[1].contactList) {
			if (thisCrmLogableShare[1].contactList.hasOwnProperty(key)) {
				thisCrmLogableShare[1].contactList[key].firstname = $(`.firstname_${thisCrmLogableShare[1].contactList[key]['classid']}`).val();
				thisCrmLogableShare[1].contactList[key].lastname = $(`.lastname_${thisCrmLogableShare[1].contactList[key]['classid']}`).val();

				if (thisCrmLogableShare[1].contactList[key].firstname == '') {
					$(`.firstname_${thisCrmLogableShare[1].contactList[key]['classid']}`).css('border', '2px solid red');
					if (thisCrmLogableShare[1].contactList[key].contact_id === null) {
						return false;
					}
				} else {
					$(`.firstname_${thisCrmLogableShare[1].contactList[key]['classid']}`).css('border', ''); // Remove highlight if not empty
				}
			}
		}

		if(thisCrmLogableShare[1].contactList.length == 0){
			bootbox.alert('Please select a contact');
			return false;
		}
		contacts.push(thisCrmLogableShare[1].contactList);
	

	data = { };
	
		data['contacts'] = JSON.stringify(contacts)
	

	data['title'] = ''
	data['subject'] = 'Share information from HAR.com'
	data['message'] = $('#shareToClientMessage').val().replace("\n", "<br />\r\n")
	
		data['ver'] = 'v2'
	
	$.ajax({
		url: '/api/sentToClientMail',
		type: "POST",
		datatype: "json",
		data: data,
		beforeSend: function() {
			$('.sendToClientBtn').prop('disabled', true);
		},
		complete: function() {
			$('.sendToClientBtn').prop('disabled', false);
		},
		success: function(json) {
			json = $.parseJSON(json);
			$('.share-form').html(resp.html);
			$('.share-form-loading').hide();
			if (json.status == 'ok') {
				
				$('.shareToClient-form').find('#senttoclient_div').hide();

					$(`.crmTypeaHeadSelected_1`).html('');
					//$(`#labelHeadingCrm_1`).val('');
					thisCrmLogableShare[1].contactList = [];
					
				
				$('.shareToClient-form').find('#shareToClientform-response').html(json.message).show();
			}
		}
	});
});


$(document).on("keyup", "#Recipienttext", function() {
	number = $(this).val()
	number = number.replace(/[a-zA-Z!@#$%^&*`~_=]*$/,"")
	$(this).val(number)
});


$('body').on('keyup', '#text_message', function(ev) {
	max_count = 90;
	$('#text_message').val($('#text_message').val().substring(0, max_count));
});

    <!-- Common JS for Top Bar Start -->
    $('#HamIcon,.resp_nav_close').click(function() {
        $('body').toggleClass('menu_opened');
    });

    $(document).on("click touchStart",".nav-link",function(){
        $(this).toggleClass('menu_item_open');
    });

    $(".nav-item.dropdown").on("show.bs.dropdown", function() {
        $('#MainNavBar').addClass('maintab_opened');
    });

    $(".nav-item.dropdown").on("hide.bs.dropdown", function() {
        $('#MainNavBar').removeClass('maintab_opened');
    });

    <!-- this script is to open the menu overlay on desktop -->
	$(".har-nav .nav-item.dropdown").on("show.bs.dropdown", function(event){
	  $('body').addClass('ds_menu_open');
	});
	$(".har-nav .nav-item.dropdown").on("hide.bs.dropdown", function(event){
	  $('body').removeClass('ds_menu_open');
	});

    $(".resp_user_menu .nav-link").click(function() {
	    $('#MainNavBar').animate({
	        scrollTop: parseInt($(".resp_user_menu").offset().top)
	    }, 800);
	});
    <!-- Common JS for Top Bar End -->

;

            if($("input").hasClass("focus_input")) {
                var focus_input = $(".focus_input").val();
                if ($(".focus_input")[0].type != "email") {
                    $(".focus_input")[0].focus();
                    $(".focus_input")[0].setSelectionRange(focus_input.length, focus_input.length);
                }
                else{
                    $(".focus_input")[0].focus();
                }
            }
                    });
        var gpsAjaxRequest;
        function getGpsLocation() {
            if(gpsAjaxRequest){
                return gpsAjaxRequest;
            }
            var url = 'https://www.har.com/api/get-location';
            return gpsAjaxRequest = $.ajax({
                url: url,
                complete:function(){
                    gpsAjaxRequest = undefined;
                },
                success: function(result) {
                    if(result['country'] !== 'United States'){
                        bootbox.alert('Your requested location is not valid!');
                    }
                    else if(result['getGps'][0] == '' || result['getGps'][1] == ''){
                        bootbox.alert('Your requested location has no valid Lat and Long!');
                    }
                    else{
                        $('.getcityzip').val(result['getZip']);
                        $('.getlatitude').val(result['getGps'][0]);
                        $('.getlongitude').val(result['getGps'][1]);
                        //$('#dtDestination_ll').val(result['getGps'][0]);
                        $('#dtDestination_ll').val(result['getGps'][0]+','+result['getGps'][1]);
                    }
                }
            })
        }

        function ShowEmail(eparam1, eparam2, element, params = '') {
            if(eparam1 && eparam2) {
                let email = eparam1 + '@' + eparam2;
                $(element).parent().html("<a href='mailto:"+ (email + params) +"'>"+ email +"</a>");
            }
        }
    </script>
<script defer type="application/javascript" src="https://io.clickguard.com/s/cHJvdGVjdG9y/bU7kEKP5"></script>
<script>
 var _mtm = window._mtm = window._mtm || [];
_mtm.push({'mtm.startTime': (new Date().getTime()), 'event': 'mtm.Start'});
var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
g.async=true; g.src='https://matomo.har.com/js/container_DZtB0Z0R.js'; s.parentNode.insertBefore(g,s);
</script>


<script defer type="application/javascript">
(function () {
    window.cg_convert = function (x, y) { if (window._cg_convert) window._cg_convert(x || null, y || null); else setTimeout(function () { cg_convert(x || null, y || null) }, 500); };
})();
</script>

<div id="FavoritesModal" class="modal fade modal--medium" tabindex="-1" role="dialog" aria-labelledby="FavoritesModal" style="display: none;z-index:3147483649" aria-hidden="true">
  <div class="modal-dialog modal-dialog-scrollable modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 tabindex="0" class="modal-title" id="bookmark_action_title">Manage Favorite</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><img loading="lazy" fetchPriority="low" alt="Close" src="https://content.harstatic.com/media/icons/icon-close-auxiliary.svg"></button>
      </div>
      <div class="modal-body text-left">
      
        <div class="row">
	        <div id="currentfavhtm" class="col-md-5 col-12 order-md-0 order-1" >
		       
	        </div>
            <div class="col-md col-12 order-md-1 order-0 mb-5 mb-md-0">
				<div id="folders_add" class="pb-3" style="display:none";>
				   <a href="#" class="font_weight--bold_extra font_size--medium open_folders_edit">
				       Add folder(s) →
					</a>
				</div>
		        <div id="folders_edit" class="pb-3">
			        <div class="font_weight--bold_extra font_size--medium d-inline mr-2">Folders</div>
			        <a href="#" class="font_weight--bold font_size--small open_folders_edit">
				       [ Edit ]
					</a>
		        </div>
                <input id="bookmark_current_lid" val="" type="hidden">
                <input id="bookmark_current_mls" val="" type="hidden">
                <input id="bookmark_current_bookmarked" val="" type="hidden">
                <input id="bookmark_current_app" val="" type="hidden">
                <input id="bookmark_notify_opt" val="" type="hidden">
		        <div class="pb-3" id="FolderEdit" style="display: none;">
					<div class="card position-relative" style="min-height:140px;">
		  				
		  				<a class="position-absolute folder_edit_close" href="javascript:void(0);" style="top:5px; left:auto; right:10px;z-index: 111;"><img style="width:12px;" src="https://content.harstatic.com/media/icons/close_black.svg" alt="close icon"></a>		  				
		  				
				  		<div class="pl-4 pr-4 pt-3 flex-grow-1">
					  		<div id="current_bookmark_folders" class="row small-gutters">
						  		
					  		</div>
					        
						</div>
						<div class="border-top pl-4 pr-4 pt-2 pb-2">
							<div class="row">
								<div class="col">
									<a href="#" id="CreateNewFolder" class="font_weight--bold">Create new folder</a>
								</div>
								<!--<div class="col-auto"><a href="#" class="font_weight--bold">Done</a></div>-->
                                 <div class="col-auto"><button type="button" class="btn btn--small-extra btn--primary folder_edit_close">Save</button></div>
							</div>
							
							<div id="AddNewFolder" class="pt-2 pb-2" style="display:none;">
								<div class="row no-gutters">
									<div class="col pr-3">										
                                        <input type="text" class="form-control w-100" id="bookmark_foldername" aria-describedby="foldername" placeholder="Give your folder a name...">
									</div>
									<div class="col">
										<button role="button" class="btn btn--ordinary cmd--create-folder">Add</button>
									</div>
                                   
								</div>
							</div>
						</div>
			  		</div>
		        </div>
				
				<div id="FolderList">
			        <div id="user_folder_tags" class="d-flex flex-wrap align-items-center mb-md-4 mb-3">								  	
			        </div>
				</div>		        
		        <div class="font_weight--bold_extra font_size--medium mb-3">Email Notifications</div>		
                <div class="dropdown">
                    <a href="#" class="cardv2 p-2  dropdown-toggle-split" role="button" id="notiftoggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="max-width:294px;">
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <img class="mr-2" src="https://content.harstatic.com/media/icons/buttons/cell_notifications.svg" alt="cell notifications icon">
                            </div>
                            <div id="notifmsg" class="col font_size--small">
                                Notify me when listing Status/Price<br>changes or has an Open House
                            </div>
                           <div class="col-auto">
					        <img class="mr-2" style="width:8px;" src="https://content.harstatic.com/media/icons/select_arrow.svg" alt="down arrow icon">
				           </div>
                        </div>                        
                    </a>
                    <div class="dropdown-menu" aria-labelledby="notiftoggle">
                        <a class="dropdown-item notifopt" data-notifyopt="3" href="#"><span class="font_size--small">Notify me when Status/Price changes<br>or has an Open House</span></a>
                        <a class="dropdown-item notifopt" data-notifyopt="1" href="#"><span class="font_size--small">Notify me when Status or Price changes</span></a>
                        <a class="dropdown-item notifopt" data-notifyopt="2" href="#"><span class="font_size--small">Notify me when an Open House is added</span></a>
                        <a class="dropdown-item notifopt" data-notifyopt="0" href="#"><span class="font_size--small">Disable Listing Notfications</span></a>
                    </div>
                </div>		        
		        <div class="font_weight--bold_extra font_size--medium mb-3">Your notes</div>
		        <div class="note_block">						  
				  <div class="note_block_innter position-relative">
					<div style="display: none;" class="save_label bg_color_available_light color_available border_radius--mini"><img style="width:10px;" src="https://content.harstatic.com/media/icons/check_mark_green.svg" alt="check mark green"> Saved</div>
				  	<textarea class="form-control NoteInput" id="bookmark_current_note" style="font-size:14px;font-weight:500 !important;line-height:1.4" rows="5" placeholder="Write some notes..."></textarea>
				  	<a class="pt-2 nb_readmore font_weight--bold"  href="#">read more</a>
				  </div>
				  <div class="pt-2 text-right pb-2 EditBar" style="display:none">
					  <a class="mr-3 ClostEditMode" href="javascript:void(0);"><img style="width:11px;" src="https://content.harstatic.com/media/icons/close_gray.svg" alt="close icon"></a>
					  <button type="button" class="btn btn--small-extra btn--primary ClostEditMode">Save</button>
				  </div>
			  </div>		        
	        </div>            
        </div>
      </div>
      <!--<div class="modal-footer text-center">
        <button role="button" class="btn btn--shapeless btn--medium" data-dismiss="modal" aria-label="Cancel">Cancel</button>
        <button role="button" class="btn btn--primary btn--medium" aria-label="Apply">Apply</button>
      </div>-->
      <div class="modal-footer text-center">
        <!--<button type="button" tabindex="0" id="openaddfoldbottom" class="btn_openaddfolder btn btn--ordinary btn--icon btn--icon--folder">Create New Folder</button>-->
        <button role="button" class="btn btn--primary btn--medium cmd--save-bookmark ml-auto mr-0" aria-label="Apply">Done</button>
        <button role="button" style="display:none" class="btn btn--secondary btn--medium cmd--del-bookmark ml-0 mr-auto" aria-label="Remove Favorite">Remove</button>
        <button role="button" style="display:none" class="btn btn--primary btn--medium cmd--update-bookmark" aria-label="Update Favorite">Update</button>
      </div>
    </div>
  </div>
</div>
<span id="othermodals"></span>

 
<script async defer type="text/javascript" src="/js/favorites.min.js?v=*********"></script>


<script type="text/javascript">
 var RecaptchaOptions = {
    theme : 'white'
 };
 function loadTerms(){
	$('#terms').attr('src','/content/termsofuse');
 }
  function loadPrivacy(){
	$('#privacy').attr('src','/content/privacy');
 }

 $("#manageketch").on("click", function(){
    ketch('showPreferences');
 });
 window.awsWafCookieDomainList = ['.har.com', '.showingsmart.com', '.hartech.io'];
 </script>
 <script type="text/javascript" src="https://4f52a7adb9cb.us-east-1.captcha-sdk.awswaf.com/4f52a7adb9cb/jsapi.js" defer></script>
<span id="bugherdbox" style="display:none">
<a href="#" onclick="_bugHerd.win.bugherd.applicationView.anonymousbar.toggleOptions();return false;" class="feedback_btn"><img loading="lazy" fetchPriority="low" class="w-100" src="https://content.harstatic.com/media/common/<EMAIL>" alt="facebook" /></a>
</span>


    <script type="text/plain" data-purposes="analytics">
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '319180425302450');
    fbq('track', 'PageView');
    </script>

</body>

</html>
