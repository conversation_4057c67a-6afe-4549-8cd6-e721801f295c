import json
import os
from collections import Counter
import numpy as np

def analyze_buildings():
    # Path to the buildings file
    filepath = os.path.join('public', 'data', 'osm', 'saoPao', 'sp_buildings_3d.geojson')
    
    print("Analyzing São Paulo building data...")
    
    # Load the GeoJSON file
    with open(filepath, 'r') as f:
        data = json.load(f)
    
    # Basic statistics
    total_buildings = len(data['features'])
    print(f"\nTotal buildings: {total_buildings:,}")
    
    # Analyze building types
    building_types = Counter()
    for feature in data['features']:
        building_type = feature['properties'].get('building', 'unknown')
        building_types[building_type] += 1
    
    print("\nBuilding types:")
    for btype, count in building_types.most_common():
        print(f"- {btype}: {count:,} ({count/total_buildings*100:.1f}%)")
    
    # Analyze heights
    heights = []
    levels = []
    for feature in data['features']:
        if feature['properties'].get('height'):
            heights.append(feature['properties']['height'])
        if feature['properties'].get('levels'):
            try:
                levels.append(float(feature['properties']['levels']))
            except (ValueError, TypeError):
                pass
    
    if heights:
        print("\nHeight statistics (meters):")
        print(f"- Buildings with height data: {len(heights):,} ({len(heights)/total_buildings*100:.1f}%)")
        print(f"- Average height: {np.mean(heights):.1f}")
        print(f"- Median height: {np.median(heights):.1f}")
        print(f"- Min height: {min(heights):.1f}")
        print(f"- Max height: {max(heights):.1f}")
    
    if levels:
        print("\nBuilding levels statistics:")
        print(f"- Buildings with level data: {len(levels):,} ({len(levels)/total_buildings*100:.1f}%)")
        print(f"- Average levels: {np.mean(levels):.1f}")
        print(f"- Median levels: {np.median(levels):.1f}")
        print(f"- Min levels: {min(levels):.1f}")
        print(f"- Max levels: {max(levels):.1f}")
    
    # Analyze coordinates
    all_coords = []
    for feature in data['features']:
        if feature['geometry']['type'] == 'Polygon':
            coords = feature['geometry']['coordinates'][0]
            all_coords.extend(coords)
    
    if all_coords:
        lons, lats = zip(*all_coords)
        print("\nGeographic bounds:")
        print(f"- Latitude range: {min(lats):.4f} to {max(lats):.4f}")
        print(f"- Longitude range: {min(lons):.4f} to {max(lons):.4f}")
    
    # Sample of properties
    print("\nSample of building properties:")
    sample = data['features'][0]['properties']
    for key, value in sample.items():
        if key != 'tags':  # Skip the full tags dict
            print(f"- {key}: {value}")

if __name__ == "__main__":
    analyze_buildings() 