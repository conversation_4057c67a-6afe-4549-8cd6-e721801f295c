import json
import sys

def handle_test_tool(params):
    message = params.get('message', '')
    return {
        "status": "ok",
        "message": f"Received message: {message}",
        "echo": message
    }

def main():
    print("MCP server started", file=sys.stderr)
    while True:
        try:
            line = sys.stdin.readline()
            if not line:
                break
            
            request = json.loads(line)
            print("Received request:", request, file=sys.stderr)
            
            method = request.get('method')
            params = request.get('params', {})
            
            if method == 'test_tool':
                response = handle_test_tool(params)
            else:
                response = {"error": f"Unknown method: {method}"}
            
            print(json.dumps(response))
            sys.stdout.flush()
            
        except Exception as e:
            print(json.dumps({"error": str(e)}))
            sys.stdout.flush()

if __name__ == "__main__":
    main() 