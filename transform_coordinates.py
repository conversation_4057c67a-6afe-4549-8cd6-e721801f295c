import json
from pyproj import Transformer

def transform_coordinates():
    # Create a transformer from UTM zone 23S to WGS84
    transformer = Transformer.from_crs("EPSG:32723", "EPSG:4326", always_xy=True)
    
    # Read the original GeoJSON file
    with open('remote/public/data/osm/saoPao/distrito.geojson', 'r') as f:
        data = json.load(f)
    
    # Transform coordinates for each feature
    for feature in data['features']:
        if feature['geometry']['type'] == 'Polygon':
            # Transform each ring in the polygon
            for ring in feature['geometry']['coordinates']:
                for i, coord in enumerate(ring):
                    x, y = transformer.transform(coord[0], coord[1])
                    ring[i] = [x, y]
        elif feature['geometry']['type'] == 'MultiPolygon':
            # Transform each polygon in the multipolygon
            for polygon in feature['geometry']['coordinates']:
                for ring in polygon:
                    for i, coord in enumerate(ring):
                        x, y = transformer.transform(coord[0], coord[1])
                        ring[i] = [x, y]
    
    # Write the transformed data to a new file
    with open('remote/public/data/osm/saoPao/distrito_wgs84.geojson', 'w') as f:
        json.dump(data, f)

if __name__ == '__main__':
    transform_coordinates() 