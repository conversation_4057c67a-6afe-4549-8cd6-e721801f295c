import overpy
import json
import time
import os

def categorize_school(tags):
    # Try to determine school type from tags
    if 'school:level' in tags:
        return tags['school:level']
    if 'isced:level' in tags:
        return tags['isced:level']
    if 'amenity' in tags and tags['amenity'] == 'university':
        return 'university'
    if 'amenity' in tags and tags['amenity'] == 'college':
        return 'college'
    if 'amenity' in tags and tags['amenity'] == 'kindergarten':
        return 'kindergarten'
    # Guess from name
    name = tags.get('name', '').lower()
    if 'elementary' in name:
        return 'elementary'
    if 'middle' in name:
        return 'middle'
    if 'high' in name:
        return 'high'
    if 'academy' in name:
        return 'academy'
    if 'charter' in name:
        return 'charter'
    return 'school'

def download_austin_schools():
    api = overpy.Overpass(url='https://overpass.kumi.systems/api/interpreter')
    # Austin bounding box
    lat_min, lon_min = 30.0, -98.0
    lat_max, lon_max = 30.5, -97.5
    bbox = f"{lat_min},{lon_min},{lat_max},{lon_max}"
    
    query = f'''
    [out:json][timeout:300];
    (
      node["amenity"="school"]({bbox});
      node["amenity"="university"]({bbox});
      node["amenity"="college"]({bbox});
      node["amenity"="kindergarten"]({bbox});
    );
    out body;
    >;
    out skel qt;
    '''
    
    print("[INFO] Starting download of Austin schools from OpenStreetMap...")
    print(f"[INFO] Bounding box: {bbox}")
    print("[INFO] Querying OSM Overpass API...")
    
    max_retries = 3
    retry_delay = 10
    for attempt in range(max_retries):
        try:
            print(f"[INFO] Attempt {attempt+1} of {max_retries}...")
            result = api.query(query)
            print(f"[INFO] Query successful on attempt {attempt+1}.")
            break
        except overpy.exception.OverpassGatewayTimeout:
            if attempt < max_retries - 1:
                print(f"[WARN] Timeout, retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                print("[ERROR] Failed after retries.")
                return
        except Exception as e:
            print(f"[ERROR] {e}")
            return
    
    features = []
    print(f"[INFO] Processing {len(result.nodes)} nodes...")
    for idx, node in enumerate(result.nodes):
        try:
            if idx % 50 == 0:
                print(f"[INFO] Processed {idx} of {len(result.nodes)} nodes...")
            feature = {
                'type': 'Feature',
                'geometry': {
                    'type': 'Point',
                    'coordinates': [float(node.lon), float(node.lat)]
                },
                'properties': {
                    'name': node.tags.get('name', 'Unnamed'),
                    'category': categorize_school(node.tags),
                    'tags': node.tags
                }
            }
            features.append(feature)
        except Exception as e:
            print(f"[ERROR] Processing node {node.id}: {e}")
            continue
    
    os.makedirs(os.path.join('public', 'data', 'osm'), exist_ok=True)
    geojson = {
        'type': 'FeatureCollection',
        'features': features
    }
    filepath = os.path.join('public', 'data', 'osm', 'austin_schools.geojson')
    with open(filepath, 'w') as f:
        json.dump(geojson, f)
    print(f"\n[SUCCESS] {len(features)} schools saved to {filepath}")

if __name__ == "__main__":
    download_austin_schools() 