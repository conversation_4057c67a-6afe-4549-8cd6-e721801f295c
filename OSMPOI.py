import overpy
import json
import time
import os
from math import ceil

def download_osm_pois():
    api = overpy.Overpass(url='https://overpass.kumi.systems/api/interpreter')
    
    # Austin coordinates
    lat_min, lon_min = 30.0, -98.0  # SW corner
    lat_max, lon_max = 30.5, -97.5  # NE corner
    
    # Split area into 3x3 grid for better performance
    lat_step = (lat_max - lat_min) / 3
    lon_step = (lon_max - lon_min) / 3
    
    # Initialize feature collections for different POI categories
    poi_features = {
        'logistics': [],  # Logistics and industrial
        'transportation': [],  # Transportation hubs
        'commercial': [],  # Commercial areas
        'services': [],  # Services and amenities
        'education': [],  # Educational institutions
        'healthcare': [],  # Healthcare facilities
        'leisure': [],  # Leisure and recreation
        'other': []
    }
    
    print("Downloading Austin POI data from OpenStreetMap in chunks...")
    
    for i in range(3):
        for j in range(3):
            chunk_lat_min = lat_min + (i * lat_step)
            chunk_lat_max = lat_min + ((i + 1) * lat_step)
            chunk_lon_min = lon_min + (j * lon_step)
            chunk_lon_max = lon_min + ((j + 1) * lon_step)
            
            bbox = f"{chunk_lat_min},{chunk_lon_min},{chunk_lat_max},{chunk_lon_max}"
            
            # Enhanced query for more POIs
            poi_query = f"""
            [out:json][timeout:300];
            (
              // Logistics and Industrial
              node["landuse"="industrial"]({bbox});
              node["industrial"="logistics"]({bbox});
              node["industrial"="warehouse"]({bbox});
              node["building"="warehouse"]({bbox});
              node["building"="industrial"]({bbox});
              
              // Transportation
              node["amenity"="bus_station"]({bbox});
              node["amenity"="subway_entrance"]({bbox});
              node["railway"="station"]({bbox});
              node["railway"="freight_yard"]({bbox});
              node["railway"="yard"]({bbox});
              node["highway"="bus_stop"]({bbox});
              node["amenity"="taxi"]({bbox});
              node["amenity"="ferry_terminal"]({bbox});
              
              // Commercial
              node["shop"]({bbox});
              node["amenity"="marketplace"]({bbox});
              node["amenity"="mall"]({bbox});
              node["building"="commercial"]({bbox});
              node["building"="retail"]({bbox});
              
              // Services
              node["amenity"="bank"]({bbox});
              node["amenity"="post_office"]({bbox});
              node["amenity"="courthouse"]({bbox});
              node["amenity"="police"]({bbox});
              node["amenity"="fire_station"]({bbox});
              node["amenity"="townhall"]({bbox});
              
              // Education
              node["amenity"="school"]({bbox});
              node["amenity"="university"]({bbox});
              node["amenity"="college"]({bbox});
              node["amenity"="kindergarten"]({bbox});
              node["amenity"="library"]({bbox});
              
              // Healthcare
              node["amenity"="hospital"]({bbox});
              node["amenity"="clinic"]({bbox});
              node["amenity"="pharmacy"]({bbox});
              node["amenity"="doctors"]({bbox});
              node["amenity"="dentist"]({bbox});
              
              // Leisure
              node["leisure"="park"]({bbox});
              node["leisure"="garden"]({bbox});
              node["leisure"="sports_centre"]({bbox});
              node["leisure"="fitness_centre"]({bbox});
              node["amenity"="cinema"]({bbox});
              node["amenity"="theatre"]({bbox});
              node["amenity"="museum"]({bbox});
              node["amenity"="arts_centre"]({bbox});
              node["amenity"="stadium"]({bbox});
              
              // Food and Drink
              node["amenity"="restaurant"]({bbox});
              node["amenity"="cafe"]({bbox});
              node["amenity"="bar"]({bbox});
              node["amenity"="fast_food"]({bbox});
              node["amenity"="food_court"]({bbox});
              
              // Religious
              node["amenity"="place_of_worship"]({bbox});
              node["building"="church"]({bbox});
              node["building"="mosque"]({bbox});
              node["building"="temple"]({bbox});
              
              // Accommodation
              node["tourism"="hotel"]({bbox});
              node["tourism"="hostel"]({bbox});
              node["tourism"="guest_house"]({bbox});
              
              // Other Important
              node["amenity"="fuel"]({bbox});
              node["amenity"="car_wash"]({bbox});
              node["amenity"="car_repair"]({bbox});
              node["amenity"="parking"]({bbox});
              node["amenity"="waste_basket"]({bbox});
              node["amenity"="waste_disposal"]({bbox});
              node["amenity"="recycling"]({bbox});
            );
            out body;
            >;
            out skel qt;
            """
            
            max_retries = 3
            retry_delay = 10  # seconds
            
            for attempt in range(max_retries):
                try:
                    print(f"Downloading chunk {(i*3)+j+1}/9...")
                    result = api.query(poi_query)
                    
                    # Process nodes
                    for node in result.nodes:
                        try:
                            feature = {
                                'type': 'Feature',
                                'geometry': {
                                    'type': 'Point',
                                    'coordinates': [float(node.lon), float(node.lat)]
                                },
                                'properties': {
                                    'name': node.tags.get('name', 'Unnamed'),
                                    'type': node.tags.get('amenity') or node.tags.get('shop') or node.tags.get('leisure'),
                                    'category': categorize_poi(node.tags),
                                    'description': node.tags.get('description', ''),
                                    'website': node.tags.get('website', ''),
                                    'phone': node.tags.get('phone', ''),
                                    'opening_hours': node.tags.get('opening_hours', ''),
                                    'wheelchair': node.tags.get('wheelchair', ''),
                                    'osm_id': node.id,
                                    'osm_type': 'node',
                                    'tags': node.tags
                                }
                            }
                            
                            # Add to appropriate category
                            category = categorize_poi(node.tags)
                            if category in poi_features:
                                poi_features[category].append(feature)
                            else:
                                poi_features['other'].append(feature)
                                
                        except Exception as e:
                            print(f"Error processing node {node.id}: {str(e)}")
                            continue
                    
                    print(f"Processed features in chunk {(i*3)+j+1}")
                    break
                    
                except overpy.exception.OverpassGatewayTimeout:
                    if attempt < max_retries - 1:
                        print(f"Timeout error, retrying in {retry_delay} seconds... (Attempt {attempt + 1}/{max_retries})")
                        time.sleep(retry_delay)
                        retry_delay *= 2
                    else:
                        print(f"Failed to download chunk after {max_retries} attempts, skipping...")
                
                except Exception as e:
                    print(f"Error downloading chunk: {str(e)}")
                    break
                
            time.sleep(2)
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.join('public', 'data', 'osm'), exist_ok=True)
    
    # Create separate GeoJSON files for each category
    output_files = {}
    for category, features in poi_features.items():
        if features:  # Only create files for non-empty feature collections
            geojson = {
                'type': 'FeatureCollection',
                'features': features
            }
            
            filename = f'austin_{category}.geojson'
            filepath = os.path.join('public', 'data', 'osm', filename)
            with open(filepath, 'w') as f:
                json.dump(geojson, f)
            output_files[category] = os.path.abspath(filepath)
    
    print("\nDownload complete!")
    print("Files created:")
    for category, filepath in output_files.items():
        print(f"\n{category}:")
        print(f"- File: {filepath}")
        print(f"- Features: {len(poi_features[category])}")

def download_austin_networks(batch_size=3):
    api = overpy.Overpass(url='https://overpass.kumi.systems/api/interpreter')
    
    # Austin coordinates
    lat_min, lon_min = 30.0, -98.0  # SW corner
    lat_max, lon_max = 30.5, -97.5  # NE corner
    
    # Split area into batch_size x batch_size grid for better performance
    lat_step = (lat_max - lat_min) / batch_size
    lon_step = (lon_max - lon_min) / batch_size
    
    # Initialize feature collections for bike lanes and pedestrian networks
    bike_features = []
    pedestrian_features = []
    
    print("Downloading Austin bike and pedestrian network data from OpenStreetMap in batches...")
    
    for i in range(batch_size):
        for j in range(batch_size):
            chunk_lat_min = lat_min + (i * lat_step)
            chunk_lat_max = lat_min + ((i + 1) * lat_step)
            chunk_lon_min = lon_min + (j * lon_step)
            chunk_lon_max = lon_min + ((j + 1) * lon_step)
            
            bbox = f"{chunk_lat_min},{chunk_lon_min},{chunk_lat_max},{chunk_lon_max}"
            
            print(f"Processing batch {(i*batch_size)+j+1}/{batch_size*batch_size} with bbox: {bbox}")
            
            # Query for bike lanes
            bike_query = f"""
            [out:json][timeout:300];
            (
              way["highway"="cycleway"]({bbox});
              way["cycleway"]({bbox});
            );
            out body;
            >;
            out skel qt;
            """
            
            # Query for pedestrian paths
            pedestrian_query = f"""
            [out:json][timeout:300];
            (
              way["highway"="footway"]({bbox});
              way["highway"="path"]({bbox});
              way["highway"="pedestrian"]({bbox});
            );
            out body;
            >;
            out skel qt;
            """
            
            max_retries = 3
            retry_delay = 10  # seconds
            
            for attempt in range(max_retries):
                try:
                    print(f"Downloading bike network batch {(i*batch_size)+j+1}/{batch_size*batch_size}...")
                    bike_result = api.query(bike_query)
                    
                    for way in bike_result.ways:
                        try:
                            feature = {
                                'type': 'Feature',
                                'geometry': {
                                    'type': 'LineString',
                                    'coordinates': [[float(node.lon), float(node.lat)] for node in way.nodes]
                                },
                                'properties': {
                                    'name': way.tags.get('name', 'Unnamed'),
                                    'type': 'cycleway',
                                    'osm_id': way.id,
                                    'osm_type': 'way',
                                    'tags': way.tags
                                }
                            }
                            bike_features.append(feature)
                        except Exception as e:
                            print(f"Error processing bike way {way.id}: {str(e)}")
                            continue
                    
                    print(f"Processed bike features in batch {(i*batch_size)+j+1}")
                    break
                    
                except overpy.exception.OverpassGatewayTimeout:
                    if attempt < max_retries - 1:
                        print(f"Timeout error, retrying in {retry_delay} seconds... (Attempt {attempt + 1}/{max_retries})")
                        time.sleep(retry_delay)
                        retry_delay *= 2
                    else:
                        print(f"Failed to download bike batch after {max_retries} attempts, skipping...")
                
                except Exception as e:
                    print(f"Error downloading bike batch: {str(e)}")
                    break
            
            time.sleep(2)
            
            for attempt in range(max_retries):
                try:
                    print(f"Downloading pedestrian network batch {(i*batch_size)+j+1}/{batch_size*batch_size}...")
                    pedestrian_result = api.query(pedestrian_query)
                    
                    for way in pedestrian_result.ways:
                        try:
                            feature = {
                                'type': 'Feature',
                                'geometry': {
                                    'type': 'LineString',
                                    'coordinates': [[float(node.lon), float(node.lat)] for node in way.nodes]
                                },
                                'properties': {
                                    'name': way.tags.get('name', 'Unnamed'),
                                    'type': 'footway',
                                    'osm_id': way.id,
                                    'osm_type': 'way',
                                    'tags': way.tags
                                }
                            }
                            pedestrian_features.append(feature)
                        except Exception as e:
                            print(f"Error processing pedestrian way {way.id}: {str(e)}")
                            continue
                    
                    print(f"Processed pedestrian features in batch {(i*batch_size)+j+1}")
                    break
                    
                except overpy.exception.OverpassGatewayTimeout:
                    if attempt < max_retries - 1:
                        print(f"Timeout error, retrying in {retry_delay} seconds... (Attempt {attempt + 1}/{max_retries})")
                        time.sleep(retry_delay)
                        retry_delay *= 2
                    else:
                        print(f"Failed to download pedestrian batch after {max_retries} attempts, skipping...")
                
                except Exception as e:
                    print(f"Error downloading pedestrian batch: {str(e)}")
                    break
            
            time.sleep(2)
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.join('public', 'data', 'osm'), exist_ok=True)
    
    # Create GeoJSON files for bike and pedestrian networks
    if bike_features:
        bike_geojson = {
            'type': 'FeatureCollection',
            'features': bike_features
        }
        bike_filepath = os.path.join('public', 'data', 'osm', 'austin_bike_network.geojson')
        with open(bike_filepath, 'w') as f:
            json.dump(bike_geojson, f)
        print(f"\nBike network file created: {os.path.abspath(bike_filepath)}")
        print(f"- Features: {len(bike_features)}")
    
    if pedestrian_features:
        pedestrian_geojson = {
            'type': 'FeatureCollection',
            'features': pedestrian_features
        }
        pedestrian_filepath = os.path.join('public', 'data', 'osm', 'austin_pedestrian_network.geojson')
        with open(pedestrian_filepath, 'w') as f:
            json.dump(pedestrian_geojson, f)
        print(f"\nPedestrian network file created: {os.path.abspath(pedestrian_filepath)}")
        print(f"- Features: {len(pedestrian_features)}")

def categorize_poi(tags):
    """Categorize a POI based on its tags"""
    amenity = tags.get('amenity', '')
    shop = tags.get('shop', '')
    leisure = tags.get('leisure', '')
    building = tags.get('building', '')
    tourism = tags.get('tourism', '')
    
    # Logistics and Industrial
    if (amenity in ['warehouse', 'industrial'] or 
        building in ['warehouse', 'industrial'] or 
        tags.get('industrial') in ['logistics', 'warehouse']):
        return 'logistics'
    
    # Transportation
    if (amenity in ['bus_station', 'subway_entrance', 'taxi', 'ferry_terminal'] or
        tags.get('railway') in ['station', 'freight_yard', 'yard'] or
        tags.get('highway') == 'bus_stop'):
        return 'transportation'
    
    # Commercial
    if (shop or 
        amenity in ['marketplace', 'mall'] or 
        building in ['commercial', 'retail']):
        return 'commercial'
    
    # Services
    if amenity in ['bank', 'post_office', 'courthouse', 'police', 'fire_station', 'townhall']:
        return 'services'
    
    # Education
    if amenity in ['school', 'university', 'college', 'kindergarten', 'library']:
        return 'education'
    
    # Healthcare
    if amenity in ['hospital', 'clinic', 'pharmacy', 'doctors', 'dentist']:
        return 'healthcare'
    
    # Leisure
    if (leisure in ['park', 'garden', 'sports_centre', 'fitness_centre'] or
        amenity in ['cinema', 'theatre', 'museum', 'arts_centre', 'stadium']):
        return 'leisure'
    
    return 'other'

if __name__ == "__main__":
    download_osm_pois()
    download_austin_networks() 