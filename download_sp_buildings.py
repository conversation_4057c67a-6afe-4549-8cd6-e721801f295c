import json
import time
import os
from math import ceil
from datetime import datetime
import requests
from requests.exceptions import RequestException
import signal

class TimeoutError(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutError("Request timed out")

def download_sp_buildings():
    # Create output directory if it doesn't exist
    output_dir = os.path.join('public', 'data', 'osm', 'saoPao')
    os.makedirs(output_dir, exist_ok=True)
    
    # Try different Overpass API endpoints
    overpass_endpoints = [
        'https://overpass-api.de/api/interpreter',
        'https://overpass.kumi.systems/api/interpreter',
        'https://maps.mail.ru/osm/tools/overpass/api/interpreter'
    ]
    
    # São Paulo coordinates (approximate bounding box)
    lat_min, lon_min = -23.8, -46.8  # SW corner
    lat_max, lon_max = -23.4, -46.4  # NE corner
    
    # Split area into 8x8 grid for even smaller chunks
    batch_size = 8
    lat_step = (lat_max - lat_min) / batch_size
    lon_step = (lon_max - lon_min) / batch_size
    
    # Initialize feature collection for buildings
    building_features = []
    
    print("Downloading São Paulo building data from OpenStreetMap in batches...")
    start_time = time.time()
    
    # Calculate starting position for batch 20
    # In an 8x8 grid, batch 20 would be at position (2,4) (0-based)
    start_i = 2  # Third row
    start_j = 4  # Fifth column
    
    for i in range(start_i, batch_size):
        for j in range(start_j if i == start_i else 0, batch_size):
            batch_start_time = time.time()
            chunk_lat_min = lat_min + (i * lat_step)
            chunk_lat_max = lat_min + ((i + 1) * lat_step)
            chunk_lon_min = lon_min + (j * lon_step)
            chunk_lon_max = lon_min + ((j + 1) * lon_step)
            
            bbox = f"{chunk_lat_min},{chunk_lon_min},{chunk_lat_max},{chunk_lon_max}"
            batch_num = (i*batch_size)+j+1
            total_batches = batch_size*batch_size
            
            print(f"\nProcessing batch {batch_num}/{total_batches} with bbox: {bbox}")
            print(f"Time elapsed: {time.time() - start_time:.1f} seconds")
            
            # Simplified query for buildings
            building_query = f"""
            [out:json][timeout:25];
            (
              way["building"]({bbox});
            );
            out body;
            >;
            out skel qt;
            """
            
            max_retries = 3
            retry_delay = 5  # seconds
            batch_timeout = 180  # 3 minutes per batch
            
            for attempt in range(max_retries):
                success = False
                for endpoint in overpass_endpoints:
                    try:
                        print(f"Downloading building batch {batch_num}/{total_batches} (Attempt {attempt + 1}/{max_retries}) using {endpoint}...")
                        
                        # Set up timeout handler
                        signal.signal(signal.SIGALRM, timeout_handler)
                        signal.alarm(30)  # 30 second timeout
                        
                        try:
                            # Direct HTTP request with timeout
                            response = requests.post(
                                endpoint,
                                data=building_query,
                                timeout=25,
                                headers={'Content-Type': 'application/x-www-form-urlencoded'}
                            )
                            
                            # Cancel the alarm
                            signal.alarm(0)
                            
                            if response.status_code == 200:
                                data = response.json()
                                buildings_in_batch = 0
                                
                                # Process elements
                                elements = data.get('elements', [])
                                ways = [e for e in elements if e.get('type') == 'way']
                                nodes = {e['id']: e for e in elements if e.get('type') == 'node'}
                                
                                for way in ways:
                                    try:
                                        # Calculate height from various possible tags
                                        height = None
                                        if 'height' in way.get('tags', {}):
                                            height = parse_height(way['tags']['height'])
                                        elif 'building:levels' in way.get('tags', {}):
                                            # Assume 3 meters per floor if no height specified
                                            height = float(way['tags']['building:levels']) * 3
                                        
                                        # Get coordinates for the way
                                        coordinates = []
                                        for node_id in way.get('nodes', []):
                                            if node_id in nodes:
                                                node = nodes[node_id]
                                                coordinates.append([float(node['lon']), float(node['lat'])])
                                        
                                        if coordinates:
                                            feature = {
                                                'type': 'Feature',
                                                'geometry': {
                                                    'type': 'Polygon',
                                                    'coordinates': [coordinates]
                                                },
                                                'properties': {
                                                    'name': way.get('tags', {}).get('name', 'Unnamed'),
                                                    'building': way.get('tags', {}).get('building', 'yes'),
                                                    'height': height,
                                                    'levels': way.get('tags', {}).get('building:levels'),
                                                    'min_level': way.get('tags', {}).get('building:min_level'),
                                                    'max_level': way.get('tags', {}).get('building:max_level'),
                                                    'osm_id': way.get('id'),
                                                    'osm_type': 'way',
                                                    'tags': way.get('tags', {})
                                                }
                                            }
                                            building_features.append(feature)
                                            buildings_in_batch += 1
                                        
                                    except Exception as e:
                                        print(f"Error processing building {way.get('id')}: {str(e)}")
                                        continue
                                
                                print(f"Processed {buildings_in_batch} buildings in batch {batch_num}")
                                print(f"Batch time: {time.time() - batch_start_time:.1f} seconds")
                                success = True
                                break  # Break the endpoint loop if successful
                            else:
                                print(f"HTTP error {response.status_code} from {endpoint}")
                                
                        except TimeoutError:
                            print(f"Request timed out for endpoint {endpoint}")
                            signal.alarm(0)  # Cancel the alarm
                            continue
                        except requests.exceptions.Timeout:
                            print(f"Request timed out for endpoint {endpoint}")
                            continue
                        except requests.exceptions.RequestException as e:
                            print(f"Request error with endpoint {endpoint}: {str(e)}")
                            continue
                            
                    except Exception as e:
                        print(f"Unexpected error with endpoint {endpoint}: {str(e)}")
                        continue
                
                if success:
                    break  # Break the retry loop if successful
                
                if attempt < max_retries - 1:
                    print(f"All endpoints failed, retrying in {retry_delay} seconds... (Attempt {attempt + 1}/{max_retries})")
                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    print(f"Failed to download batch after {max_retries} attempts, skipping...")
                
                # Check if batch is taking too long
                if time.time() - batch_start_time > batch_timeout:
                    print(f"Batch {batch_num} taking too long (> {batch_timeout} seconds), skipping...")
                    break
            
            time.sleep(2)  # Be nice to the API
            
            # Save progress after each batch
            if building_features:
                temp_filepath = os.path.join(output_dir, 'sp_buildings_3d_temp.geojson')
                with open(temp_filepath, 'w') as f:
                    json.dump({
                        'type': 'FeatureCollection',
                        'features': building_features
                    }, f)
                print(f"Progress saved: {len(building_features)} buildings collected so far")
    
    # Create final GeoJSON file for buildings
    if building_features:
        building_geojson = {
            'type': 'FeatureCollection',
            'features': building_features
        }
        building_filepath = os.path.join(output_dir, 'sp_buildings_3d.geojson')
        with open(building_filepath, 'w') as f:
            json.dump(building_geojson, f)
        print(f"\nBuilding file created: {os.path.abspath(building_filepath)}")
        print(f"- Total buildings: {len(building_features)}")
        print(f"- Total time: {time.time() - start_time:.1f} seconds")
        
        # Print some statistics
        heights = [f['properties']['height'] for f in building_features if f['properties']['height']]
        if heights:
            print(f"- Average height: {sum(heights)/len(heights):.2f} meters")
            print(f"- Max height: {max(heights):.2f} meters")
            print(f"- Min height: {min(heights):.2f} meters")

def parse_height(height_str):
    """Parse height string into meters"""
    try:
        # Remove any non-numeric characters except decimal point
        height_str = ''.join(c for c in height_str if c.isdigit() or c == '.')
        return float(height_str)
    except:
        return None

if __name__ == "__main__":
    download_sp_buildings() 