import json
import sys

if len(sys.argv) < 2:
    print("Usage: python check_geojson_ids.py <path_to_geojson>")
    sys.exit(1)

geojson_path = sys.argv[1]

with open(geojson_path, 'r') as f:
    data = json.load(f)

features = data.get('features', [])
missing = []

print(f"Checking {len(features)} features in {geojson_path}")

for i, feat in enumerate(features):
    if 'id' not in feat:
        missing.append(i)

print("First 5 feature ids:")
for feat in features[:5]:
    print(feat.get('id', 'MISSING'), '|', feat.get('properties', {}).get('feature_type', ''))

if missing:
    print(f"WARNING: {len(missing)} features are missing top-level 'id'. Example indices: {missing[:10]}")
else:
    print("All features have a top-level 'id'.") 