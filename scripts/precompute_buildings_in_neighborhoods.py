import json
import os
from shapely.geometry import shape, Polygon, MultiPolygon, Point

# Paths
NEIGHBORHOODS_PATH = 'remote/public/data/osm/Neighborhoods.geojson'
BUILDINGS_PATH = 'remote/public/data/osm/austin_3d_buildings.geojson'
OUTPUT_PATH = 'remote/public/data/osm/neighborhoods_and_buildings.geojson'

print(f"Loading neighborhoods from {NEIGHBORHOODS_PATH}")
with open(NEIGHBORHOODS_PATH, 'r') as f:
    neighborhoods = json.load(f)
print(f"Loaded {len(neighborhoods['features'])} neighborhoods")

print(f"Loading buildings from {BUILDINGS_PATH}")
with open(BUILDINGS_PATH, 'r') as f:
    buildings = json.load(f)
print(f"Loaded {len(buildings['features'])} buildings")

# Prepare neighborhood polygons
neigh_polys = []
for feat in neighborhoods['features']:
    poly = shape(feat['geometry'])
    name = feat['properties'].get('name', 'unknown')
    neigh_polys.append((name, poly))
print(f"Prepared {len(neigh_polys)} neighborhood polygons")

# 1. Add all neighborhoods as features
output_features = []
for idx, feat in enumerate(neighborhoods['features']):
    feat_copy = dict(feat)
    feat_copy['properties'] = dict(feat['properties'])
    feat_copy['properties']['feature_type'] = 'neighborhood'
    feat_copy['id'] = f"neigh-{idx}"
    output_features.append(feat_copy)
print(f"Added {len(neighborhoods['features'])} neighborhood features to output, all assigned new ids")

# 2. Assign buildings to neighborhoods and add as features
assigned_buildings = 0
for idx, b in enumerate(buildings['features']):
    if idx % 1000 == 0 and idx > 0:
        print(f"Processed {idx} buildings...")
    b_geom = shape(b['geometry'])
    centroid = b_geom.centroid
    found = False
    for n_name, n_poly in neigh_polys:
        if n_poly.contains(centroid):
            b2 = dict(b)
            b2['properties'] = dict(b['properties'])
            b2['properties']['neighborhood'] = n_name
            b2['properties']['feature_type'] = 'building'
            b2['id'] = f"bldg-{idx}"
            output_features.append(b2)
            assigned_buildings += 1
            found = True
            break
    if not found:
        # Optionally, add buildings not in any neighborhood
        pass
print(f"Assigned {assigned_buildings} buildings to neighborhoods, all assigned new ids")

# Output
os.makedirs(os.path.dirname(OUTPUT_PATH), exist_ok=True)
print(f"Writing output to {OUTPUT_PATH}")
with open(OUTPUT_PATH, 'w') as f:
    json.dump({
        'type': 'FeatureCollection',
        'features': output_features
    }, f)
print(f"Wrote {len(output_features)} features (neighborhoods + buildings) to {OUTPUT_PATH}")

# Post-process: Check and print first 3 features for 'id'
print("Sample features from output:")
for i, feat in enumerate(output_features[:3]):
    print(json.dumps({k: v for k, v in feat.items() if k in ['id', 'properties', 'geometry']}, indent=2)[:500])
    if 'id' not in feat:
        print(f"WARNING: Feature at index {i} is missing top-level 'id'!")

# Assert all features have 'id'
missing_id_count = sum(1 for feat in output_features if 'id' not in feat)
if missing_id_count > 0:
    print(f"WARNING: {missing_id_count} features are missing top-level 'id'!")
else:
    print("All features have a top-level 'id'.") 