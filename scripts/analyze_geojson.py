import json
from collections import Counter

def analyze_geojson(file_path):
    print(f"Analyzing {file_path}...")
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    # Count feature types
    feature_types = Counter()
    neighborhood_names = Counter()
    building_properties = Counter()
    
    for feature in data['features']:
        feature_type = feature.get('properties', {}).get('feature_type')
        feature_types[feature_type] += 1
        
        if feature_type == 'neighborhood':
            name = feature.get('properties', {}).get('name')
            if name:
                neighborhood_names[name] += 1
        
        if feature_type == 'building':
            # Count which properties exist in building features
            for prop in feature.get('properties', {}).keys():
                building_properties[prop] += 1
    
    print("\nFeature Type Distribution:")
    for type_, count in feature_types.items():
        print(f"{type_}: {count}")
    
    print("\nNeighborhood Names (first 10):")
    for name, count in list(neighborhood_names.items())[:10]:
        print(f"{name}: {count}")
    
    print("\nBuilding Properties:")
    for prop, count in building_properties.items():
        print(f"{prop}: {count}")
    
    # Print a sample of each feature type
    print("\nSample Features:")
    for type_ in feature_types:
        sample = next(f for f in data['features'] if f.get('properties', {}).get('feature_type') == type_)
        print(f"\nSample {type_} feature:")
        print(json.dumps(sample, indent=2))

if __name__ == "__main__":
    analyze_geojson("remote/public/data/osm/neighborhoods_and_buildings.geojson") 