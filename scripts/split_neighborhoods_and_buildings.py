import json
import os
from collections import defaultdict

INPUT_PATH = 'remote/public/data/osm/neighborhoods_and_buildings.geojson'
NEIGHBORHOODS_PATH = 'remote/public/data/osm/neighborhoods.geojson'
BUILDINGS_DIR = 'remote/public/data/osm/buildings_by_neighborhood'

os.makedirs(BUILDINGS_DIR, exist_ok=True)

print(f"Loading {INPUT_PATH}")
with open(INPUT_PATH, 'r') as f:
    data = json.load(f)

neighborhood_features = []
buildings_by_neigh = defaultdict(list)

for feature in data['features']:
    props = feature.get('properties', {})
    if props.get('feature_type') == 'neighborhood':
        neighborhood_features.append(feature)
    elif props.get('feature_type') == 'building':
        neigh = props.get('neighborhood', 'unknown')
        buildings_by_neigh[neigh].append(feature)

# Write neighborhoods.geojson
print(f"Writing {NEIGHBORHOODS_PATH} with {len(neighborhood_features)} neighborhoods")
with open(NEIGHBORHOODS_PATH, 'w') as f:
    json.dump({
        'type': 'FeatureCollection',
        'features': neighborhood_features
    }, f)

# Write per-neighborhood buildings files
for neigh, features in buildings_by_neigh.items():
    # Sanitize filename
    safe_name = neigh.replace('/', '_').replace(' ', '_').replace('\\', '_')
    out_path = os.path.join(BUILDINGS_DIR, f'{safe_name}.geojson')
    print(f"Writing {out_path} with {len(features)} buildings")
    with open(out_path, 'w') as f:
        json.dump({
            'type': 'FeatureCollection',
            'features': features
        }, f)

print("Done splitting neighborhoods and buildings.") 