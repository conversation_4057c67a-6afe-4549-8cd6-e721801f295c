import json
import time
import requests
from typing import Dict, List, Any
import pandas as pd
from tqdm import tqdm
import logging
from ratelimit import limits, sleep_and_retry

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
GOOGLE_PLACES_API_KEY = "AIzaSyA4eaZjls2_Yh1TH3KK95pr8MoeEfSuyk4"
BATCH_SIZE = 10  # Process 10 locations at a time
RATE_LIMIT = 50  # Maximum requests per minute
RATE_LIMIT_PERIOD = 60  # Period in seconds

# Rate limiting decorator
@sleep_and_retry
@limits(calls=RATE_LIMIT, period=RATE_LIMIT_PERIOD)
def call_google_places_api(lat: float, lng: float) -> Dict[str, Any]:
    """Call Google Places API with rate limiting."""
    base_url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
    params = {
        "location": f"{lat},{lng}",
        "radius": "100",  # 100 meters radius
        "key": GOOGLE_PLACES_API_KEY
    }
    
    try:
        response = requests.get(base_url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"Error calling Google Places API: {e}")
        return {"error": str(e)}

def get_place_details(place_id: str) -> Dict[str, Any]:
    """Get detailed information about a place."""
    base_url = "https://maps.googleapis.com/maps/api/place/details/json"
    params = {
        "place_id": place_id,
        "fields": "name,formatted_address,rating,types,website,formatted_phone_number,opening_hours",
        "key": GOOGLE_PLACES_API_KEY
    }
    
    try:
        response = requests.get(base_url, params=params)
        response.raise_for_status()
        return response.json().get("result", {})
    except requests.exceptions.RequestException as e:
        logger.error(f"Error getting place details: {e}")
        return {}

def process_batch(features: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Process a batch of features and enrich them with Google Places data."""
    enriched_features = []
    
    for feature in features:
        try:
            # Get coordinates - handle different geometry types
            geometry = feature['geometry']
            if geometry['type'] == 'Point':
                coords = geometry['coordinates']
                lng, lat = coords[0], coords[1]
            elif geometry['type'] in ['Polygon', 'MultiPolygon']:
                # For polygons, use the first point of the first ring
                if geometry['type'] == 'Polygon':
                    coords = geometry['coordinates'][0][0]
                else:  # MultiPolygon
                    coords = geometry['coordinates'][0][0][0]
                lng, lat = coords[0], coords[1]
            else:
                logger.warning(f"Unsupported geometry type: {geometry['type']}")
                continue
            
            # Get nearby places
            places_data = call_google_places_api(lat, lng)
            
            # Initialize enriched properties
            enriched_properties = feature['properties'].copy()
            
            if 'results' in places_data and places_data['results']:
                # Get the closest place
                closest_place = places_data['results'][0]
                
                # Get detailed information
                place_details = get_place_details(closest_place['place_id'])
                
                # Add Google Places data to properties
                enriched_properties.update({
                    'google_places': {
                        'name': closest_place.get('name'),
                        'place_id': closest_place.get('place_id'),
                        'types': closest_place.get('types', []),
                        'rating': closest_place.get('rating'),
                        'user_ratings_total': closest_place.get('user_ratings_total'),
                        'vicinity': closest_place.get('vicinity'),
                        'details': place_details
                    },
                    'nearby_places': [
                        {
                            'name': place.get('name'),
                            'place_id': place.get('place_id'),
                            'types': place.get('types', []),
                            'rating': place.get('rating'),
                            'vicinity': place.get('vicinity')
                        }
                        for place in places_data['results'][1:4]  # Get up to 3 nearby places
                    ]
                })
            
            # Create new feature with enriched properties
            enriched_feature = feature.copy()
            enriched_feature['properties'] = enriched_properties
            enriched_features.append(enriched_feature)
            
            # Add a small delay between requests
            time.sleep(0.1)
            
        except Exception as e:
            logger.error(f"Error processing feature: {e}")
            # Add the original feature without enrichment
            enriched_features.append(feature)
            continue
    
    return enriched_features

def main():
    # Read the original GeoJSON file
    input_file = "/Users/<USER>/Documents/Kernel/ALLAPPS/SP/remote/public/data/osm/saoPao/logistics.geojson"
    output_file = "/Users/<USER>/Documents/Kernel/ALLAPPS/SP/remote/public/data/osm/saoPao/logistics_enriched.geojson"
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        logger.error(f"Input file not found: {input_file}")
        return
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in input file: {input_file}")
        return
    
    # Process features in batches
    features = data['features']
    enriched_features = []
    
    for i in tqdm(range(0, len(features), BATCH_SIZE), desc="Processing batches"):
        batch = features[i:i + BATCH_SIZE]
        enriched_batch = process_batch(batch)
        enriched_features.extend(enriched_batch)
        
        # Save progress after each batch
        data['features'] = enriched_features
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Processed {i + len(batch)} of {len(features)} features")
    
    logger.info(f"Processing complete. Enriched data saved to {output_file}")

if __name__ == "__main__":
    main() 