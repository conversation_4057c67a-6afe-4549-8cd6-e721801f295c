import overpy
import json
import time
import os

def download_3d_buildings_austin(batch_size=3):
    api = overpy.Overpass(url='https://overpass.kumi.systems/api/interpreter')
    # Austin bounding box
    lat_min, lon_min = 30.0, -98.0
    lat_max, lon_max = 30.5, -97.5
    lat_step = (lat_max - lat_min) / batch_size
    lon_step = (lon_max - lon_min) / batch_size
    features = []
    print("Downloading 3D buildings for Austin from OpenStreetMap in batches...")
    for i in range(batch_size):
        for j in range(batch_size):
            chunk_lat_min = lat_min + (i * lat_step)
            chunk_lat_max = lat_min + ((i + 1) * lat_step)
            chunk_lon_min = lon_min + (j * lon_step)
            chunk_lon_max = lon_min + ((j + 1) * lon_step)
            bbox = f"{chunk_lat_min},{chunk_lon_min},{chunk_lat_max},{chunk_lon_max}"
            print(f"Processing batch {i*batch_size+j+1}/{batch_size*batch_size} with bbox: {bbox}")
            query = f'''
            [out:json][timeout:300];
            (
              way["building"]["height"]({bbox});
              way["building"]["building:levels"]({bbox});
              relation["building"]["height"]({bbox});
              relation["building"]["building:levels"]({bbox});
            );
            out body;
            >;
            out skel qt;
            '''
            max_retries = 3
            retry_delay = 10
            for attempt in range(max_retries):
                try:
                    print(f"Downloading batch {i*batch_size+j+1}...")
                    result = api.query(query)
                    for way in result.ways:
                        try:
                            coords = [[float(node.lon), float(node.lat)] for node in way.nodes]
                            if len(coords) < 3:
                                continue
                            feature = {
                                'type': 'Feature',
                                'geometry': {
                                    'type': 'Polygon',
                                    'coordinates': [coords]
                                },
                                'properties': {
                                    'id': way.id,
                                    'tags': way.tags
                                }
                            }
                            features.append(feature)
                        except Exception as e:
                            print(f"Error processing way {way.id}: {e}")
                            continue
                    for rel in result.relations:
                        try:
                            # Only process multipolygon relations
                            if rel.tags.get('type') != 'multipolygon':
                                continue
                            polygons = []
                            for member in rel.members:
                                if member.role in ('outer', 'inner') and hasattr(member, 'resolve'):
                                    try:
                                        way = member.resolve()
                                        coords = [[float(node.lon), float(node.lat)] for node in way.nodes]
                                        if len(coords) >= 3:
                                            polygons.append(coords)
                                    except Exception as e:
                                        print(f"Error resolving member way in relation {rel.id}: {e}")
                                        continue
                            if polygons:
                                feature = {
                                    'type': 'Feature',
                                    'geometry': {
                                        'type': 'MultiPolygon',
                                        'coordinates': [polygons]
                                    },
                                    'properties': {
                                        'id': rel.id,
                                        'tags': rel.tags
                                    }
                                }
                                features.append(feature)
                        except Exception as e:
                            print(f"Error processing relation {rel.id}: {e}")
                            continue
                    print(f"Processed features in batch {i*batch_size+j+1}")
                    break
                except overpy.exception.OverpassGatewayTimeout:
                    if attempt < max_retries - 1:
                        print(f"Timeout error, retrying in {retry_delay} seconds... (Attempt {attempt + 1}/{max_retries})")
                        time.sleep(retry_delay)
                        retry_delay *= 2
                    else:
                        print(f"Failed to download batch after {max_retries} attempts, skipping...")
                except Exception as e:
                    print(f"Error downloading batch: {e}")
                    break
            time.sleep(2)
    os.makedirs(os.path.join('public', 'data', 'osm'), exist_ok=True)
    geojson = {
        'type': 'FeatureCollection',
        'features': features
    }
    filepath = os.path.join('public', 'data', 'osm', 'austin_3d_buildings.geojson')
    with open(filepath, 'w') as f:
        json.dump(geojson, f)
    print(f"\nDone! {len(features)} 3D buildings saved to {filepath}")

if __name__ == "__main__":
    download_3d_buildings_austin() 