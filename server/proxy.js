const cors = require('cors');
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();

// CORS configuration
app.use(cors({
  origin: 'http://localhost:3001', // Your frontend origin
  methods: ['GET', 'POST'],
  allowedHeaders: ['Content-Type', 'Accept', 'Origin']
}));

// Proxy configuration
app.use('/', createProxyMiddleware({
  changeOrigin: true,
  timeout: 20000,
  proxyTimeout: 20000,
  pathRewrite: {
    '^/': '/'
  },
  onError: (err, req, res) => {
    console.error('Proxy Error:', err);
    res.status(500).send('Proxy Error');
  }
}));

const PORT = 8080;
app.listen(PORT, () => {
  console.log(`Proxy server running on port ${PORT}`);
}); 