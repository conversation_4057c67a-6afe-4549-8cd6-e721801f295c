import json
import sys
from bs4 import BeautifulSoup
import os
from datetime import datetime
from typing import Dict, Any
from playwright.async_api import async_playwright
from dotenv import load_dotenv
import time
import asyncio
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Create data directory if not exists
os.makedirs("data", exist_ok=True)

class HARScraper:
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.logged_in = False

    async def initialize(self):
        """Initialize Playwright browser"""
        self.playwright = await async_playwright().start()
        # Launch browser in non-headless mode with larger viewport
        self.browser = await self.playwright.chromium.launch(
            headless=False,
            args=['--start-maximized']
        )
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        self.page = await self.context.new_page()
        
        # Create screenshots directory
        os.makedirs("screenshots", exist_ok=True)

    async def take_screenshot(self, name):
        """Take a screenshot for debugging"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"screenshots/{name}_{timestamp}.png"
        await self.page.screenshot(path=filename)
        print(f"Screenshot saved: {filename}")

    async def cleanup(self):
        """Clean up Playwright resources"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

    async def login(self):
        """Login to HAR.com using Playwright"""
        if self.logged_in:
            logger.info("Already logged in, skipping login process")
            return True

        try:
            # Get credentials from environment
            email = os.getenv('HAR_EMAIL', '<EMAIL>')
            password = os.getenv('HAR_PASSWORD', 'hzh6*BYAkXf&8YJ')

            logger.info("Starting login process...")
            logger.info("Navigating to login page...")
            await self.page.goto('https://www.har.com/login')
            await self.take_screenshot("login_page")
            
            logger.info("Waiting for login form...")
            await self.page.wait_for_selector('#username')
            
            logger.info("Filling in credentials...")
            await self.page.fill('#username', email)
            await self.page.fill('#password', password)
            await self.take_screenshot("credentials_filled")
            
            logger.info("Looking for submit button...")
            submit_button = await self.page.query_selector('button[type="submit"]')
            if not submit_button:
                logger.info("Looking for alternative login button...")
                submit_button = await self.page.query_selector('.btn-login')
            
            if submit_button:
                logger.info("Clicking login button...")
                try:
                    async with self.page.expect_navigation(timeout=60000):
                        await submit_button.click()
                    await self.take_screenshot("after_login")
                except Exception as e:
                    logger.error(f"Navigation error: {str(e)}")
                    await self.take_screenshot("login_error")
            else:
                logger.error("Could not find login button")
                await self.take_screenshot("no_login_button")
                return False
            
            # Check if login was successful
            try:
                # Wait for either success or failure indicators
                success = False
                
                try:
                    # Check for user menu or dashboard elements
                    await self.page.wait_for_selector('.user-menu, .dashboard-content', timeout=5000)
                    success = True
                except:
                    # Check if we're still on login page
                    try:
                        await self.page.wait_for_selector('.alert-danger', timeout=5000)
                        error_message = await self.page.text_content('.alert-danger')
                        print(f"Login failed: {error_message}")
                        await self.take_screenshot("login_failure")
                        return False
                    except:
                        # If no error message and no login form, we might be logged in
                        try:
                            login_form = await self.page.query_selector('#username')
                            if not login_form:
                                success = True
                        except:
                            success = True
                
                if success:
                    print("Successfully logged in!")
                    await self.take_screenshot("login_success")
                    self.logged_in = True
                    return True
                else:
                    print("Login status unclear")
                    await self.take_screenshot("login_unclear")
                    return False
                    
            except Exception as e:
                print(f"Error checking login status: {str(e)}")
                await self.take_screenshot("login_check_error")
                return False
                
        except Exception as e:
            print(f"Login error: {str(e)}")
            await self.take_screenshot("login_exception")
            return False

    async def scrape_property(self, address: str) -> Dict[str, Any]:
        """Scrape property information from HAR.com"""
        if not await self.login():
            return {"error": "Failed to login"}

        try:
            # First search for the property
            print(f"Searching for property: {address}")
            search_url = f"https://www.har.com/search?quicksearch={address}"
            await self.page.goto(search_url)
            await self.page.wait_for_load_state('networkidle')
            
            # Wait for search results
            try:
                await self.page.wait_for_selector('.property-item', timeout=10000)
            except:
                return {"error": "No search results found"}
            
            # Look for exact match
            property_links = await self.page.query_selector_all('.property-item a.listing-wrap')
            property_url = None
            
            for link in property_links:
                href = await link.get_attribute('href')
                if href:
                    # Get the address text from the link
                    address_elem = await link.query_selector('.address')
                    if address_elem:
                        listing_address = await address_elem.text_content()
                        # Compare addresses (ignoring case and extra spaces)
                        if listing_address.lower().replace(' ', '') == address.lower().replace(' ', ''):
                            property_url = href
                            break
            
            if not property_url:
                return {"error": "Property not found in search results"}
            
            # Navigate to the property page
            print(f"Navigating to property page: {property_url}")
            await self.page.goto(f"https://www.har.com{property_url}")
            await self.page.wait_for_load_state('networkidle')
            
            # Initialize data structure
            data = {
                "address": address,
                "url": f"https://www.har.com{property_url}",
                "timestamp": datetime.now().isoformat(),
                "details": {},
                "history": {
                    "financial": [],
                    "listing": [],
                    "tax": [],
                    "deed": []
                }
            }
            
            # Wait for content to load
            print("Waiting for property details...")
            try:
                await self.page.wait_for_selector('.property-details-container', timeout=10000)
            except:
                print("Property details section not found")
            
            # Get basic property details
            selectors = {
                'price': '.property-price',
                'beds': '.property-info .beds, .property-info-item:has-text("Bed")',
                'baths': '.property-info .baths, .property-info-item:has-text("Bath")',
                'sqft': '.property-info .sqft, .property-info-item:has-text("SqFt")',
                'lot_size': '.property-info .lot-size, .property-info-item:has-text("Lot")',
                'year_built': '.property-info .year-built, .property-info-item:has-text("Year")',
                'property_type': '.property-info .property-type, .property-info-item:has-text("Type")'
            }
            
            for key, selector in selectors.items():
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        text = await element.text_content()
                        data['details'][key] = text.strip()
                except Exception as e:
                    print(f"Error getting {key}: {str(e)}")
            
            # Get additional details from the details section
            try:
                details_section = await self.page.query_selector('.property-details-section')
                if details_section:
                    rows = await details_section.query_selector_all('.detail-row')
                    for row in rows:
                        label = await row.query_selector('.label')
                        value = await row.query_selector('.value')
                        if label and value:
                            label_text = await label.text_content()
                            value_text = await value.text_content()
                            data['details'][label_text.strip()] = value_text.strip()
            except Exception as e:
                print(f"Error getting additional details: {str(e)}")
            
            # Get property history
            print("Looking for property history...")
            history_tab = await self.page.query_selector('a:has-text("History"), button:has-text("History")')
            if history_tab:
                print("Navigating to history tab...")
                await history_tab.click()
                await self.page.wait_for_load_state('networkidle')
                
                # Wait for history content
                try:
                    await self.page.wait_for_selector('.history-section', timeout=10000)
                except:
                    print("History section not found")
                
                # Extract history data
                history_sections = {
                    'financial': '.price-history-section, .financial-history-section',
                    'listing': '.listing-history-section',
                    'tax': '.tax-history-section',
                    'deed': '.deed-history-section'
                }
                
                for history_type, selector in history_sections.items():
                    print(f"Extracting {history_type} history...")
                    try:
                        section = await self.page.query_selector(selector)
                        if section:
                            # Try to get table data first
                            table = await section.query_selector('table')
                            if table:
                                rows = await table.query_selector_all('tr')
                                headers = []
                                for row in rows:
                                    cells = await row.query_selector_all('td, th')
                                    row_data = [await cell.text_content() for cell in cells]
                                    if not headers and any('Date' in cell or 'Price' in cell for cell in row_data):
                                        headers = row_data
                                    else:
                                        if headers:
                                            clean_data = {k.strip(): v.strip() for k, v in zip(headers, row_data)}
                                            data["history"][history_type].append(clean_data)
                                        else:
                                            data["history"][history_type].append([v.strip() for v in row_data])
                            else:
                                # If no table, try to get text content
                                text = await section.text_content()
                                if text:
                                    data["history"][history_type].append({"text": text.strip()})
                    except Exception as e:
                        print(f"Error extracting {history_type} history: {str(e)}")
            else:
                print("No history tab found")
            
            return data

        except Exception as e:
            print(f"Error scraping property: {str(e)}")
            return {"error": str(e)}

    async def search_properties(self, query: str, max_results: int = 10) -> Dict[str, Any]:
        """Search for properties on HAR.com"""
        if not await self.login():
            return {"error": "Failed to login"}

        try:
            # Navigate to search page
            search_url = f"https://www.har.com/search/dosearch?for_sale=1&quicksearch={query}"
            await self.page.goto(search_url)
            await self.page.wait_for_load_state('networkidle')
            
            results = []
            property_items = await self.page.query_selector_all('.property-item')
            
            for item in property_items[:max_results]:
                property_data = {}
                
                # Get address
                address = await item.query_selector('.address')
                if address:
                    property_data['address'] = await address.text_content()
                
                # Get price
                price = await item.query_selector('.price')
                if price:
                    property_data['price'] = await price.text_content()
                
                # Get details
                details = await item.query_selector('.property-details')
                if details:
                    property_data['details'] = await details.text_content()
                
                results.append(property_data)
            
            return {
                "query": query,
                "results": results,
                "total_found": len(results)
            }

        except Exception as e:
            print(f"Search error: {str(e)}")
            return {"error": str(e)}

    async def navigate_to_matrix(self):
        """Navigate to Matrix MLS from dashboard"""
        try:
            logger.info("Attempting to navigate to Matrix MLS...")
            
            # Wait for dashboard to load
            logger.info("Waiting for dashboard elements...")
            await self.page.wait_for_selector('.dashboard-content', timeout=10000)
            
            # Look for Matrix MLS link
            logger.info("Looking for Matrix MLS link...")
            matrix_link = await self.page.query_selector('a:has-text("Matrix")')
            if matrix_link:
                logger.info("Found Matrix link, clicking...")
                await matrix_link.click()
                
                # Wait for new page/iframe
                logger.info("Waiting for Matrix page to load...")
                await self.page.wait_for_load_state('networkidle')
                return True
            else:
                logger.error("Could not find Matrix MLS link")
                return False
                
        except Exception as e:
            logger.error(f"Error navigating to Matrix: {str(e)}")
            return False

    async def search_property_history(self, address: str) -> Dict[str, Any]:
        """Search for property history in Matrix MLS"""
        try:
            logger.info(f"Searching for property history: {address}")
            
            # Navigate to Matrix first
            if not await self.navigate_to_matrix():
                return {"error": "Failed to access Matrix MLS"}
            
            # Look for search input
            logger.info("Looking for search input...")
            search_input = await self.page.query_selector('input[placeholder*="Search"]')
            if search_input:
                logger.info("Entering search query...")
                await search_input.fill(address)
                await search_input.press('Enter')
                
                # Wait for results
                logger.info("Waiting for search results...")
                await self.page.wait_for_selector('.search-results', timeout=10000)
                
                # Look for 360 History tab
                logger.info("Looking for 360 History tab...")
                history_tab = await self.page.query_selector('a:has-text("360 History")')
                if history_tab:
                    logger.info("Clicking 360 History tab...")
                    await history_tab.click()
                    
                    # Wait for history to load
                    logger.info("Waiting for history data...")
                    await self.page.wait_for_selector('.history-data', timeout=10000)
                    
                    # Extract history data
                    logger.info("Extracting history data...")
                    history_data = await self.extract_history_data()
                    return history_data
                else:
                    logger.error("360 History tab not found")
                    return {"error": "History tab not found"}
            else:
                logger.error("Search input not found")
                return {"error": "Search input not found"}
                
        except Exception as e:
            logger.error(f"Error searching property history: {str(e)}")
            return {"error": str(e)}

    async def extract_history_data(self) -> Dict[str, Any]:
        """Extract history data from the 360 History page"""
        try:
            logger.info("Starting history data extraction...")
            history_data = {
                "listing_history": [],
                "price_changes": [],
                "status_changes": []
            }
            
            # Extract data from tables
            tables = await self.page.query_selector_all('table')
            for table in tables:
                headers = []
                rows = await table.query_selector_all('tr')
                
                for i, row in enumerate(rows):
                    cells = await row.query_selector_all('td, th')
                    if i == 0:  # Header row
                        headers = [await cell.text_content() for cell in cells]
                    else:  # Data rows
                        row_data = {}
                        for j, cell in enumerate(cells):
                            if j < len(headers):
                                row_data[headers[j].strip()] = (await cell.text_content()).strip()
                        
                        # Determine which category this data belongs to
                        if any('price' in h.lower() for h in headers):
                            history_data["price_changes"].append(row_data)
                        elif any('status' in h.lower() for h in headers):
                            history_data["status_changes"].append(row_data)
                        else:
                            history_data["listing_history"].append(row_data)
            
            logger.info(f"Extracted {len(history_data['listing_history'])} listing records")
            logger.info(f"Extracted {len(history_data['price_changes'])} price changes")
            logger.info(f"Extracted {len(history_data['status_changes'])} status changes")
            
            return history_data
            
        except Exception as e:
            logger.error(f"Error extracting history data: {str(e)}")
            return {"error": str(e)}

async def handle_mcp_request(request: Dict[str, Any]) -> Dict[str, Any]:
    """Handle an MCP request"""
    scraper = HARScraper()
    
    try:
        await scraper.initialize()
        
        method = request.get('method')
        params = request.get('params', {})
        
        if method == 'scrape_property':
            return await scraper.scrape_property(params['address'])
        elif method == 'search_properties':
            max_results = params.get('max_results', 10)
            return await scraper.search_properties(params['query'], max_results)
        elif method == 'search_property_history':
            return await scraper.search_property_history(params['address'])
        else:
            return {"error": f"Unknown method: {method}"}
    finally:
        await scraper.cleanup()

async def main():
    """Main MCP server loop"""
    while True:
        try:
            # Read request from stdin
            line = sys.stdin.readline()
            if not line:
                break
                
            # Parse request
            request = json.loads(line)
            
            # Handle request
            response = await handle_mcp_request(request)
            
            # Send response
            print(json.dumps(response))
            sys.stdout.flush()
            
        except Exception as e:
            print(json.dumps({"error": str(e)}))
            sys.stdout.flush()

if __name__ == "__main__":
    asyncio.run(main()) 