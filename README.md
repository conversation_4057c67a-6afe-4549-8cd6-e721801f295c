# HAR Property Scraper

A Python script for scraping property information from the Houston Association of Realtors (HAR) website.

## Overview

This script searches for property listings on HAR.com and extracts relevant property details. It's designed to:
1. Search for a property using an address
2. Extract property details from the listing page
3. Save the results and debug information

## Features

- Address parsing and formatting
- Property search using HAR's web interface
- Detailed property information extraction including:
  - Price
  - Number of bedrooms
  - Number of bathrooms
  - Square footage
- Debug HTML saving for troubleshooting
- JSON output for easy data processing

## Requirements

```
requests
beautifulsoup4
python-dotenv
```

## Usage

1. Install the required packages:
```bash
pip install requests beautifulsoup4 python-dotenv
```

2. Run the script:
```bash
python har_scraper.py
```

The script will:
- Search for the test property (727 West Temple St Houston TX 77009)
- Save debug HTML files to `data/debug_html_*.html`
- Save property details to `data/har_property_test_*.json`

## How It Works

1. **Property Search**
   - Takes a full address as input
   - Formats the address for HAR's search URL
   - Sends a GET request to HAR's search page
   - Parses the HTML response to find property listings

2. **Property Details Extraction**
   - Visits the individual property page
   - Extracts details using BeautifulSoup
   - Saves information like price, beds, baths, and square footage

3. **Data Storage**
   - Saves debug HTML for troubleshooting
   - Stores property details in JSON format
   - Uses timestamps in filenames for tracking

## Output Format

The script generates JSON files with the following structure:
```json
{
  "address": "Property Address",
  "url": "Full HAR URL",
  "search_url": "Search Result URL",
  "timestamp": "YYYYMMDD_HHMMSS",
  "price": "Listed Price",
  "beds": "Number of Bedrooms",
  "baths": "Number of Bathrooms",
  "square_feet": "Property Square Footage"
}
```

## Debug Information

Debug HTML files are saved in the `data` directory with timestamps for troubleshooting search results and parsing issues.

## Notes

- The script uses HAR's public web interface
- Rate limiting and respectful scraping practices should be followed
- Some property details may not be available for all listings 