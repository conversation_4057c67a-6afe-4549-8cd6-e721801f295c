# Newport 🏙️

**Advanced Real Estate & Urban Planning Analysis Platform**

Newport is a comprehensive spatial analysis platform that combines AI-powered document processing, geospatial analysis, and interactive mapping to provide deep insights into real estate markets and urban planning.

## 🌟 Key Features

### 🤖 AI-Powered Analysis
- **Document Processing**: Advanced planning document analysis using OpenAI and Anthropic Claude
- **REIT Discovery**: Automated commercial real estate portfolio discovery
- **Market Intelligence**: AI-driven property and market analysis

### 🗺️ Interactive Mapping
- **Real-time Visualization**: Interactive maps with Mapbox GL JS
- **Multi-layer Analysis**: Buildings, zoning, demographics, and infrastructure
- **3D Building Models**: Advanced building footprint and height analysis

### 🏢 Property Intelligence
- **Web Scraping**: HAR (Houston Association of Realtors) property data extraction
- **REIT Properties**: Commercial real estate investment trust portfolio analysis
- **Market Analytics**: Property pricing, trends, and investment opportunities

### 📊 Geospatial Analytics
- **OpenStreetMap Integration**: Building footprints, POIs, and infrastructure data
- **Zoning Analysis**: Municipal zoning and land use optimization
- **Demographic Insights**: Population density and employment cluster analysis

## 🏗️ Architecture

### Frontend (`/remote/`)
- **React Application**: Modern web interface with interactive mapping
- **Mapbox Integration**: Advanced geospatial visualization
- **AI Chat Interface**: Natural language queries for property analysis

### Backend Services
- **Atlas Framework**: Core AI analysis engine (`/atlas/`)
- **FastAPI Services**: High-performance Python APIs
- **Express Proxy**: Node.js proxy for external API integrations

### Data Processing
- **Planning Processor**: Municipal document analysis pipeline
- **Property Scrapers**: Automated real estate data collection
- **Geospatial Tools**: Building and zoning data optimization

## 🚀 Quick Start

### Prerequisites
```bash
# Python 3.8+
pip install -r requirements.txt

# Node.js 16+
cd remote && npm install
```

### Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Add your API keys:
# - OPENAI_API_KEY
# - CLAUDE_API_KEY
# - TAVILY_API_KEY
# - MAPBOX_ACCESS_TOKEN
```

### Run the Application
```bash
# Start the backend services
python -m atlas.main

# Start the frontend (in another terminal)
cd remote && npm start

# Start the proxy server (in another terminal)
cd remote && node server.js
```

## 📁 Project Structure

```
newport/
├── atlas/                 # Core AI analysis framework
│   ├── clients/           # AI service integrations
│   ├── core/             # Configuration and utilities
│   ├── prism/            # Advanced building analysis
│   └── services/         # Business logic services
├── remote/               # Frontend React application
│   ├── src/components/   # React components
│   ├── planning_processor/ # Document analysis pipeline
│   └── scripts/          # Data processing scripts
├── data/                 # Raw and processed datasets
└── scripts/              # Utility and analysis scripts
```

## 🔧 Key Technologies

- **AI/ML**: OpenAI GPT-4, Anthropic Claude, Perplexity API
- **Geospatial**: Mapbox GL JS, GeoPandas, Shapely, Turf.js
- **Web**: React, FastAPI, Express.js, TailwindCSS
- **Data**: PostgreSQL, GeoJSON, OpenStreetMap

## 📈 Use Cases

1. **Real Estate Investment**: Identify high-potential properties and markets
2. **Urban Planning**: Analyze zoning regulations and development opportunities
3. **Market Research**: Track property trends and demographic shifts
4. **Due Diligence**: Comprehensive property and neighborhood analysis

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenStreetMap contributors for geographic data
- Mapbox for mapping infrastructure
- OpenAI and Anthropic for AI capabilities