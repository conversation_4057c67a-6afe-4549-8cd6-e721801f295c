import geopandas as gpd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import time
import pandas as pd

def analyze_cleaned_buildings():
    start_time = time.time()
    
    # Path to the cleaned buildings file
    input_filepath = Path('public/data/osm/saoPao/sp_buildings_3d_cleaned.geojson')
    
    print("Analyzing cleaned São Paulo building data...")
    print(f"Input file: {input_filepath.absolute()}")
    
    # Read the GeoJSON file
    print("\nReading data...")
    try:
        gdf = gpd.read_file(input_filepath)
        print(f"Successfully loaded {len(gdf):,} buildings")
        print(f"Columns available: {', '.join(gdf.columns)}")
    except Exception as e:
        print(f"Error reading file: {str(e)}")
        return
    
    # Basic statistics
    print(f"\nTotal buildings: {len(gdf):,}")
    
    # Height statistics
    print("\nCalculating height statistics...")
    height_count = gdf['height'].count()
    print(f"Buildings with height data: {height_count:,}")
    
    if height_count > 0:
        height_stats = gdf['height'].describe()
        print("\nHeight Statistics (meters):")
        print(f"Mean height: {height_stats['mean']:.1f}")
        print(f"Median height: {gdf['height'].median():.1f}")
        print(f"Min height: {height_stats['min']:.1f}")
        print(f"Max height: {height_stats['max']:.1f}")
    else:
        print("No height data available")
    
    # Level statistics
    print("\nCalculating level statistics...")
    levels_count = gdf['levels'].count()
    print(f"Buildings with level data: {levels_count:,}")
    
    if levels_count > 0:
        # Convert levels to numeric, handling any non-numeric values
        levels_series = pd.to_numeric(gdf['levels'], errors='coerce')
        level_stats = levels_series.describe()
        print("\nLevel Statistics:")
        print(f"Mean levels: {level_stats['mean']:.1f}")
        print(f"Median levels: {level_stats['50%']:.1f}")
        print(f"Min levels: {level_stats['min']:.1f}")
        print(f"Max levels: {level_stats['max']:.1f}")
    else:
        print("No level data available for statistical analysis")
    
    # Create visualizations
    print("\nCreating visualizations...")
    
    # 1. Height distribution histogram
    print("Creating height distribution histogram...")
    plt.figure(figsize=(12, 6))
    plt.hist(gdf['height'].dropna(), bins=50, edgecolor='black')
    plt.title('Distribution of Building Heights')
    plt.xlabel('Height (meters)')
    plt.ylabel('Number of Buildings')
    plt.savefig('building_heights_histogram.png')
    plt.close()
    print("Height histogram saved")
    
    # 2. Levels distribution histogram (only if we have data)
    if levels_count > 0:
        print("Creating levels distribution histogram...")
        plt.figure(figsize=(12, 6))
        plt.hist(levels_series.dropna(), bins=30, edgecolor='black')
        plt.title('Distribution of Building Levels')
        plt.xlabel('Number of Levels')
        plt.ylabel('Number of Buildings')
        plt.savefig('building_levels_histogram.png')
        plt.close()
        print("Levels histogram saved")
    
    # 3. Simple map of building footprints
    print("\nCreating building footprint map (this might take a while)...")
    plt.figure(figsize=(15, 15))
    gdf.plot(figsize=(15, 15), alpha=0.5, markersize=1)
    plt.title('São Paulo Building Footprints')
    plt.axis('off')
    plt.savefig('building_footprints.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("Building footprint map saved")
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\nAnalysis complete!")
    print(f"Total processing time: {duration:.1f} seconds")
    print("\nVisualizations saved as:")
    print("- building_heights_histogram.png")
    if levels_count > 0:
        print("- building_levels_histogram.png")
    print("- building_footprints.png")

if __name__ == "__main__":
    analyze_cleaned_buildings() 