import asyncio
import json
from har_scraper import <PERSON><PERSON><PERSON><PERSON>raper

async def main():
    scraper = HARScraper()
    try:
        # Initialize the scraper
        await scraper.initialize()
        
        # Test address
        address = "708 W Temple St Houston TX 77009"
        
        # Run the scraping process
        print("\nStarting property history search...")
        history_data = await scraper.search_property_history(address)
        
        # Save results to file
        output_file = "property_history.json"
        with open(output_file, "w") as f:
            json.dump(history_data, f, indent=2)
        print(f"\nResults saved to {output_file}")
        
    finally:
        await scraper.cleanup()

if __name__ == "__main__":
    asyncio.run(main()) 