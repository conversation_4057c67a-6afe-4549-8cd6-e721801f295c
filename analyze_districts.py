import json
import geopandas as gpd
from shapely.geometry import shape
import matplotlib.pyplot as plt

def analyze_districts():
    # Read the GeoJSON file
    file_path = '/Users/<USER>/Documents/Kernel/ALLAPPS/SP/remote/public/data/osm/saoPao/distrito_wgs84.geojson'
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    # Convert to GeoDataFrame for easier analysis
    gdf = gpd.GeoDataFrame.from_features(data['features'])
    
    # Print basic information
    print("\n=== District Analysis ===")
    print(f"Total number of districts: {len(gdf)}")
    
    # Print properties of each district
    print("\nDistrict Properties:")
    for idx, row in gdf.iterrows():
        print(f"\nDistrict: {row['ds_nome']}")
        print(f"Code: {row['ds_codigo']}")
        print(f"Subprefecture: {row['ds_subpref']}")
        print(f"Area (km²): {row['ds_areakm']:.2f}")
        
        # Get the bounds of the district
        bounds = row.geometry.bounds
        print(f"Bounds: {bounds}")
    
    # Create a simple visualization
    fig, ax = plt.subplots(figsize=(12, 8))
    gdf.plot(ax=ax, alpha=0.5, edgecolor='black')
    plt.title('São Paulo Districts')
    plt.savefig('districts_map.png')
    plt.close()
    
    # Print integration guidance
    print("\n=== Integration Guidance ===")
    print("To integrate this data with your map:")
    print("1. The data is already in WGS84 coordinates (EPSG:4326)")
    print("2. Each district has the following properties you can use for styling:")
    print("   - ds_nome: District name")
    print("   - ds_codigo: District code")
    print("   - ds_subpref: Subprefecture")
    print("   - ds_areakm: Area in square kilometers")
    print("3. You can use these properties to create choropleth maps or add interactive features")
    print("4. The geometry is stored in the 'geometry' column and can be used directly with mapping libraries")

if __name__ == "__main__":
    analyze_districts() 