import json
from collections import Counter
import pandas as pd

def analyze_geojson(file_path):
    # Read the GeoJSON file
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Get basic information
    print(f"\nTotal number of features: {len(data['features'])}")
    
    # Extract all properties
    properties_list = []
    for feature in data['features']:
        properties_list.append(feature['properties'])
    
    # Convert to DataFrame for easier analysis
    df = pd.DataFrame(properties_list)
    
    # Print column names
    print("\nAvailable properties/columns:")
    for col in df.columns:
        print(f"- {col}")
    
    # Print value counts for categorical columns
    print("\nValue counts for key categorical columns:")
    for col in df.columns:
        if df[col].nunique() < 10:  # Only show columns with few unique values
            print(f"\n{col}:")
            print(df[col].value_counts().head())
    
    # Print basic statistics for numeric columns
    print("\nBasic statistics for numeric columns:")
    numeric_cols = df.select_dtypes(include=['float64', 'int64']).columns
    if len(numeric_cols) > 0:
        print(df[numeric_cols].describe())
    
    # Print height distribution
    if 'height' in df.columns:
        # Convert height to numeric, coerce errors
        df['height'] = pd.to_numeric(df['height'], errors='coerce')
        print("\nHeight distribution (in meters):")
        print(df['height'].describe())
        print("\nHeight ranges:")
        height_ranges = pd.cut(df['height'], bins=[0, 5, 10, 20, 50, float('inf')])
        print(height_ranges.value_counts().sort_index())
    
    # Print sample of coordinates
    print("\nSample of coordinates (first 3 features):")
    for i, feature in enumerate(data['features'][:3]):
        coords = feature['geometry']['coordinates']
        print(f"Feature {i+1}: {coords}")
    
    # Print lat/long for the first three features
    print("\nLat/Long for the first three features:")
    for i, feature in enumerate(data['features'][:3]):
        coords = feature['geometry']['coordinates']
        # Assuming the first point of the polygon is representative
        lat, lon = coords[0][0][1], coords[0][0][0]
        print(f"Feature {i+1}: Latitude: {lat}, Longitude: {lon}")
    
    # Print all unique values for each property
    print("\nDetailed property analysis:")
    for col in df.columns:
        unique_values = df[col].unique()
        if len(unique_values) < 20:  # Only show if there are fewer than 20 unique values
            print(f"\n{col} unique values:")
            for val in unique_values:
                count = len(df[df[col] == val])
                print(f"- {val}: {count} occurrences")

if __name__ == "__main__":
    file_path = "/Users/<USER>/Documents/Kernel/ALLAPPS/SP/remote/public/data/osm/saoPao/logistics_buildings.geojson"
    analyze_geojson(file_path) 