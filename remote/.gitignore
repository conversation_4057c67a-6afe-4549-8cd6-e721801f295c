# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
ENV/

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
*.log

# Build directories
dist/
build/
*.egg-info/

# Data files
*.json
!package.json
!package-lock.json
!yarn.lock

# Large data directories
public/
data/
optimized_zoning/
test_docs/
test_output/
