import { useState, useEffect } from 'react';

export const useLayerStates = () => {
  // Initialize all layer states
  const [showRoadGrid, setShowRoadGrid] = useState(false);
  const [showMUDLayer, setShowMUDLayer] = useState(false);
  const [showHarveyData, setShowHarveyData] = useState(false);
  const [showSurfaceWater, setShowSurfaceWater] = useState(false);
  const [showWastewaterOutfalls, setShowWastewaterOutfalls] = useState(false);
  const [showZipCodes, setShowZipCodes] = useState(false);
  const [showZipFloodAnalysis, setShowZipFloodAnalysis] = useState(false);
  const [showAIConsensus, setShowAIConsensus] = useState(false);
  const [showRoadParticles, setShowRoadParticles] = useState(false);
  const [is3DActive, setIs3DActive] = useState(false);
  const [showZoningLayer, setShowZoningLayer] = useState(false);
  const [showPlanningDocsLayer, setShowPlanningDocsLayer] = useState(false);
  const [showConcentricCircles, setShowConcentricCircles] = useState(false);
  const [showPlanningAnalysis, setShowPlanningAnalysis] = useState(false);
  const [showAdaptiveReuse, setShowAdaptiveReuse] = useState(false);
  const [showDevelopmentPotential, setShowDevelopmentPotential] = useState(false);
  const [showTransportation, setShowTransportation] = useState(false);
  const [showRoads, setShowRoads] = useState(true);
  const [showPublicTransit, setShowPublicTransit] = useState(true);
  const [showBikeInfra, setShowBikeInfra] = useState(true);
  const [showPedestrian, setShowPedestrian] = useState(true);
  const [showPOIs, setShowPOIs] = useState(true);
  const [showTransitStops, setShowTransitStops] = useState(true);
  const [showTransitStations, setShowTransitStations] = useState(true);
  const [showTransitRoutes, setShowTransitRoutes] = useState(true);
  const [showBikeLanes, setShowBikeLanes] = useState(true);
  const [showBikePaths, setShowBikePaths] = useState(true);
  const [showBikeParking, setShowBikeParking] = useState(true);
  const [showPedestrianPaths, setShowPedestrianPaths] = useState(true);
  const [showPedestrianCrossings, setShowPedestrianCrossings] = useState(true);
  const [showNeighborhoodBoundaries, setShowNeighborhoodBoundaries] = useState(false);
  const [showNeighborhoodLabels, setShowNeighborhoodLabels] = useState(false);
  const [show3DBuildings, setShow3DBuildings] = useState(false);
  const [showPropertyPrices, setShowPropertyPrices] = useState(false);
  const [showEmployment, setShowEmployment] = useState(false);
  const [showEmploymentLabels, setShowEmploymentLabels] = useState(false);
  const [showParks, setShowParks] = useState(false);
  const [showLocalZones, setShowLocalZones] = useState(false);
  const [showLocalZoneBoundaries, setShowLocalZoneBoundaries] = useState(false);
  const [showLocalZoneLabels, setShowLocalZoneLabels] = useState(false);
  const [showAustinBike, setShowAustinBike] = useState(false);
  const [showAustinPedestrian, setShowAustinPedestrian] = useState(false);

  // Combine all states into a single object
  const [layerStates, setLayerStates] = useState({
    showRoadGrid,
    showMUDLayer,
    showHarveyData,
    showSurfaceWater,
    showWastewaterOutfalls,
    showZipCodes,
    showZipFloodAnalysis,
    showAIConsensus,
    showRoadParticles,
    is3DActive,
    showZoningLayer,
    showPlanningDocsLayer,
    showConcentricCircles,
    showPlanningAnalysis,
    showAdaptiveReuse,
    showDevelopmentPotential,
    showTransportation,
    showRoads,
    showPublicTransit,
    showBikeInfra,
    showPedestrian,
    showPOIs,
    showTransitStops,
    showTransitStations,
    showTransitRoutes,
    showBikeLanes,
    showBikePaths,
    showBikeParking,
    showPedestrianPaths,
    showPedestrianCrossings,
    showNeighborhoodBoundaries,
    showNeighborhoodLabels,
    show3DBuildings,
    showPropertyPrices,
    showEmployment,
    showEmploymentLabels,
    showParks,
    showLocalZones,
    showLocalZoneBoundaries,
    showLocalZoneLabels,
    showAustinBike,
    showAustinPedestrian
  });

  // Update layerStates whenever any individual state changes
  useEffect(() => {
    setLayerStates({
      showRoadGrid,
      showMUDLayer,
      showHarveyData,
      showSurfaceWater,
      showWastewaterOutfalls,
      showZipCodes,
      showZipFloodAnalysis,
      showAIConsensus,
      showRoadParticles,
      is3DActive,
      showZoningLayer,
      showPlanningDocsLayer,
      showConcentricCircles,
      showPlanningAnalysis,
      showAdaptiveReuse,
      showDevelopmentPotential,
      showTransportation,
      showRoads,
      showPublicTransit,
      showBikeInfra,
      showPedestrian,
      showPOIs,
      showTransitStops,
      showTransitStations,
      showTransitRoutes,
      showBikeLanes,
      showBikePaths,
      showBikeParking,
      showPedestrianPaths,
      showPedestrianCrossings,
      showNeighborhoodBoundaries,
      showNeighborhoodLabels,
      show3DBuildings,
      showPropertyPrices,
      showEmployment,
      showEmploymentLabels,
      showParks,
      showLocalZones,
      showLocalZoneBoundaries,
      showLocalZoneLabels,
      showAustinBike,
      showAustinPedestrian
    });
  }, [
    showRoadGrid,
    showMUDLayer,
    showHarveyData,
    showSurfaceWater,
    showWastewaterOutfalls,
    showZipCodes,
    showZipFloodAnalysis,
    showAIConsensus,
    showRoadParticles,
    is3DActive,
    showZoningLayer,
    showPlanningDocsLayer,
    showConcentricCircles,
    showPlanningAnalysis,
    showAdaptiveReuse,
    showDevelopmentPotential,
    showTransportation,
    showRoads,
    showPublicTransit,
    showBikeInfra,
    showPedestrian,
    showPOIs,
    showTransitStops,
    showTransitStations,
    showTransitRoutes,
    showBikeLanes,
    showBikePaths,
    showBikeParking,
    showPedestrianPaths,
    showPedestrianCrossings,
    showNeighborhoodBoundaries,
    showNeighborhoodLabels,
    show3DBuildings,
    showPropertyPrices,
    showEmployment,
    showEmploymentLabels,
    showParks,
    showLocalZones,
    showLocalZoneBoundaries,
    showLocalZoneLabels,
    showAustinBike,
    showAustinPedestrian
  ]);

  return {
    layerStates,
    setLayerStates,
    showRoadGrid, setShowRoadGrid,
    showMUDLayer, setShowMUDLayer,
    showHarveyData, setShowHarveyData,
    showSurfaceWater, setShowSurfaceWater,
    showWastewaterOutfalls, setShowWastewaterOutfalls,
    showZipCodes, setShowZipCodes,
    showZipFloodAnalysis, setShowZipFloodAnalysis,
    showAIConsensus, setShowAIConsensus,
    showRoadParticles, setShowRoadParticles,
    is3DActive, setIs3DActive,
    showZoningLayer, setShowZoningLayer,
    showPlanningDocsLayer, setShowPlanningDocsLayer,
    showConcentricCircles, setShowConcentricCircles,
    showPlanningAnalysis, setShowPlanningAnalysis,
    showAdaptiveReuse, setShowAdaptiveReuse,
    showDevelopmentPotential, setShowDevelopmentPotential,
    showTransportation, setShowTransportation,
    showRoads, setShowRoads,
    showPublicTransit, setShowPublicTransit,
    showBikeInfra, setShowBikeInfra,
    showPedestrian, setShowPedestrian,
    showPOIs, setShowPOIs,
    showTransitStops, setShowTransitStops,
    showTransitStations, setShowTransitStations,
    showTransitRoutes, setShowTransitRoutes,
    showBikeLanes, setShowBikeLanes,
    showBikePaths, setShowBikePaths,
    showBikeParking, setShowBikeParking,
    showPedestrianPaths, setShowPedestrianPaths,
    showPedestrianCrossings, setShowPedestrianCrossings,
    showNeighborhoodBoundaries, setShowNeighborhoodBoundaries,
    showNeighborhoodLabels, setShowNeighborhoodLabels,
    show3DBuildings, setShow3DBuildings,
    showPropertyPrices, setShowPropertyPrices,
    showEmployment, setShowEmployment,
    showEmploymentLabels, setShowEmploymentLabels,
    showParks, setShowParks,
    showLocalZones, setShowLocalZones,
    showLocalZoneBoundaries, setShowLocalZoneBoundaries,
    showLocalZoneLabels, setShowLocalZoneLabels,
    showAustinBike, setShowAustinBike,
    showAustinPedestrian, setShowAustinPedestrian
  };
}; 