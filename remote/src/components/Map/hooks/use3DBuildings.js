import { useCallback } from 'react';

export const use3DBuildings = (map, is3DActive, setIs3DActive, setIs3DLoading) => {
  const setup3DBuildings = useCallback(async () => {
    try {
      console.debug('\n=== Setting up São Paulo Logistics Buildings Layer ===');
      
      // Wait for style to be loaded
      if (!map.current.isStyleLoaded()) {
        console.debug('Waiting for map style to load...');
        await new Promise(resolve => {
          map.current.once('style.load', resolve);
        });
        console.debug('Map style loaded successfully');
      }

      // Add São Paulo logistics buildings source
      if (!map.current.getSource('sp-logistics-buildings')) {
        try {
          const startTime = performance.now();
          console.debug('Fetching São Paulo logistics buildings data...');
          
          const response = await fetch('/data/osm/saoPao/logistics_buildings.geojson');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          console.debug(`Fetched building data in ${performance.now() - startTime}ms`);
          const parseStart = performance.now();
          const data = await response.json();
          console.debug(`Parsed building data in ${performance.now() - parseStart}ms`);
          
          map.current.addSource('sp-logistics-buildings', {
            type: 'geojson',
            data: data
          });
          
          console.debug('São Paulo logistics buildings source added successfully');
        } catch (error) {
          console.error('Error loading São Paulo logistics buildings data:', error);
          throw error;
        }
      }

      // Add São Paulo logistics buildings layer
      if (!map.current.getLayer('sp-logistics-buildings-3d') && map.current.getSource('sp-logistics-buildings')) {
        console.debug('Adding São Paulo logistics buildings layer...');
        map.current.addLayer({
          'id': 'sp-logistics-buildings-3d',
          'source': 'sp-logistics-buildings',
          'type': 'fill-extrusion',
          'paint': {
            'fill-extrusion-color': [
              'interpolate',
              ['linear'],
              ['get', 'height'],
              0, '#4a4a4a',    // Dark gray for shortest buildings
              50, '#666666',   // Medium gray for medium buildings
              100, '#808080'   // Light gray for tallest buildings
            ],
            'fill-extrusion-height': ['get', 'height'],
            'fill-extrusion-base': 0,
            'fill-extrusion-opacity': 0.9,
            'fill-extrusion-vertical-gradient': true,
            'fill-extrusion-ambient-occlusion-intensity': 0.3,
            'fill-extrusion-ambient-occlusion-radius': 3
          },
          'layout': {
            'visibility': 'none'
          }
        });
        console.debug('São Paulo logistics buildings layer added successfully');
      }

      console.debug('=== São Paulo Logistics Buildings Setup Complete ===\n');
    } catch (error) {
      console.error('Error setting up São Paulo logistics buildings:', error);
      throw error;
    }
  }, [map]);

  const toggle3D = useCallback(async () => {
    if (!map.current) return;
    
    try {
      console.log('\n=== Toggling São Paulo Logistics Buildings ===');
      console.debug('3D Buildings toggle initiated');
      setIs3DLoading(true);
      
      // Record memory usage before 3D operation
      if (window.performance && window.performance.memory) {
        const memBefore = window.performance.memory;
        console.debug('Memory before 3D toggle:', {
          usedJSHeapSize: (memBefore.usedJSHeapSize / 1048576).toFixed(2) + ' MB',
          usagePercentage: ((memBefore.usedJSHeapSize / memBefore.jsHeapSizeLimit) * 100).toFixed(2) + '%'
        });
      }
      
      // Wait for style to be loaded if needed
      if (!map.current.isStyleLoaded()) {
        console.debug('Waiting for map style to load...');
        await new Promise(resolve => {
          map.current.once('style.load', resolve);
        });
      }

      const newState = !is3DActive;
      console.debug(`Setting 3D buildings visibility to: ${newState ? 'visible' : 'none'}`);
      
      // Initialize 3D buildings if turning on
      if (newState) {
        if (!map.current.getSource('sp-logistics-buildings')) {
          console.debug('Setting up São Paulo logistics buildings');
          await setup3DBuildings();
        }
      }
      
      // Update visibility for São Paulo logistics buildings layer
      if (map.current.getLayer('sp-logistics-buildings-3d')) {
        map.current.setLayoutProperty(
          'sp-logistics-buildings-3d',
          'visibility',
          newState ? 'visible' : 'none'
        );
      }
      
      // Measure memory after 3D toggle
      if (window.performance && window.performance.memory) {
        const memAfter = window.performance.memory;
        console.debug('Memory after 3D toggle:', {
          usedJSHeapSize: (memAfter.usedJSHeapSize / 1048576).toFixed(2) + ' MB',
          usagePercentage: ((memAfter.usedJSHeapSize / memAfter.jsHeapSizeLimit) * 100).toFixed(2) + '%',
          increase: ((memAfter.usedJSHeapSize - (window._memBefore || 0)) / 1048576).toFixed(2) + ' MB'
        });
      }
      
      setIs3DActive(newState);
      console.debug('São Paulo logistics buildings toggle complete');
    } catch (error) {
      console.error('Error toggling São Paulo logistics buildings:', error);
    } finally {
      setIs3DLoading(false);
    }
  }, [map, is3DActive, setIs3DActive, setIs3DLoading, setup3DBuildings]);

  return {
    setup3DBuildings,
    toggle3D
  };
}; 