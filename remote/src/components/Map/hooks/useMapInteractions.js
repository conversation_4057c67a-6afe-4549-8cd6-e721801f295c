import { useCallback, useMemo } from 'react';

export const useMapInteractions = (
  map,
  setRoadParticleThrottle,
  setLayerStates,
  {
    setShowTransportation,
    setShowRoads,
    setShowPublicTransit,
    setShowBikeInfra,
    setShowPedestrian,
    setShowRoadGrid,
    setShowMUDLayer,
    setShowHarveyData,
    setShowSurfaceWater,
    setShowWastewaterOutfalls,
    setShowZipCodes,
    setShowZipFloodAnalysis,
    setShowAIConsensus,
    setShowRoadParticles,
    setIs3DActive,
    setShowZoningLayer,
    setShowPlanningDocsLayer,
    setShowPlanningAnalysis,
    setShowAdaptiveReuse,
    setShowDevelopmentPotential,
    setShowNeighborhoodBoundaries,
    setShowNeighborhoodLabels,
    setShow3DBuildings,
    setShowPOIs,
    setShowPropertyPrices,
    setShowParks,
    setShowEmployment,
    setShowEmploymentLabels,
    setShowLocalZones,
    setShowLocalZoneBoundaries,
    setShowLocalZoneLabels,
    setShowAustinBike,
    setShowAustinPedestrian,
    toggle3D
  }
) => {
  // Function to handle all click events and throttle animations
  const setupMapInteractionHandlers = useCallback((map) => {
    if (!map) return;
    
    // Handle any click on the map - we throttle the animation temporarily
    map.on('click', (e) => {
      setRoadParticleThrottle(2, 1500);
    });
    
    // Also throttle during drag operations
    map.on('dragstart', () => {
      setRoadParticleThrottle(2, 500); // medium throttle, shorter duration
    });
    
    // Heavy throttle during zoom operations which are more intensive
    map.on('zoomstart', () => {
      setRoadParticleThrottle(3, 1000); // high throttle for 1 second
    });
    
    // Handle the end of these operations
    map.on('zoomend', () => {
      setTimeout(() => setRoadParticleThrottle(1), 300);
    });

    // Handle highlight interactions
    map.on('mousemove', 'highlight-areas-fill', (e) => {
      if (e.features && e.features.length > 0) {
        // Throttle animations during highlight hover
        setRoadParticleThrottle(2, 500);
      }
    });

    map.on('mouseleave', 'highlight-areas-fill', () => {
      // Reset throttle after highlight hover ends
      setTimeout(() => setRoadParticleThrottle(1), 300);
    });
    
    // Listen for custom events from AIChatPanel or SceneManager
    window.mapEventBus.on('scene:loading', () => {
      setRoadParticleThrottle(3, 2000); // heavy throttle during scene changes
    });
    
    window.mapEventBus.on('scene:loaded', () => {
      setTimeout(() => setRoadParticleThrottle(1), 500);
    });
    
    window.mapEventBus.on('ai:processing', () => {
      setRoadParticleThrottle(2, 3000); // medium throttle during AI operations
    });
    
    // Clean up when component unmounts
    return () => {
      map.off('click');
      map.off('dragstart');
      map.off('zoomstart');
      map.off('zoomend');
      map.off('mousemove', 'highlight-areas-fill');
      map.off('mouseleave', 'highlight-areas-fill');
    };
  }, [setRoadParticleThrottle]);

  // Add rotateMap function
  const rotateMap = useCallback((currentRotation, setCurrentRotation) => {
    if (!map.current) return;
    
    const newRotation = (currentRotation + 90) % 360;
    
    map.current.easeTo({
      bearing: newRotation,
      duration: 1000
    });
    
    setCurrentRotation(newRotation);
  }, [map]);

  // Add LayerManager utility
  const LayerManager = useCallback(() => {
    const loadedLayers = new Set();
    const layerLoadTimes = {};
    const pendingLayers = [];
    const layerTypes = {};
    let processingQueue = false;
    
    // Process the layer queue gradually to avoid overwhelming the GPU
    const processLayerQueue = () => {
      if (pendingLayers.length === 0 || processingQueue || !map.current) {
        return;
      }
      
      processingQueue = true;
      
      // Process just a few layers at a time
      const batchSize = 2;
      const layersToProcess = pendingLayers.splice(0, batchSize);
      
      layersToProcess.forEach(({ layerId, setupFunction, type }) => {
        const startTime = performance.now();
        
        try {
          setupFunction();
          loadedLayers.add(layerId);
          layerTypes[layerId] = type;
          const loadTime = performance.now() - startTime;
          layerLoadTimes[layerId] = loadTime;
        } catch (error) {
          // Error handling without console.log
        }
      });
      
      processingQueue = false;
      
      // Continue processing queue after a short delay
      if (pendingLayers.length > 0) {
        setTimeout(processLayerQueue, 100);
      }
    };
    
    return {
      queueLayer: (layerId, setupFunction, type = 'unknown') => {
        if (loadedLayers.has(layerId)) {
          return;
        }
        
        pendingLayers.push({ layerId, setupFunction, type });
        
        if (!processingQueue) {
          processLayerQueue();
        }
      },
      
      removeLayer: (layerId) => {
        if (!map.current || !loadedLayers.has(layerId)) {
          return;
        }
        
        try {
          if (map.current.getLayer(layerId)) {
            map.current.removeLayer(layerId);
          }
          
          // If this layer has a source with the same ID, remove it too
          if (map.current.getSource(layerId)) {
            map.current.removeSource(layerId);
          }
          
          loadedLayers.delete(layerId);
        } catch (error) {
          // Error handling without console.log
        }
      },
      
      getLayerStats: () => {
        return {
          totalLayers: loadedLayers.size,
          loadedLayers: Array.from(loadedLayers),
          pendingLayers: pendingLayers.map(l => l.layerId),
          layerLoadTimes,
          layerTypeBreakdown: Object.entries(
            Array.from(loadedLayers).reduce((acc, layerId) => {
              const type = layerTypes[layerId] || 'unknown';
              acc[type] = (acc[type] || 0) + 1;
              return acc;
            }, {})
          )
        };
      }
    };
  }, [map]);

  // Handler for loading scenes
  const handleLoadScene = useCallback((sceneLayerStates) => {
    if (!map.current) return;
    
    window.mapEventBus.emit('scene:loading');
    
    try {
      // First, hide all OSM layers
      const hideAllOSMLayers = () => {
        if (!map.current) return;
        const osmLayers = [
          'osm-transit-stops', 'osm-transit-routes',
          'osm-bike-lanes', 'osm-bike-paths', 'osm-bike-parking',
          'osm-pedestrian-paths', 'osm-pedestrian-crossings'
        ];
        osmLayers.forEach(layerId => {
          try {
            map.current.setLayoutProperty(layerId, 'visibility', 'none');
          } catch (error) {
            // Error handling without console.log
          }
        });
      };

      hideAllOSMLayers();

      // Update all layer states
      Object.entries(sceneLayerStates).forEach(([key, value]) => {
        switch (key) {
          case 'showTransportation':
            setShowTransportation(value);
            break;
          case 'showRoads':
            setShowRoads(value);
            break;
          case 'showPublicTransit':
            setShowPublicTransit(value);
            // Only show transit layers if both parent and child toggles are true
            if (value && sceneLayerStates.showOSMTransit) {
              if (sceneLayerStates.showTransitStops) {
                map.current.setLayoutProperty('osm-transit-stops', 'visibility', 'visible');
              }
              if (sceneLayerStates.showTransitRoutes) {
                map.current.setLayoutProperty('osm-transit-routes', 'visibility', 'visible');
              }
            }
            break;
          case 'showBikeInfra':
            setShowBikeInfra(value);
            // Only show bike layers if both parent and child toggles are true
            if (value && sceneLayerStates.showOSMBike) {
              if (sceneLayerStates.showBikeLanes) {
                map.current.setLayoutProperty('osm-bike-lanes', 'visibility', 'visible');
              }
              if (sceneLayerStates.showBikePaths) {
                map.current.setLayoutProperty('osm-bike-paths', 'visibility', 'visible');
              }
              if (sceneLayerStates.showBikeParking) {
                map.current.setLayoutProperty('osm-bike-parking', 'visibility', 'visible');
              }
            }
            break;
          case 'showPedestrian':
            setShowPedestrian(value);
            // Only show pedestrian layers if both parent and child toggles are true
            if (value && sceneLayerStates.showOSMPedestrian) {
              if (sceneLayerStates.showPedestrianPaths) {
                map.current.setLayoutProperty('osm-pedestrian-paths', 'visibility', 'visible');
              }
              if (sceneLayerStates.showPedestrianCrossings) {
                map.current.setLayoutProperty('osm-pedestrian-crossings', 'visibility', 'visible');
              }
            }
            break;
          case 'showOSMTransit':
          case 'showTransitStops':
          case 'showTransitRoutes':
          case 'showOSMBike':
          case 'showBikeLanes':
          case 'showBikePaths':
          case 'showBikeParking':
          case 'showOSMPedestrian':
          case 'showPedestrianPaths':
          case 'showPedestrianCrossings':
            // These are handled in their parent cases
            break;
          case 'showRoadGrid':
            setShowRoadGrid(value);
            break;
          case 'showMUDLayer':
            setShowMUDLayer(value);
            break;
          case 'showHarveyData':
            setShowHarveyData(value);
            break;
          case 'showSurfaceWater':
            setShowSurfaceWater(value);
            break;
          case 'showWastewaterOutfalls':
            setShowWastewaterOutfalls(value);
            break;
          case 'showZipCodes':
            setShowZipCodes(value);
            break;
          case 'showZipFloodAnalysis':
            setShowZipFloodAnalysis(value);
            break;
          case 'showAIConsensus':
            setShowAIConsensus(value);
            break;
          case 'showRoadParticles':
            setShowRoadParticles(value);
            break;
          case 'is3DActive':
            setIs3DActive(value);
            // Update 3D buildings visibility directly
            if (map.current.getLayer('osm-buildings-3d')) {
              map.current.setLayoutProperty(
                'osm-buildings-3d',
                'visibility',
                value ? 'visible' : 'none'
              );
            }
            if (map.current.getLayer('buildings-3d-layer')) {
              map.current.setLayoutProperty(
                'buildings-3d-layer',
                'visibility',
                value ? 'visible' : 'none'
              );
            }
            break;
          case 'showZoningLayer':
            setShowZoningLayer(value);
            break;
          case 'showPlanningDocsLayer':
            setShowPlanningDocsLayer(value);
            break;
          case 'showPlanningAnalysis':
            setShowPlanningAnalysis(value);
            break;
          case 'showAdaptiveReuse':
            setShowAdaptiveReuse(value);
            break;
          case 'showDevelopmentPotential':
            setShowDevelopmentPotential(value);
            break;
          case 'showNeighborhoodBoundaries':
            setShowNeighborhoodBoundaries(value);
            break;
          case 'showNeighborhoodLabels':
            setShowNeighborhoodLabels(value);
            break;
          case 'show3DBuildings':
            setShow3DBuildings(value);
            break;
          case 'showPOIs':
            setShowPOIs(value);
            break;
          case 'showPropertyPrices':
            setShowPropertyPrices(value);
            break;
          case 'showParks':
            setShowParks(value);
            // Handle park layers visibility
            if (value) {
              const parkLayers = [
                'park', 'park-label', 'national-park', 'golf-course', 'pitch', 'grass'
              ];
              
              parkLayers.forEach(layerId => {
                if (map.current && map.current.getLayer(layerId)) {
                  try {
                    map.current.setLayoutProperty(layerId, 'visibility', 'visible');
                    if (map.current.getPaintProperty(layerId, 'fill-color') !== undefined) {
                      map.current.setPaintProperty(layerId, 'fill-color', '#2a9d2a');
                      map.current.setPaintProperty(layerId, 'fill-opacity', 0.45);
                    }
                  } catch (error) {
                    // Error handling without console.log
                  }
                }
              });
              
              // Handle natural layer if it exists
              if (map.current && map.current.getLayer('natural')) {
                try {
                  if (!map.current._originalNaturalFilter) {
                    map.current._originalNaturalFilter = map.current.getFilter('natural') || ['all'];
                  }
                  
                  map.current.setFilter('natural', ['all', 
                    map.current._originalNaturalFilter,
                    ['any',
                      ['==', ['get', 'class'], 'park'],
                      ['==', ['get', 'class'], 'garden'],
                      ['==', ['get', 'class'], 'forest'],
                      ['==', ['get', 'class'], 'wood']
                    ]
                  ]);
                  
                  map.current.setLayoutProperty('natural', 'visibility', 'visible');
                  map.current.setPaintProperty('natural', 'fill-color', '#2a9d2a');
                  map.current.setPaintProperty('natural', 'fill-opacity', 0.45);
                } catch (error) {
                  // Error handling without console.log
                }
              }
            } else {
              const parkLayers = [
                'park', 'park-label', 'national-park', 'golf-course', 'pitch', 'grass'
              ];
              
              parkLayers.forEach(layerId => {
                if (map.current && map.current.getLayer(layerId)) {
                  try {
                    map.current.setLayoutProperty(layerId, 'visibility', 'none');
                  } catch (error) {
                    // Error handling without console.log
                  }
                }
              });
              
              // Reset natural layer
              if (map.current && map.current.getLayer('natural')) {
                try {
                  map.current.setLayoutProperty('natural', 'visibility', 'none');
                  if (map.current._originalNaturalFilter) {
                    map.current.setFilter('natural', map.current._originalNaturalFilter);
                  }
                } catch (error) {
                  // Error handling without console.log
                }
              }
            }
            break;
          case 'showEmployment':
            setShowEmployment(value);
            break;
          case 'showEmploymentLabels':
            setShowEmploymentLabels(value);
            break;
          case 'showLocalZones':
            setShowLocalZones(value);
            break;
          case 'showLocalZoneBoundaries':
            setShowLocalZoneBoundaries(value);
            break;
          case 'showLocalZoneLabels':
            setShowLocalZoneLabels(value);
            break;
          case 'showAustinBike':
            setShowAustinBike(value);
            break;
          case 'showAustinPedestrian':
            setShowAustinPedestrian(value);
            break;
          default:
            // Unknown layer state handling without console.log
            break;
        }
      });

      // Force update layerStates to ensure it reflects the new values
      setLayerStates(prevState => ({
        ...prevState,
        ...sceneLayerStates
      }));
      
      // Notify when scene is loaded
      setTimeout(() => {
        window.mapEventBus.emit('scene:loaded');
      }, 500);
    } catch (error) {
      setTimeout(() => {
        window.mapEventBus.emit('scene:loaded');
      }, 100);
    }
  }, [
    map,
    setLayerStates,
    setShowTransportation,
    setShowRoads,
    setShowPublicTransit,
    setShowBikeInfra,
    setShowPedestrian,
    setShowRoadGrid,
    setShowMUDLayer,
    setShowHarveyData,
    setShowSurfaceWater,
    setShowWastewaterOutfalls,
    setShowZipCodes,
    setShowZipFloodAnalysis,
    setShowAIConsensus,
    setShowRoadParticles,
    setIs3DActive,
    setShowZoningLayer,
    setShowPlanningDocsLayer,
    setShowPlanningAnalysis,
    setShowAdaptiveReuse,
    setShowDevelopmentPotential,
    setShowNeighborhoodBoundaries,
    setShowNeighborhoodLabels,
    setShow3DBuildings,
    setShowPOIs,
    setShowPropertyPrices,
    setShowParks,
    setShowEmployment,
    setShowEmploymentLabels,
    setShowLocalZones,
    setShowLocalZoneBoundaries,
    setShowLocalZoneLabels,
    setShowAustinBike,
    setShowAustinPedestrian
  ]);

  return {
    setupMapInteractionHandlers,
    handleLoadScene,
    rotateMap,
    LayerManager
  };
}; 