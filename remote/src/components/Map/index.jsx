import React, { useRef, useEffect, useState, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { MapContainer, ToggleButton } from './styles/MapStyles';
import { Toggle3DButton, RotateButton } from './StyledComponents';
import AIChatPanel from './AIChatPanel/index';
import { useAIConsensusAnimation } from './hooks/useAIConsensusAnimation';
import { useMapInitialization } from './hooks/useMapInitialization';
import { useLayerStates } from './hooks/useLayerStates';
import { useMapInteractions } from './hooks/useMapInteractions';
import { useAIChat } from './hooks/useAIChat';
import { PopupManager } from './components/PopupManager';
import { 
    initializeRoadGrid,
    loadHarveyData
} from './utils';
import LayerToggle from './components/LayerToggle';
import { mockDisagreementData } from './constants/mockData';
import { ErcotManager } from './components/ErcotManager';
import { 
    initializeRoadParticles,
    animateRoadParticles,
    stopRoadParticles,
    setRoadParticleThrottle
} from './hooks/mapAnimations';
import ZoningLayer from './components/ZoningLayer';
import PlanningDocsLayer from './components/PlanningDocsLayer';
import PlanningAnalysisLayer from './components/PlanningAnalysisLayer';
import SceneManager from './components/SceneManager';
import LocalZonesLayer from './components/LocalZonesLayer';
import PropertyPricesLayer from './components/PropertyPricesLayer';
import EmploymentLayer from './components/EmploymentLayer';
import { 
    SAO_PAULO_CENTER,
    CONCENTRIC_CIRCLES,
    CONCENTRIC_CIRCLES_IDS,
    CONCENTRIC_CIRCLES_SOURCES
} from './constants/mapConstants';
import { circle } from '@turf/turf';
import { use3DBuildings } from './hooks/use3DBuildings';

const MapComponent = () => {
  const mapContainer = useRef(null);
  const map = useRef(null);
  const [isErcotMode, setIsErcotMode] = useState(false);
  const [isLayerMenuCollapsed, setIsLayerMenuCollapsed] = useState(false);
  const [currentRotation, setCurrentRotation] = useState(0);
  const roadParticleAnimation = useRef(null);
  const [is3DLoading, setIs3DLoading] = useState(false);

  // Use the new useLayerStates hook
  const {
    layerStates,
    setLayerStates,
    showRoadGrid, setShowRoadGrid,
    showAIConsensus, setShowAIConsensus,
    showRoadParticles, setShowRoadParticles,
    is3DActive, setIs3DActive,
    showZoningLayer, setShowZoningLayer,
    showPlanningDocsLayer, setShowPlanningDocsLayer,
    showConcentricCircles, setShowConcentricCircles,
    showPlanningAnalysis, setShowPlanningAnalysis,
    showAdaptiveReuse, setShowAdaptiveReuse,
    showDevelopmentPotential, setShowDevelopmentPotential,
    showTransportation, setShowTransportation,
    showRoads, setShowRoads,
    showPublicTransit, setShowPublicTransit,
    showBikeInfra, setShowBikeInfra,
    showPedestrian, setShowPedestrian,
    showPOIs, setShowPOIs,
    showTransitStops, setShowTransitStops,
    showTransitStations, setShowTransitStations,
    showTransitRoutes, setShowTransitRoutes,
    showBikeLanes, setShowBikeLanes,
    showBikePaths, setShowBikePaths,
    showBikeParking, setShowBikeParking,
    showPedestrianPaths, setShowPedestrianPaths,
    showPedestrianCrossings, setShowPedestrianCrossings,
    showNeighborhoodBoundaries, setShowNeighborhoodBoundaries,
    showNeighborhoodLabels, setShowNeighborhoodLabels,
    show3DBuildings, setShow3DBuildings,
    showPropertyPrices, setShowPropertyPrices,
    showEmployment, setShowEmployment,
    showEmploymentLabels, setShowEmploymentLabels,
    showParks, setShowParks,
    showLocalZones, setShowLocalZones,
    showLocalZoneBoundaries, setShowLocalZoneBoundaries,
    showLocalZoneLabels, setShowLocalZoneLabels,
    showAustinBike, setShowAustinBike,
    showAustinPedestrian, setShowAustinPedestrian
  } = useLayerStates();

  // Use the new useMapInteractions hook
  const { 
    setupMapInteractionHandlers, 
    handleLoadScene,
    rotateMap,
    LayerManager
  } = useMapInteractions(
    map,
    setRoadParticleThrottle,
    setLayerStates,
    {
      setShowTransportation,
      setShowRoads,
      setShowPublicTransit,
      setShowBikeInfra,
      setShowPedestrian,
      setShowRoadGrid,
      setShowAIConsensus,
      setShowRoadParticles,
      setIs3DActive,
      setShowZoningLayer,
      setShowPlanningDocsLayer,
      setShowPlanningAnalysis,
      setShowAdaptiveReuse,
      setShowDevelopmentPotential,
      setShowNeighborhoodBoundaries,
      setShowNeighborhoodLabels,
      setShow3DBuildings,
      setShowPOIs,
      setShowPropertyPrices,
      setShowParks,
      setShowEmployment,
      setShowEmploymentLabels,
      setShowLocalZones,
      setShowLocalZoneBoundaries,
      setShowLocalZoneLabels,
      setShowAustinBike,
      setShowAustinPedestrian
    }
  );

  // Use the new useAIChat hook
  const {
    messages,
    setMessages,
    isLoading,
    loadingMessage,
    inputValue,
    setInputValue,
    handleQuestion
  } = useAIChat(map);

  // Set mapbox access token
  mapboxgl.accessToken = process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;

  // Define window level event bus for communication if it doesn't exist
  if (!window.mapEventBus) {
    window.mapEventBus = {
      listeners: {},
      emit: function(event, data) {
        if (this.listeners[event]) {
          this.listeners[event].forEach(callback => {
            try {
              callback(data);
            } catch (error) {}
          });
        }
      },
      on: function(event, callback) {
        if (!this.listeners[event]) {
          this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
        
        // Return an unsubscribe function
        return () => {
          if (this.listeners[event]) {
            this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
          }
        };
      }
    };
  }

  const { initializeParticleLayer, generateParticles } = useAIConsensusAnimation(map, showAIConsensus, mockDisagreementData);
  useMapInitialization(map, mapContainer);

  const ercotManagerRef = useRef(null);

  // Add effect to expose handleLoadScene on the map object and window.mapComponent
  useEffect(() => {
    if (map.current) {
      map.current.handleLoadScene = handleLoadScene;
      map.current.layerStates = layerStates;
      window.mapComponent = {
        handleLoadScene,
        layerStates
      };
    }
  }, [handleLoadScene, layerStates]);

  useEffect(() => {
    if (map.current) {
      if (showRoadGrid) {
        initializeRoadGrid(map.current, {
          minzoom: 5,
          maxzoom: 22
        });
      } else {
        if (map.current.getLayer('road-grid')) {
          map.current.removeLayer('road-grid');
        }
      }
    }
  }, [showRoadGrid]);

  // Add this effect for road particles
  useEffect(() => {
    if (!map.current) return;

    const initializeParticles = async () => {
      try {
        // Wait for style to fully load
        if (!map.current.isStyleLoaded()) {
          await new Promise(resolve => {
            map.current.once('style.load', resolve);
          });
        }

        if (showRoadParticles) {
          initializeRoadParticles(map.current);
          
          // Set up interaction handlers for the map
          const cleanupHandlers = setupMapInteractionHandlers(map.current);
          
          // Use the original requestAnimationFrame for the animation loop
          // to avoid potential issues with our wrapped version
          const originalRequestAnimationFrame = window._originalRAF || window.requestAnimationFrame;
          
          const animate = (timestamp) => {
            try {
              if (!map.current) return;
              
              animateRoadParticles({ map: map.current, timestamp });
              roadParticleAnimation.current = originalRequestAnimationFrame(animate);
            } catch (error) {
              if (roadParticleAnimation.current) {
                cancelAnimationFrame(roadParticleAnimation.current);
                roadParticleAnimation.current = null;
              }
            }
          };
          
          // Start the animation loop
          roadParticleAnimation.current = originalRequestAnimationFrame(animate);
          
          // Return cleanup function that also removes event handlers
          return () => {
            if (cleanupHandlers) cleanupHandlers();
          };
        } else {
          if (roadParticleAnimation.current) {
            stopRoadParticles(map.current);
            cancelAnimationFrame(roadParticleAnimation.current);
            roadParticleAnimation.current = null;
          }
        }
      } catch (error) {}
    };

    // Store original requestAnimationFrame if not already stored
    if (!window._originalRAF) {
      window._originalRAF = window.requestAnimationFrame;
    }

    // Initialize when map is ready
    if (map.current && map.current.loaded()) {
      initializeParticles();
    } else {
      map.current.once('load', initializeParticles);
    }

    // Cleanup function
    return () => {
      if (roadParticleAnimation.current) {
        cancelAnimationFrame(roadParticleAnimation.current);
        roadParticleAnimation.current = null;
      }
    };
  }, [showRoadParticles, setupMapInteractionHandlers]);

  // Add cleanup effect
  useEffect(() => {
    return () => {
      if (roadParticleAnimation.current) {
        cancelAnimationFrame(roadParticleAnimation.current);
        roadParticleAnimation.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (!map.current) return;

    // Update bounds whenever the map moves
    const updateBounds = () => {
      const bounds = map.current.getBounds();
    };

    map.current.on('moveend', updateBounds);
    // Get initial bounds
    updateBounds();

    return () => {
      if (map.current) {
        map.current.off('moveend', updateBounds);
      }
    };
  }, []);

  useEffect(() => {
    if (!map.current) return;

    // Add touch event handlers
    const handleTouchStart = (e) => {
      if (!e || !e.touches) return;
      
      if (e.touches.length === 2) {
        e.preventDefault(); // Prevent default zoom behavior
      }
    };

    const handleTouchMove = (e) => {
      if (!e || !e.touches) return;
      
      if (e.touches.length === 2) {
        e.preventDefault();
      }
    };

    // Add the event listeners to the canvas container
    const mapCanvas = map.current.getCanvas();
    if (mapCanvas) {
      mapCanvas.addEventListener('touchstart', handleTouchStart, { passive: false });
      mapCanvas.addEventListener('touchmove', handleTouchMove, { passive: false });

      return () => {
        mapCanvas.removeEventListener('touchstart', handleTouchStart);
        mapCanvas.removeEventListener('touchmove', handleTouchMove);
      };
    }
  }, []);

  // Use the new use3DBuildings hook
  const { setup3DBuildings, toggle3D } = use3DBuildings(
    map,
    is3DActive,
    setIs3DActive,
    setIs3DLoading
  );

  // Add cleanup effect for 3D buildings
  useEffect(() => {
    return () => {
      if (map.current) {
        // Use LayerManager to safely remove 3D layers
        if (LayerManager && typeof LayerManager.removeLayer === 'function') {
          LayerManager.removeLayer('osm-buildings-3d');
        } else {
          // Fallback to direct removal if LayerManager is not available
          if (map.current.getLayer('osm-buildings-3d')) {
            map.current.removeLayer('osm-buildings-3d');
          }
        }
        
        // Remove the source last
        if (map.current.getSource('osm-buildings')) {
          try {
            map.current.removeSource('osm-buildings');
          } catch (error) {}
        }
      }
    };
  }, [LayerManager]);

  // Update the rotation button click handler
  const handleRotation = () => {
    rotateMap(currentRotation, setCurrentRotation);
  };

  return (
    <MapContainer>
      <div ref={mapContainer} style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }} />
      <PopupManager map={map} />
      <ErcotManager ref={ercotManagerRef} map={map} isErcotMode={isErcotMode} setIsErcotMode={setIsErcotMode} />
      
      <SceneManager 
        map={map.current}
        layerStates={layerStates}
        onLoadScene={handleLoadScene}
        onSaveScene={() => {}}
        onClose={() => {}}
      />
      
      {showZoningLayer && (
        <ZoningLayer map={map} visible={showZoningLayer} />
      )}
      
      {showPlanningDocsLayer && (
        <PlanningDocsLayer map={map} visible={showPlanningDocsLayer} />
      )}
      
      {showPlanningAnalysis && (
        <PlanningAnalysisLayer
          map={map}
          showAdaptiveReuse={showAdaptiveReuse}
          showDevelopmentPotential={showDevelopmentPotential}
        />
      )}
      
      <LayerToggle
        map={map}
        isLayerMenuCollapsed={isLayerMenuCollapsed}
        setIsLayerMenuCollapsed={setIsLayerMenuCollapsed}
        isErcotMode={isErcotMode}
        setIsErcotMode={setIsErcotMode}
        showRoadGrid={showRoadGrid}
        setShowRoadGrid={setShowRoadGrid}
        showTransportation={showTransportation}
        setShowTransportation={setShowTransportation}
        showRoads={showRoads}
        setShowRoads={setShowRoads}
        showPublicTransit={showPublicTransit}
        setShowPedestrian={setShowPedestrian}
        showPOIs={showPOIs}
        setShowPOIs={setShowPOIs}
        showConcentricCircles={showConcentricCircles}
        setShowConcentricCircles={setShowConcentricCircles}
        showTransitStops={showTransitStops}
        setShowTransitStops={setShowTransitStops}
        showTransitStations={showTransitStations}
        setShowTransitStations={setShowTransitStations}
        showTransitRoutes={showTransitRoutes}
        setShowTransitRoutes={setShowTransitRoutes}
        setShowBikePaths={setShowBikePaths}
        showBikeParking={showBikeParking}
        setShowBikeParking={setShowBikeParking}
        showPedestrianPaths={showPedestrianPaths}
        setShowPedestrianPaths={setShowPedestrianPaths}
        showPedestrianCrossings={showPedestrianCrossings}
        setShowPedestrianCrossings={setShowPedestrianCrossings}
        showNeighborhoodBoundaries={showNeighborhoodBoundaries}
        setShowNeighborhoodBoundaries={setShowNeighborhoodBoundaries}
        show3DBuildings={show3DBuildings}
        setShow3DBuildings={setShow3DBuildings}
        is3DLoading={is3DLoading}
        setIs3DLoading={setIs3DLoading}
        fetchErcotData={() => ercotManagerRef.current?.fetchErcotData()}
        loadHarveyData={loadHarveyData}
        showPropertyPrices={showPropertyPrices}
        setShowPropertyPrices={setShowPropertyPrices}
        showEmployment={showEmployment}
        setShowEmployment={setShowEmployment}
        showParks={showParks}
        setShowParks={setShowParks}
        showNeighborhoodLabels={showNeighborhoodLabels}
        setShowNeighborhoodLabels={setShowNeighborhoodLabels}
        showEmploymentLabels={showEmploymentLabels}
        setShowEmploymentLabels={setShowEmploymentLabels}
        showLocalZones={showLocalZones}
        setShowLocalZones={setShowLocalZones}
        showLocalZoneBoundaries={showLocalZoneBoundaries}
        setShowLocalZoneBoundaries={setShowLocalZoneBoundaries}
        showLocalZoneLabels={showLocalZoneLabels}
        setShowLocalZoneLabels={setShowLocalZoneLabels}
        showAustinBike={showAustinBike}
        setShowAustinBike={setShowAustinBike}
        showAustinPedestrian={showAustinPedestrian}
        setShowAustinPedestrian={setShowAustinPedestrian}
        showRoadParticles={showRoadParticles}
        setShowRoadParticles={setShowRoadParticles}
      />

      <Toggle3DButton 
        $active={is3DActive}
        onClick={toggle3D}
        disabled={is3DLoading}
        aria-label="Toggle 3D view"
      >
        {is3DLoading ? '...' : (is3DActive ? '2D' : '3D')}
      </Toggle3DButton>
      
      <RotateButton 
        onClick={handleRotation}
        aria-label="Rotate map"
      >
        ↻
      </RotateButton>

      <AIChatPanel 
        messages={messages}
        setMessages={setMessages}
        isLoading={isLoading}
        loadingMessage={loadingMessage}
        inputValue={inputValue}
        setInputValue={setInputValue}
        map={map.current}
        handleQuestion={handleQuestion}
        initialCollapsed={true}
      />

      {showPropertyPrices && (
        <PropertyPricesLayer
          map={map}
          showPropertyPrices={showPropertyPrices}
        />
      )}
      
      {showEmployment && (
        <EmploymentLayer
          map={map}
          showEmployment={showEmployment}
          showLabels={showEmploymentLabels}
        />
      )}
      
      {showLocalZones && (
        <LocalZonesLayer
          map={map}
          showLocalZones={showLocalZones}
          showLocalZoneBoundaries={showLocalZoneBoundaries}
          showLocalZoneLabels={showLocalZoneLabels}
        />
      )}
    </MapContainer>
  );
};

export default MapComponent;

