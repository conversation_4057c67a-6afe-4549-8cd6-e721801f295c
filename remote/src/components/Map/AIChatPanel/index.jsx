import React, { useRef, useState } from 'react';
import { <PERSON>, ChatHeader, DiffusionTag } from './StyledComponents';
import { Sparkles } from 'lucide-react';
import { MODEL_COLORS } from './mockData';

// Import components
import ModelSelector from './components/ModelSelector';
import MessageList from './components/MessageList';
import InputArea from './components/InputArea';
import InitialQuestions from './components/InitialQuestions';
import CollapseButton from './components/CollapseButton';
import { useAIChatPanel } from './hooks/useAIChatPanel';

// Import contexts
import { ModelProvider } from './contexts/ModelContext';
import { DiffusionProvider } from './contexts/DiffusionContext';
import { MapProvider } from './contexts/MapContext';

const AIChatPanelContent = ({ messages, setMessages, onQuestion, map, initialCollapsed = true }) => {
  const messagesEndRef = useRef(null);
  const panelInitializedRef = useRef(false);
  const questionButtonsRef = useRef([]);

  const {
    // State
    messages: effectiveMessages,
    inputValue,
    isLoading,
    isCollapsed,
    selectedModel,
    isDiffusionActive,
    isBlinking,
    
    // Setters
    setInputValue,
    setIsLoading,
    
    // Handlers
    handleModelChange,
    handleDiffusionChange,
    handleCollapseChange,
    handleQuestion,
    handleSubmit,
    handleDiffusionClick
  } = useAIChatPanel({
    initialMessages: messages,
    initialCollapsed,
    map,
    setMessages
  });

  return (
    <>
      <Panel 
        $isCollapsed={isCollapsed}
        style={{ willChange: 'transform' }}
      >
        <ChatHeader>
          <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', width: '100%' }}>
            <ModelSelector 
              selectedModel={selectedModel}
              handleModelChange={handleModelChange}
            />
            <DiffusionTag 
              className={`${isDiffusionActive ? 'active' : ''} ${isBlinking ? 'blinking' : ''}`}
              onClick={handleDiffusionClick}
            >
              Diffusion
            </DiffusionTag>
          </div>
        </ChatHeader>

        {effectiveMessages.length === 0 ? (
          <InitialQuestions
            handleQuestion={handleQuestion}
            selectedModel={selectedModel}
            isDiffusionActive={isDiffusionActive}
            key={`questions-${isDiffusionActive ? 'diffusion' : 'normal'}`}
          />
        ) : (
          <MessageList
            messages={effectiveMessages}
            isLoading={isLoading}
            handleQuestion={handleQuestion}
            messagesEndRef={messagesEndRef}
            selectedModel={selectedModel}
            isDiffusionActive={isDiffusionActive}
          />
        )}

        <InputArea
          inputValue={inputValue}
          setInputValue={setInputValue}
          handleSubmit={handleSubmit}
        />
      </Panel>

      <CollapseButton 
        isCollapsed={isCollapsed}
        handleCollapseToggle={handleCollapseChange}
      />
    </>
  );
};

const AIChatPanel = (props) => {
  return (
    <ModelProvider>
      <DiffusionProvider>
        <MapProvider initialMap={props.map} initialCollapsed={props.initialCollapsed}>
          <AIChatPanelContent {...props} />
        </MapProvider>
      </DiffusionProvider>
    </ModelProvider>
  );
};

export default AIChatPanel;
