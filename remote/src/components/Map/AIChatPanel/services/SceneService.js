export class SceneService {
  static loadScene(sceneName) {
    if (window.mapComponent?.loadSceneByName) {
      return window.mapComponent.loadSceneByName(sceneName);
    }
    return false;
  }

  static loadScene01() {
    return this.loadScene("Scene01");
  }

  static loadZoningScene() {
    return this.loadScene("Zoning");
  }

  static loadNextScene() {
    return this.loadScene("Next");
  }

  static getEffectiveMap(map) {
    const directMapRef = map;
    const windowMapRef = window.mapComponent?.map;
    return directMapRef || windowMapRef;
  }
} 