export class MessageService {
  constructor(setMessages) {
    this.setMessages = setMessages;
  }

  addUserMessage(content) {
    this.setMessages(prev => {
      const prevMessages = Array.isArray(prev) ? prev : [];
      return [...prevMessages, { isUser: true, content }];
    });
  }

  addSystemMessage(content) {
    this.setMessages(prev => {
      const prevMessages = Array.isArray(prev) ? prev : [];
      return [...prevMessages, { isUser: false, content }];
    });
  }

  addErrorResponse() {
    this.addSystemMessage({
      preGraphText: "Sorry, I encountered an error processing your request.",
      postGraphText: "Please try again or select another question."
    });
  }

  addSolarPotentialResponse() {
    this.addUserMessage("View Solar Potential Analysis");
    this.addSystemMessage({
      preGraphText: "Loading detailed solar potential analysis for Los Angeles neighborhoods...",
      postGraphText: "This visualization shows the rooftop solar capacity and potential energy generation across different areas. The highlighted regions indicate optimal locations for new solar installations."
    });
  }

  addMockResponse(questionText, mockResponse) {
    this.addUserMessage(questionText);
    this.addSystemMessage(JSON.parse(mockResponse.content[0].text));
  }
} 