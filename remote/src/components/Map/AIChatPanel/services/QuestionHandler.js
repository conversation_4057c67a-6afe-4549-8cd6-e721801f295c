import { 
  handlePanelQuestion, 
  handleUrbanImpactQuestion, 
  handleServiceCorridorsQuestion,
  handleInfrastructureImprovementsQuestion,
  MOCK_RESPONSES 
} from '../../../../services/claude';
import { MessageService } from './MessageService';
import { SceneService } from './SceneService';
import { NotificationManager } from './NotificationManager';

export const useQuestionHandler = (setMessages, setIsLoading) => {
  const messageService = new MessageService(setMessages);

  const handleUrbanImpact = async (questionText) => {
    SceneService.loadScene01();
    messageService.addUserMessage(questionText);
    await handleUrbanImpactQuestion(questionText, [], setMessages, setIsLoading);
  };

  const handleServiceCorridors = async () => {
    SceneService.loadZoningScene();
    await handleServiceCorridorsQuestion(SceneService.getEffectiveMap(), setMessages, setIsLoading);
  };

  const handleSolarPotential = async () => {
    SceneService.loadNextScene();
    messageService.addSolarPotentialResponse();
    NotificationManager.showNotification("Loading solar potential analysis...");
  };

  const handleInfrastructureImprovements = async () => {
    SceneService.loadNextScene();
    await handleInfrastructureImprovementsQuestion(SceneService.getEffectiveMap(), setMessages, setIsLoading);
  };

  const handleMockResponse = async (questionText) => {
    if (MOCK_RESPONSES?.[questionText]) {
      messageService.addMockResponse(questionText, MOCK_RESPONSES[questionText]);
      return true;
    }
    return false;
  };

  const handleQuestion = async (questionText) => {
    try {
      // Handle special cases
      switch (questionText) {
        case "Finding Downtown parcels with the highest emergency resilience potential...":
          await handleUrbanImpact(questionText);
          return;
          
        case "Show potential service corridors around Skid Row":
          await handleServiceCorridors();
          return;
          
        case "SHOW_SOLAR_POTENTIAL":
          await handleSolarPotential();
          return;
          
        case "What infrastructure improvements would have most impact in Skid Row?":
          await handleInfrastructureImprovements();
          return;
          
        case "Where are the major flood-prone areas?":
          if (await handleMockResponse(questionText)) {
            return;
          }
          break;
      }
      
      // Handle general questions
      await handlePanelQuestion(questionText, SceneService.getEffectiveMap(), setMessages, setIsLoading);
    } catch (error) {
      console.error('Error in handleQuestion:', error);
      messageService.addErrorResponse();
    }
  };

  return {
    handleQuestion
  };
}; 