import { useState, useCallback, useEffect } from 'react';
import { useQuestionHandler } from '../services/QuestionHandler';
import { useModel } from '../contexts/ModelContext';
import { useDiffusion } from '../contexts/DiffusionContext';
import { useMap } from '../contexts/MapContext';

export const useAIChatPanel = ({ 
  initialMessages = [], 
  initialCollapsed = true,
  map,
  setMessages: externalSetMessages 
}) => {
  // State management
  const [localMessages, setLocalMessages] = useState(initialMessages);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Use provided messages/setMessages if available, otherwise use local state
  const effectiveMessages = externalSetMessages ? initialMessages : localMessages;
  const effectiveSetMessages = externalSetMessages || setLocalMessages;

  // Use contexts
  const { selectedModel, handleModelChange } = useModel();
  const { 
    isDiffusionActive, 
    isBlinking, 
    handleDiffusionChange, 
    handleDiffusionClick 
  } = useDiffusion();
  const { isCollapsed, handleCollapseChange } = useMap();

  // Initialize question handler
  const { handleQuestion } = useQuestionHandler(effectiveSetMessages, setIsLoading);

  // Expose the loading state setter function globally
  useEffect(() => {
    window.setAIChatPanelLoading = setIsLoading;
    window.setAIChatPanelMessages = externalSetMessages;
    window.setAIChatPanelCollapsed = handleCollapseChange;
    
    return () => {
      window.setAIChatPanelLoading = null;
      window.setAIChatPanelMessages = null;
      window.setAIChatPanelCollapsed = null;
    };
  }, [externalSetMessages, handleCollapseChange]);

  // Handle form submission
  const handleSubmit = useCallback(async (e) => {
    if (e) e.preventDefault();
    if (!inputValue.trim()) return;
    
    const question = inputValue.trim();
    setInputValue('');
    
    if (isCollapsed) {
      handleCollapseChange(false);
    }
    
    await handleQuestion(question);
  }, [inputValue, isCollapsed, handleCollapseChange, handleQuestion]);

  return {
    // State
    messages: effectiveMessages,
    inputValue,
    isLoading,
    isCollapsed,
    selectedModel,
    isDiffusionActive,
    isBlinking,
    
    // Setters
    setInputValue,
    setIsLoading,
    
    // Handlers
    handleModelChange,
    handleDiffusionChange,
    handleCollapseChange,
    handleQuestion,
    handleSubmit,
    handleDiffusionClick
  };
}; 