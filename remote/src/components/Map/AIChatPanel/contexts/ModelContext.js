import React, { createContext, useContext, useState } from 'react';

const ModelContext = createContext();

export const ModelProvider = ({ children }) => {
  const [selectedModel, setSelectedModel] = useState('claude3');

  const handleModelChange = (newModel) => {
    setSelectedModel(newModel);
  };

  return (
    <ModelContext.Provider value={{ selectedModel, handleModelChange }}>
      {children}
    </ModelContext.Provider>
  );
};

export const useModel = () => {
  const context = useContext(ModelContext);
  if (!context) {
    throw new Error('useModel must be used within a ModelProvider');
  }
  return context;
}; 