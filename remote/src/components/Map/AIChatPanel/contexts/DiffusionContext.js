import React, { createContext, useContext, useState, useCallback } from 'react';

const DiffusionContext = createContext();

export const DiffusionProvider = ({ children }) => {
  const [isDiffusionActive, setIsDiffusionActive] = useState(false);
  const [isBlinking, setIsBlinking] = useState(false);

  const handleDiffusionChange = useCallback((newState) => {
    setIsDiffusionActive(newState);
  }, []);

  const handleDiffusionClick = useCallback(() => {
    if (!isDiffusionActive) {
      setIsBlinking(true);
      
      setTimeout(() => {
        setIsDiffusionActive(true);
        
        setTimeout(() => {
          setIsBlinking(false);
        }, 50);
      }, 300);
    } else {
      setIsDiffusionActive(false);
    }
  }, [isDiffusionActive]);

  return (
    <DiffusionContext.Provider 
      value={{ 
        isDiffusionActive, 
        isBlinking, 
        handleDiffusionChange, 
        handleDiffusionClick 
      }}
    >
      {children}
    </DiffusionContext.Provider>
  );
};

export const useDiffusion = () => {
  const context = useContext(DiffusionContext);
  if (!context) {
    throw new Error('useDiffusion must be used within a DiffusionProvider');
  }
  return context;
}; 