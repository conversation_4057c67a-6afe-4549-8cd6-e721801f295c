import React, { createContext, useContext, useState, useCallback } from 'react';

const MapContext = createContext();

export const MapProvider = ({ children, initialMap, initialCollapsed = true }) => {
  const [map, setMap] = useState(initialMap);
  const [isCollapsed, setIsCollapsed] = useState(initialCollapsed);

  const handleCollapseChange = useCallback((newState) => {
    console.log('MapContext: handleCollapseChange called with:', newState);
    console.log('MapContext: current isCollapsed state:', isCollapsed);
    setIsCollapsed(newState);
    console.log('MapContext: isCollapsed state updated to:', newState);
  }, [isCollapsed]);

  const getEffectiveMap = useCallback(() => {
    const directMapRef = map;
    const windowMapRef = window.mapComponent?.map;
    return directMapRef || windowMapRef;
  }, [map]);

  return (
    <MapContext.Provider 
      value={{ 
        map,
        setMap,
        isCollapsed,
        handleCollapseChange,
        getEffectiveMap
      }}
    >
      {children}
    </MapContext.Provider>
  );
};

export const useMap = () => {
  const context = useContext(MapContext);
  if (!context) {
    throw new Error('useMap must be used within a MapProvider');
  }
  return context;
}; 