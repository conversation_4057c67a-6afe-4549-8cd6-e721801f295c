import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import styled from 'styled-components';

const LegendContainer = styled.div`
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(33, 33, 33, 0.9);
  padding: 10px;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  font-family: Arial, sans-serif;
  z-index: 1000;
  color: #ffffff;
`;

const LegendTitle = styled.h4`
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #ffffff;
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
  margin: 4px 0;
  font-size: 12px;
  color: #e0e0e0;
`;

const LegendColor = styled.div`
  width: 20px;
  height: 10px;
  margin-right: 8px;
  background: ${props => props.color};
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const DemographicDensityLayer = ({ map, visible }) => {
  const layerLoaded = useRef(false);
  const popupRef = useRef(null);
  const dataRef = useRef(null);
  const styleLoadHandler = useRef(null);
  const legendRef = useRef(null);

  useEffect(() => {
    if (!map?.current) return;

    const loadDemographicDensityLayer = async () => {
      try {
        // Only fetch data if we haven't already
        if (!dataRef.current) {
          const response = await fetch('/data/osm/saoPao/densidadedemografica_wgs84.geojson');
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
          dataRef.current = await response.json();
        }

        // Add or update source
        if (map.current.getSource('demographic-density')) {
          map.current.removeSource('demographic-density');
        }
        map.current.addSource('demographic-density', {
          type: 'geojson',
          data: dataRef.current
        });

        // Add or update layer
        if (map.current.getLayer('demographic-density-fill')) {
          map.current.removeLayer('demographic-density-fill');
        }
        map.current.addLayer({
          id: 'demographic-density-fill',
          type: 'fill',
          source: 'demographic-density',
          paint: {
            'fill-color': [
              'interpolate',
              ['linear'],
              ['get', 'dd_hab_hec'],
              0, '#1a1a2e',    // Dark blue-black for low density
              50, '#16213e',   // Dark blue for medium-low density
              100, '#0f3460',  // Deep blue for medium density
              150, '#533483',  // Purple for medium-high density
              200, '#e94560'   // Bright red for high density
            ],
            'fill-opacity': 0.3
          },
          layout: {
            visibility: visible ? 'visible' : 'none'
          }
        });

        // Create popup if it doesn't exist
        if (!popupRef.current) {
          popupRef.current = new mapboxgl.Popup({
            closeButton: false,
            closeOnClick: false
          });
        }

        // Add event handlers if not already added
        if (!layerLoaded.current) {
          map.current.on('mousemove', 'demographic-density-fill', (e) => {
            if (e.features.length > 0) {
              map.current.getCanvas().style.cursor = 'pointer';
              const properties = e.features[0].properties;
              console.log('Hovering over area:', {
                id: properties.dd_id,
                sector: properties.dd_setor,
                year: properties.dd_ano,
                households: properties.dd_qt_domi,
                population: properties.dd_populac,
                area_hectares: properties.dd_hectare,
                density: properties.dd_hab_hec
              });
            }
          });

          map.current.on('mouseleave', 'demographic-density-fill', () => {
            map.current.getCanvas().style.cursor = '';
          });

          layerLoaded.current = true;
        }

        // Update visibility and zoom to layer if visible
        map.current.setLayoutProperty('demographic-density-fill', 'visibility', visible ? 'visible' : 'none');
        if (visible) {
          map.current.moveLayer('demographic-density-fill');
          
          // Calculate bounds of all features
          const bounds = new mapboxgl.LngLatBounds();
          dataRef.current.features.forEach(feature => {
            feature.geometry.coordinates[0].forEach(coord => {
              bounds.extend(coord);
            });
          });
          
          // Add padding to bounds
          const padding = { top: 50, bottom: 50, left: 50, right: 50 };
          
          // Fit map to bounds with animation
          map.current.fitBounds(bounds, {
            padding,
            duration: 1000,
            maxZoom: 15
          });
        }

      } catch (error) {
        console.error('Error in demographic density layer:', error);
      }
    };

    const initializeLayer = () => {
      if (map.current.isStyleLoaded()) {
        loadDemographicDensityLayer();
      } else {
        // Store the handler reference so we can remove it later
        styleLoadHandler.current = () => {
          loadDemographicDensityLayer();
          map.current.off('style.load', styleLoadHandler.current);
        };
        map.current.on('style.load', styleLoadHandler.current);
      }
    };

    // Initialize layer if map is ready
    if (map.current.loaded()) {
      initializeLayer();
    } else {
      const onMapLoad = () => {
        initializeLayer();
        map.current.off('load', onMapLoad);
      };
      map.current.on('load', onMapLoad);
    }

    // Cleanup
    return () => {
      if (map.current) {
        map.current.off('click', 'demographic-density-fill');
        map.current.off('mousemove', 'demographic-density-fill');
        map.current.off('mouseleave', 'demographic-density-fill');
        if (styleLoadHandler.current) {
          map.current.off('style.load', styleLoadHandler.current);
        }
        
        if (popupRef.current) {
          popupRef.current.remove();
        }
        
        if (map.current.getLayer('demographic-density-fill')) {
          map.current.removeLayer('demographic-density-fill');
        }
        if (map.current.getSource('demographic-density')) {
          map.current.removeSource('demographic-density');
        }
      }
    };
  }, [map, visible]);

  return visible ? (
    <LegendContainer ref={legendRef}>
      <LegendTitle>Demographic Density</LegendTitle>
      <LegendItem>
        <LegendColor color="#1a1a2e" />
        <span>Low (0-50)</span>
      </LegendItem>
      <LegendItem>
        <LegendColor color="#0f3460" />
        <span>Medium (50-100)</span>
      </LegendItem>
      <LegendItem>
        <LegendColor color="#533483" />
        <span>High (100-150)</span>
      </LegendItem>
      <LegendItem>
        <LegendColor color="#e94560" />
        <span>Very High (150+)</span>
      </LegendItem>
      <div style={{ fontSize: '11px', color: '#b0b0b0', marginTop: '4px' }}>
        inhabitants/hectare
      </div>
    </LegendContainer>
  ) : null;
};

export default DemographicDensityLayer; 