import React from 'react';
import PropTypes from 'prop-types';

// Example columns: ["Axes", "Inv. (sq.m)", "Vacancy", "Price (BRL/sq.m)", "Net Abs. (sq.m) 2025Q1", "Const. (sq.m)", "Proposed (sq.m)"]
// Example data: array of objects with keys matching columns

const getCellBg = (col, value) => {
  // Simple color logic for demo; you can expand this for more accurate coloring
  if (col === 'Vacancy') {
    const num = parseFloat(value);
    if (num > 20) return '#7c3a3a'; // dark red
    if (num > 10) return '#bfa14a'; // dark yellow
    return '#2a4d3a'; // dark green
  }
  if (col === 'Price (BRL/sq.m)') {
    return '#23404a';
  }
  if (col === 'Net Abs. (sq.m) 2025Q1') {
    return '#4a4a23';
  }
  if (col === 'Const. (sq.m)' || col === 'Proposed (sq.m)' || col === 'Inv. (sq.m)') {
    return '#234a3a';
  }
  return 'transparent';
};

const DataCard = ({ columns, data, axesColWidth = 140, colWidth = 70, fontSize = 12 }) => {
  return (
    <div style={{
      background: '#181c23',
      borderRadius: 12,
      padding: fontSize < 10 ? 6 : 12,
      boxShadow: '0 4px 24px rgba(0,0,0,0.7)',
      color: '#f3f3f3',
      overflowX: 'auto',
      fontFamily: 'Inter, Segoe UI, Arial, sans-serif',
    }}>
      <table style={{
        width: 'auto',
        borderCollapse: 'collapse',
        background: 'none',
        fontSize,
      }}>
        <thead>
          <tr>
            {columns.map((col, i) => (
              <th key={col} style={{
                background: '#5a5d61',
                color: '#fff',
                fontWeight: 600,
                fontSize: fontSize + 1,
                padding: fontSize < 10 ? '3px 2px' : '6px 4px',
                border: 'none',
                textAlign: i === 0 ? 'left' : 'center',
                letterSpacing: 0.2,
                minWidth: i === 0 ? axesColWidth : colWidth,
                width: i === 0 ? axesColWidth : colWidth,
              }}>{col}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, idx) => (
            <tr key={row[columns[0]] + idx} style={{
              background: idx % 2 === 0 ? '#23262b' : '#1a1d22',
            }}>
              {columns.map((col, j) => (
                <td key={col} style={{
                  padding: fontSize < 10 ? '2px 2px' : '5px 4px',
                  color: j === 0 ? '#fff' : '#e0e0e0',
                  fontWeight: j === 0 ? 500 : 400,
                  textAlign: j === 0 ? 'left' : 'center',
                  background: getCellBg(col, row[col]),
                  border: 'none',
                  fontSize,
                  borderRight: j === columns.length - 1 ? 'none' : '1px solid #333',
                  borderLeft: j === 0 ? 'none' : '1px solid #222',
                  minWidth: j === 0 ? axesColWidth : colWidth,
                  width: j === 0 ? axesColWidth : colWidth,
                }}>
                  {row[col]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

DataCard.propTypes = {
  columns: PropTypes.arrayOf(PropTypes.string).isRequired,
  data: PropTypes.arrayOf(PropTypes.object).isRequired,
  axesColWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  colWidth: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  fontSize: PropTypes.number,
};

export default DataCard; 