import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import './styles/LogisticsPopup.css';

const LogisticsMarkersLayer = ({ map, visible }) => {
  const layerLoaded = useRef(false);
  const popupRef = useRef(null);
  const dataRef = useRef(null);
  const styleLoadHandler = useRef(null);
  const markersRef = useRef([]);
  const selectedMarkerRef = useRef(null);
  const selectedMarkerOriginalStyle = useRef({});

  useEffect(() => {
    if (!map?.current) return;

    const loadLogisticsMarkersLayer = async () => {
      try {
        // Only fetch data if we haven't already
        if (!dataRef.current) {
          const response = await fetch('/data/osm/saoPao/logistics_enriched.geojson');
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
          dataRef.current = await response.json();
        }

        // Clear existing markers
        markersRef.current.forEach(marker => marker.remove());
        markersRef.current = [];

        // Create popup if it doesn't exist
        if (!popupRef.current) {
          popupRef.current = new mapboxgl.Popup({
            closeButton: false,
            closeOnClick: false,
            className: 'logistics-popup',
            maxWidth: 'none'
          });
        }

        // Add markers for each feature
        dataRef.current.features.forEach(feature => {
          const coordinates = feature.geometry.coordinates;
          const properties = feature.properties;
          const googlePlaces = properties.google_places || {};
          const nearbyPlaces = properties.nearby_places || [];

          // Create marker element
          const el = document.createElement('div');
          el.className = 'logistics-marker';
          el.style.width = '10px';
          el.style.height = '10px';
          el.style.borderRadius = '50%';
          el.style.backgroundColor = '#4CAF50';
          el.style.border = '2px solid white';
          el.style.cursor = 'pointer';
          el.style.boxShadow = '0 0 4px rgba(0,0,0,0.3)';

          // Create marker
          const marker = new mapboxgl.Marker(el)
            .setLngLat(coordinates)
            .addTo(map.current);

          // Add click handler
          el.addEventListener('click', () => {
            // Reset previous selected marker style
            if (selectedMarkerRef.current && selectedMarkerRef.current !== el) {
              const prev = selectedMarkerRef.current;
              const orig = selectedMarkerOriginalStyle.current;
              prev.style.width = orig.width;
              prev.style.height = orig.height;
              prev.style.backgroundColor = orig.backgroundColor;
              prev.style.zIndex = orig.zIndex;
              prev.style.border = orig.border;
              prev.style.boxShadow = orig.boxShadow;
            }
            // Store original style
            selectedMarkerOriginalStyle.current = {
              width: el.style.width,
              height: el.style.height,
              backgroundColor: el.style.backgroundColor,
              zIndex: el.style.zIndex,
              border: el.style.border,
              boxShadow: el.style.boxShadow,
            };
            // Enlarge and recolor this marker
            el.style.width = '30px';
            el.style.height = '30px';
            el.style.backgroundColor = '#00FF00'; // bright green
            el.style.zIndex = '1000';
            el.style.border = '3px solid #222';
            el.style.boxShadow = '0 0 12px 2px rgba(0,128,0,0.4)';
            selectedMarkerRef.current = el;

            // Build address from tags
            const tags = properties.tags || {};
            const street = tags['addr:street'] || '';
            const housenumber = tags['addr:housenumber'] || '';
            const city = tags['addr:city'] || '';
            const postcode = tags['addr:postcode'] || '';
            let address = '';
            if (street && housenumber && city) {
              address = `${street}, ${housenumber}, ${city}`;
            } else if (street && city) {
              address = `${street}, ${city}`;
            } else if (city) {
              address = city;
            }
            if (postcode) {
              address += `, ${postcode}`;
            }
            // Fallback to googlePlaces.vicinity or details.formatted_address if address is empty
            if (!address) {
              address = googlePlaces.details?.formatted_address || googlePlaces.vicinity || '';
            }
            // Determine type: tags.building, else first google_places.types, else 'Logistics'
            let type = '';
            if (tags['building']) {
              type = tags['building'][0].toUpperCase() + tags['building'].slice(1);
            } else if (googlePlaces.types && googlePlaces.types.length > 0) {
              type = googlePlaces.types[0][0].toUpperCase() + googlePlaces.types[0].slice(1);
            } else {
              type = 'Logistics';
            }
            const name = googlePlaces.name || properties.name || '';
            const showName = false;

            const popupContent = `
              <div class="logistics-popup-grab"></div>
              <button class="logistics-popup-close" id="logistics-popup-close">&times;</button>
              
              <div class="logistics-popup-header">
                <h2 class="logistics-popup-address">${address || 'Address not available'}</h2>
                <p class="logistics-popup-coordinates">${coordinates[1].toFixed(6)}, ${coordinates[0].toFixed(6)}</p>
              </div>

              <div class="logistics-popup-main">
                ${type ? `<div class=\"logistics-popup-type-row\">
                  <span class=\"logistics-popup-badge type\">${type}</span>
                  <span class=\"logistics-popup-badge\">12K SQM</span>
                  <span class=\"logistics-popup-badge energy\">Solar</span>
                </div>` : ''}
                ${googlePlaces.rating ? `
                  <div class=\"logistics-popup-rating\">
                    ${'⭐'.repeat(Math.round(googlePlaces.rating))}
                    <span class=\"logistics-popup-rating-count\">(${googlePlaces.user_ratings_total || 0} reviews)</span>
                  </div>
                ` : ''}
              </div>

              <button class="logistics-popup-toggle" id="logistics-popup-toggle">
                Show More Details
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M6 9l6 6 6-6"/>
                </svg>
              </button>

              <div class="logistics-popup-details" id="logistics-popup-details">
                ${googlePlaces.types ? `
                  <div class="logistics-popup-section">
                    <h4 class="logistics-popup-section-title">Types</h4>
                    <p class="logistics-popup-info">${googlePlaces.types.join(', ')}</p>
                  </div>
                ` : ''}

                ${googlePlaces.details?.formatted_phone_number ? `
                  <div class="logistics-popup-section">
                    <h4 class="logistics-popup-section-title">Contact</h4>
                    <p class="logistics-popup-info">${googlePlaces.details.formatted_phone_number}</p>
                  </div>
                ` : ''}

                ${googlePlaces.details?.website ? `
                  <div class="logistics-popup-section">
                    <h4 class="logistics-popup-section-title">Website</h4>
                    <p class="logistics-popup-info">
                      <a href="${googlePlaces.details.website}" target="_blank">${googlePlaces.details.website}</a>
                    </p>
                  </div>
                ` : ''}

                ${googlePlaces.details?.opening_hours?.weekday_text ? `
                  <div class="logistics-popup-section">
                    <h4 class="logistics-popup-section-title">Opening Hours</h4>
                    ${googlePlaces.details.opening_hours.weekday_text.map(day => `
                      <p class="logistics-popup-info">${day}</p>
                    `).join('')}
                  </div>
                ` : ''}

                ${nearbyPlaces.length > 0 ? `
                  <div class="logistics-popup-section logistics-popup-nearby">
                    <h4 class="logistics-popup-section-title">Nearby Places</h4>
                    ${nearbyPlaces.map(place => `
                      <div class="logistics-popup-nearby-item">
                        <h5 class="logistics-popup-nearby-name">${place.name}</h5>
                        ${place.rating ? `
                          <p class="logistics-popup-nearby-info">
                            Rating: ${'⭐'.repeat(Math.round(place.rating))} (${place.user_ratings_total || 0} reviews)
                          </p>
                        ` : ''}
                        ${place.vicinity ? `
                          <p class="logistics-popup-nearby-info">${place.vicinity}</p>
                        ` : ''}
                        ${place.types ? `
                          <p class="logistics-popup-nearby-info">${place.types.join(', ')}</p>
                        ` : ''}
                      </div>
                    `).join('')}
                  </div>
                ` : ''}

                ${properties.height ? `
                  <div class="logistics-popup-section">
                    <h4 class="logistics-popup-section-title">Building Information</h4>
                    <p class="logistics-popup-info">Height: ${properties.height}m</p>
                  </div>
                ` : ''}
              </div>
            `;

            popupRef.current
              .setLngLat(coordinates)
              .setHTML(popupContent)
              .addTo(map.current);

            // Add event handlers
            setTimeout(() => {
              const closeBtn = document.getElementById('logistics-popup-close');
              const toggleBtn = document.getElementById('logistics-popup-toggle');
              const detailsDiv = document.getElementById('logistics-popup-details');
              const grabHandle = document.querySelector('.logistics-popup-grab');

              if (closeBtn) {
                closeBtn.onclick = () => {
                  popupRef.current.remove();
                  // Reset marker style on popup close
                  if (selectedMarkerRef.current) {
                    const orig = selectedMarkerOriginalStyle.current;
                    selectedMarkerRef.current.style.width = orig.width;
                    selectedMarkerRef.current.style.height = orig.height;
                    selectedMarkerRef.current.style.backgroundColor = orig.backgroundColor;
                    selectedMarkerRef.current.style.zIndex = orig.zIndex;
                    selectedMarkerRef.current.style.border = orig.border;
                    selectedMarkerRef.current.style.boxShadow = orig.boxShadow;
                    selectedMarkerRef.current = null;
                  }
                };
              }

              if (toggleBtn && detailsDiv) {
                toggleBtn.onclick = () => {
                  const isExpanded = detailsDiv.classList.contains('expanded');
                  detailsDiv.classList.toggle('expanded');
                  toggleBtn.classList.toggle('expanded');
                  toggleBtn.innerHTML = isExpanded ? 
                    'Show More Details <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M6 9l6 6 6-6"/></svg>' :
                    'Show Less Details <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M6 9l6 6 6-6"/></svg>';
                };
              }

              if (grabHandle) {
                grabHandle.addEventListener('mousedown', (e) => {
                  const popup = popupRef.current.getElement();
                  const startX = e.clientX;
                  const startY = e.clientY;
                  const startLeft = parseInt(popup.style.left || 0, 10);
                  const startTop = parseInt(popup.style.top || 0, 10);

                  const onMouseMove = (e) => {
                    const dx = e.clientX - startX;
                    const dy = e.clientY - startY;
                    popup.style.left = (startLeft + dx) + 'px';
                    popup.style.top = (startTop + dy) + 'px';
                  };

                  const onMouseUp = () => {
                    document.removeEventListener('mousemove', onMouseMove);
                    document.removeEventListener('mouseup', onMouseUp);
                  };

                  document.addEventListener('mousemove', onMouseMove);
                  document.addEventListener('mouseup', onMouseUp);
                });
              }
            }, 0);
          });

          // Store marker reference
          markersRef.current.push(marker);
        });

        // Update visibility
        markersRef.current.forEach(marker => {
          marker.getElement().style.display = visible ? 'block' : 'none';
        });

        layerLoaded.current = true;

      } catch (error) {
        console.error('Error in logistics markers layer:', error);
      }
    };

    const initializeLayer = () => {
      if (map.current.isStyleLoaded()) {
        loadLogisticsMarkersLayer();
      } else {
        // Store the handler reference so we can remove it later
        styleLoadHandler.current = () => {
          loadLogisticsMarkersLayer();
          map.current.off('style.load', styleLoadHandler.current);
        };
        map.current.on('style.load', styleLoadHandler.current);
      }
    };

    // Initialize layer if map is ready
    if (map.current.loaded()) {
      initializeLayer();
    } else {
      const onMapLoad = () => {
        initializeLayer();
        map.current.off('load', onMapLoad);
      };
      map.current.on('load', onMapLoad);
    }

    // Cleanup
    return () => {
      if (map.current) {
        if (styleLoadHandler.current) {
          map.current.off('style.load', styleLoadHandler.current);
        }
        
        if (popupRef.current) {
          popupRef.current.remove();
        }
        
        // Remove all markers
        markersRef.current.forEach(marker => marker.remove());
        markersRef.current = [];
      }
    };
  }, [map, visible]);

  return null;
};

export default LogisticsMarkersLayer; 