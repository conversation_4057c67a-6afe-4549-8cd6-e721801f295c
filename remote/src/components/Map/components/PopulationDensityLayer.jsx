import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import { toWgs84 } from '@turf/turf';

const PopulationDensityLayer = ({ map, visible }) => {
  const layerLoaded = useRef(false);
  const popupRef = useRef(null);
  const dataRef = useRef(null);
  const styleLoadHandler = useRef(null);

  useEffect(() => {
    if (!map?.current) return;

    const loadPopulationDensityLayer = async () => {
      try {
        // Only fetch and transform data if we haven't already
        if (!dataRef.current) {
          const response = await fetch('/data/osm/saoPao/densidadedemografica.geojson');
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
          const data = await response.json();
          dataRef.current = toWgs84(data);
        }

        // Add or update source
        if (map.current.getSource('population-density')) {
          map.current.removeSource('population-density');
        }
        map.current.addSource('population-density', {
          type: 'geojson',
          data: dataRef.current
        });

        // Add or update layer
        if (map.current.getLayer('population-density-fill')) {
          map.current.removeLayer('population-density-fill');
        }
        map.current.addLayer({
          id: 'population-density-fill',
          type: 'fill',
          source: 'population-density',
          paint: {
            'fill-color': '#00ff00',
            'fill-opacity': 0.3,
            'fill-outline-color': '#ffffff'
          },
          layout: {
            visibility: visible ? 'visible' : 'none'
          }
        });

        // Create popup if it doesn't exist
        if (!popupRef.current) {
          popupRef.current = new mapboxgl.Popup({
            closeButton: false,
            closeOnClick: false
          });
        }

        // Add event handlers if not already added
        if (!layerLoaded.current) {
          map.current.on('mousemove', 'population-density-fill', (e) => {
            if (e.features.length > 0) {
              map.current.getCanvas().style.cursor = 'pointer';
              const properties = e.features[0].properties;
              console.log('Hovering over area:', {
                id: properties.dd_id,
                area: properties.dd_area,
                census_sector: properties.dd_setor,
                year: properties.dd_ano,
                households: properties.dd_qt_domi,
                population: properties.dd_populac,
                area_hectares: properties.dd_hectare,
                population_per_hectare: properties.dd_hab_hec
              });
            }
          });

          map.current.on('mouseleave', 'population-density-fill', () => {
            map.current.getCanvas().style.cursor = '';
          });

          map.current.on('click', 'population-density-fill', (e) => {
            const coordinates = e.lngLat;
            const properties = e.features[0].properties;

            const popupContent = `
              <div style="padding: 10px;">
                <h3 style="margin: 0 0 10px 0;">Population Density</h3>
                <p style="margin: 0 0 5px 0;"><strong>ID:</strong> ${properties.dd_id}</p>
                <p style="margin: 0 0 5px 0;"><strong>Area:</strong> ${properties.dd_area.toFixed(2)} m²</p>
                <p style="margin: 0 0 5px 0;"><strong>Census Sector:</strong> ${properties.dd_setor}</p>
                <p style="margin: 0 0 5px 0;"><strong>Year:</strong> ${properties.dd_ano}</p>
                <p style="margin: 0 0 5px 0;"><strong>Households:</strong> ${properties.dd_qt_domi.toLocaleString()}</p>
                <p style="margin: 0 0 5px 0;"><strong>Population:</strong> ${properties.dd_populac.toLocaleString()} inhabitants</p>
                <p style="margin: 0 0 5px 0;"><strong>Area:</strong> ${properties.dd_hectare.toFixed(2)} hectares</p>
                <p style="margin: 0 0 5px 0;"><strong>Density:</strong> ${Math.round(properties.dd_hab_hec)} inhabitants/hectare</p>
              </div>
            `;

            popupRef.current
              .setLngLat(coordinates)
              .setHTML(popupContent)
              .addTo(map.current);
          });

          layerLoaded.current = true;
        }

        // Update visibility
        map.current.setLayoutProperty('population-density-fill', 'visibility', visible ? 'visible' : 'none');
        if (visible) {
          map.current.moveLayer('population-density-fill');
        }

      } catch (error) {
        console.error('Error in population density layer:', error);
      }
    };

    const initializeLayer = () => {
      if (map.current.isStyleLoaded()) {
        loadPopulationDensityLayer();
      } else {
        // Store the handler reference so we can remove it later
        styleLoadHandler.current = () => {
          loadPopulationDensityLayer();
          map.current.off('style.load', styleLoadHandler.current);
        };
        map.current.on('style.load', styleLoadHandler.current);
      }
    };

    // Initialize layer if map is ready
    if (map.current.loaded()) {
      initializeLayer();
    } else {
      const onMapLoad = () => {
        initializeLayer();
        map.current.off('load', onMapLoad);
      };
      map.current.on('load', onMapLoad);
    }

    // Cleanup
    return () => {
      if (map.current) {
        map.current.off('click', 'population-density-fill');
        map.current.off('mousemove', 'population-density-fill');
        map.current.off('mouseleave', 'population-density-fill');
        if (styleLoadHandler.current) {
          map.current.off('style.load', styleLoadHandler.current);
        }
        
        if (popupRef.current) {
          popupRef.current.remove();
        }
        
        if (map.current.getLayer('population-density-fill')) {
          map.current.removeLayer('population-density-fill');
        }
        if (map.current.getSource('population-density')) {
          map.current.removeSource('population-density');
        }
      }
    };
  }, [map, visible]);

  return null;
};

export default PopulationDensityLayer; 