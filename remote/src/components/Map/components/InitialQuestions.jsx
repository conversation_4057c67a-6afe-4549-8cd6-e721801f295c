import React, { useState, useRef, useEffect, useCallback } from 'react';

const InitialQuestions = ({ selectedModel, isDiffusionActive, onQuestionClick }) => {
  const [buttonRefs, setButtonRefs] = useState([]);
  const containerRef = useRef(null);
  const instanceId = useRef(`questions-${Math.random().toString(36).substr(2, 9)}`);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.setAttribute('data-instance-id', instanceId.current);
    }
  }, []);

  useEffect(() => {
    // Only log in development environment
    if (process.env.NODE_ENV === 'development') {
      console.debug('InitialQuestions mounted');
    }
  }, []);

  const getModelTheme = useCallback((model) => {
    return '#8b5cf6'; // Default theme color
  }, []);

  // ... rest of the component code ...

  return (
    <div ref={containerRef} className="questions-container">
      {/* ... existing JSX ... */}
    </div>
  );
};

export default React.memo(InitialQuestions, (prevProps, nextProps) => {
  // Only log in development environment
  if (process.env.NODE_ENV === 'development') {
    console.debug('InitialQuestions memo comparison:', {
      prevDiffusion: prevProps.isDiffusionActive,
      nextDiffusion: nextProps.isDiffusionActive,
      prevModel: prevProps.selectedModel,
      nextModel: nextProps.selectedModel
    });
  }
  
  return prevProps.isDiffusionActive === nextProps.isDiffusionActive &&
         prevProps.selectedModel === nextProps.selectedModel;
}); 