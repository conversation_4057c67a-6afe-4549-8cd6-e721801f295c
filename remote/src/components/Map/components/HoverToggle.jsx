import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import styled from 'styled-components';
import { CategorySection, CategoryHeader, CategoryIcon, CategoryTitle, ToggleSwitch } from './styles/LayerToggleStyles';

const neighborhoodsSourceId = 'hover-neighborhoods';
const neighborhoodsFillLayerId = 'hover-neighborhoods-fill';
const neighborhoodsOutlineLayerId = 'hover-neighborhoods-outline';
const buildingsSourceId = 'hover-buildings';
const buildingsLayerId = 'hover-buildings-3d';

export default function HoverToggle({ map, enabled }) {
  const [hoverEnabled, setHoverEnabled] = useState(enabled);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const hoveredIdRef = useRef(null);
  const currentNeighborhoodRef = useRef(null);

  // Update local state when prop changes
  useEffect(() => {
    console.log('HoverToggle: enabled prop changed:', enabled);
    setHoverEnabled(enabled);
  }, [enabled]);

  // Handle map loading
  useEffect(() => {
    if (!map?.current) {
      console.log('HoverToggle: Waiting for map to be available');
      return;
    }

    const mapInstance = map.current;

    // Check if map is already loaded
    if (mapInstance.loaded()) {
      setIsMapLoaded(true);
    } else {
      // Wait for map to load
      mapInstance.once('load', () => {
        console.log('HoverToggle: Map loaded');
        setIsMapLoaded(true);
      });
    }

    return () => {
      if (mapInstance) {
        mapInstance.off('load');
      }
    };
  }, [map]);

  // Function to load buildings for a neighborhood
  const loadBuildingsForNeighborhood = async (neighborhoodName) => {
    if (!map?.current) return;
    const mapInstance = map.current;

    // If we're already showing this neighborhood's buildings, don't reload
    if (currentNeighborhoodRef.current === neighborhoodName) {
      return;
    }

    try {
      // First remove existing layers and sources
      if (mapInstance.getLayer(buildingsLayerId)) {
        mapInstance.removeLayer(buildingsLayerId);
      }
      if (mapInstance.getSource(buildingsSourceId)) {
        mapInstance.removeSource(buildingsSourceId);
      }

      // Wait for the next frame to ensure cleanup is complete
      await new Promise(resolve => setTimeout(resolve, 0));

      const response = await fetch(`/data/osm/saoPao/${neighborhoodName}.geojson`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      
      console.log('HoverToggle: Successfully loaded buildings data for:', neighborhoodName);

      // Add the new source
      mapInstance.addSource(buildingsSourceId, {
        type: 'geojson',
        data: data
      });

      // Add the new layer
      mapInstance.addLayer({
        id: buildingsLayerId,
        type: 'fill-extrusion',
        source: buildingsSourceId,
        paint: {
          'fill-extrusion-color': '#FF4500',
          'fill-extrusion-height': [
            'coalesce',
            ['get', 'height'],
            10 // default height if missing
          ],
          'fill-extrusion-opacity': 0.7,
          'fill-extrusion-vertical-gradient': true,
          'fill-extrusion-ambient-occlusion-intensity': 0.6,
          'fill-extrusion-ambient-occlusion-radius': 3
        },
      });

      console.log('HoverToggle: Successfully added buildings layer for:', neighborhoodName);
      currentNeighborhoodRef.current = neighborhoodName;
    } catch (error) {
      console.error('HoverToggle: Error loading buildings data:', error);
      // Reset the current neighborhood ref on error
      currentNeighborhoodRef.current = null;
    }
  };

  // Function to remove buildings
  const removeBuildings = () => {
    if (!map?.current) return;
    const mapInstance = map.current;

    if (mapInstance.getLayer(buildingsLayerId)) {
      mapInstance.removeLayer(buildingsLayerId);
    }
    if (mapInstance.getSource(buildingsSourceId)) {
      mapInstance.removeSource(buildingsSourceId);
    }
    currentNeighborhoodRef.current = null;
  };

  useEffect(() => {
    if (!isMapLoaded || !map?.current) {
      console.log('HoverToggle: Map not ready yet');
      return;
    }

    console.log('HoverToggle: Component mounted/updated', {
      mapExists: !!map,
      hoverEnabled,
      enabled,
      mapInstance: map ? 'exists' : 'null'
    });

    const mapInstance = map.current;

    function onMouseMove(e) {
      if (!e.features.length) {
        console.log('HoverToggle: Mouse moved but no features found');
        return;
      }

      const feature = e.features[0];
      const id = feature.properties?.ds_codigo;
      
      if (hoveredIdRef.current !== null && hoveredIdRef.current !== id) {
        console.log('HoverToggle: Removing hover state from previous feature:', hoveredIdRef.current);
        mapInstance.setFeatureState(
          { source: neighborhoodsSourceId, id: hoveredIdRef.current },
          { hover: false }
        );
      }
      
      hoveredIdRef.current = id;
      console.log('HoverToggle: Setting hover state for feature:', id);
      mapInstance.setFeatureState(
        { source: neighborhoodsSourceId, id },
        { hover: true }
      );
    }

    function onMouseLeave() {
      if (hoveredIdRef.current !== null) {
        console.log('HoverToggle: Removing hover state from feature:', hoveredIdRef.current);
        mapInstance.setFeatureState(
          { source: neighborhoodsSourceId, id: hoveredIdRef.current },
          { hover: false }
        );
        hoveredIdRef.current = null;
      }
    }

    function onClick(e) {
      if (!e.features.length) {
        console.log('HoverToggle: Clicked but no features found');
        return;
      }

      const feature = e.features[0];
      const neighborhoodName = feature.properties?.ds_nome;

      // Get the bounds of the clicked feature
      const bounds = new mapboxgl.LngLatBounds();
      const coordinates = feature.geometry.coordinates[0];
      coordinates.forEach(coord => {
        bounds.extend(coord);
      });

      // Add padding to the bounds
      const padding = { top: 50, bottom: 50, left: 50, right: 50 };

      // Animate to the bounds
      mapInstance.fitBounds(bounds, {
        padding: padding,
        duration: 1000, // 1 second animation
        maxZoom: 18, // Don't zoom in too close
        essential: true
      });

      // If we click the same neighborhood that's already showing, remove the buildings
      if (currentNeighborhoodRef.current === neighborhoodName) {
        removeBuildings();
      } else {
        // Load buildings for this neighborhood
        if (neighborhoodName) {
          loadBuildingsForNeighborhood(neighborhoodName);
        }
      }
    }

    // Cleanup function
    const cleanup = () => {
      console.log('HoverToggle: Cleaning up');
      if (hoveredIdRef.current !== null) {
        mapInstance.setFeatureState(
          { source: neighborhoodsSourceId, id: hoveredIdRef.current },
          { hover: false }
        );
        hoveredIdRef.current = null;
      }
      mapInstance.off('mousemove', neighborhoodsFillLayerId, onMouseMove);
      mapInstance.off('mouseleave', neighborhoodsFillLayerId, onMouseLeave);
      mapInstance.off('click', neighborhoodsFillLayerId, onClick);

      // Remove layers and source if they exist
      if (mapInstance.getLayer(neighborhoodsOutlineLayerId)) {
        mapInstance.removeLayer(neighborhoodsOutlineLayerId);
      }
      if (mapInstance.getLayer(neighborhoodsFillLayerId)) {
        mapInstance.removeLayer(neighborhoodsFillLayerId);
      }
      removeBuildings();
      if (mapInstance.getSource(neighborhoodsSourceId)) {
        mapInstance.removeSource(neighborhoodsSourceId);
      }
    };

    // If disabled, clean up and return
    if (!hoverEnabled) {
      cleanup();
      return;
    }

    console.log('HoverToggle: Setting up hover functionality');
    
    // Add neighborhoods source
    if (!mapInstance.getSource(neighborhoodsSourceId)) {
      console.log('HoverToggle: Adding neighborhoods source');
      try {
        fetch('/data/osm/saoPao/distrito_wgs84.geojson')
          .then(response => {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            console.log('HoverToggle: Successfully loaded neighborhoods data');
            mapInstance.addSource(neighborhoodsSourceId, {
              type: 'geojson',
              data: data
            });
            console.log('HoverToggle: Successfully added neighborhoods source');

            // Add fill layer for hit detection
            mapInstance.addLayer({
              id: neighborhoodsFillLayerId,
              type: 'fill',
              source: neighborhoodsSourceId,
              paint: {
                'fill-opacity': 0,
              },
            });
            console.log('HoverToggle: Successfully added fill layer');

            // Add outline layer
            mapInstance.addLayer({
              id: neighborhoodsOutlineLayerId,
              type: 'line',
              source: neighborhoodsSourceId,
              paint: {
                'line-color': [
                  'case',
                  ['boolean', ['feature-state', 'hover'], false],
                  '#00FF00', // Green on hover
                  'rgba(0, 0, 0, 0)'  // Transparent when not hovered
                ],
                'line-width': [
                  'case',
                  ['boolean', ['feature-state', 'hover'], false],
                  2, // Normal width on hover
                  0  // No width when not hovered
                ],
                'line-opacity': [
                  'case',
                  ['boolean', ['feature-state', 'hover'], false],
                  1,  // Fully opaque on hover
                  0   // Transparent when not hovered
                ]
              },
            });
            console.log('HoverToggle: Successfully added outline layer');

            // Add event listeners
            console.log('HoverToggle: Adding event listeners');
            mapInstance.on('mousemove', neighborhoodsFillLayerId, onMouseMove);
            mapInstance.on('mouseleave', neighborhoodsFillLayerId, onMouseLeave);
            mapInstance.on('click', neighborhoodsFillLayerId, onClick);
          })
          .catch(error => {
            console.error('HoverToggle: Error loading neighborhoods data:', error);
          });
      } catch (error) {
        console.error('HoverToggle: Error setting up hover:', error);
        return;
      }
    }

    return cleanup;
  }, [hoverEnabled, map, isMapLoaded]);

  return (
    <CategorySection>
      <CategoryHeader>
        <CategoryIcon>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <rect x="4" y="4" width="16" height="16" rx="2" />
            <path d="M8 8h8v8H8z" />
          </svg>
        </CategoryIcon>
        <CategoryTitle>Neighborhood Hover/3D</CategoryTitle>
        <ToggleSwitch>
          <input
            type="checkbox"
            checked={hoverEnabled}
            onChange={() => setHoverEnabled(!hoverEnabled)}
          />
          <span></span>
        </ToggleSwitch>
      </CategoryHeader>
    </CategorySection>
  );
} 