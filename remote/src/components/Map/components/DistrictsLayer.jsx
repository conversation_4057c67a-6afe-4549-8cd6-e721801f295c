import React, { useEffect } from 'react';
import mapboxgl from 'mapbox-gl';

const DistrictsLayer = ({ map, visible }) => {
  useEffect(() => {
    if (!map.current) return;

    const addDistrictsLayer = () => {
      // Add source if it doesn't exist
      if (!map.current.getSource('districts')) {
        map.current.addSource('districts', {
          type: 'geojson',
          data: '/data/osm/saoPao/distrito_wgs84.geojson'
        });
      }

      // Add line layer if it doesn't exist
      if (!map.current.getLayer('districts-line')) {
        map.current.addLayer({
          id: 'districts-line',
          type: 'line',
          source: 'districts',
          layout: {
            visibility: visible ? 'visible' : 'none'
          },
          paint: {
            'line-color': '#ffffff',
            'line-width': 0.7,
            'line-opacity': 0.4
          }
        });
      } else {
        // Update visibility of existing layer
        map.current.setLayoutProperty('districts-line', 'visibility', visible ? 'visible' : 'none');
      }
    };

    // Check if style is already loaded
    if (map.current.isStyleLoaded()) {
      addDistrictsLayer();
    } else {
      // Wait for style to load
      map.current.on('style.load', addDistrictsLayer);
    }

    // Cleanup function
    return () => {
      if (map.current) {
        if (map.current.getLayer('districts-line')) {
          map.current.removeLayer('districts-line');
        }
        if (map.current.getSource('districts')) {
          map.current.removeSource('districts');
        }
        // Remove the style.load event listener
        map.current.off('style.load', addDistrictsLayer);
      }
    };
  }, [map, visible]);

  return null;
};

export default DistrictsLayer; 