import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';

const categoryColors = {
  elementary: '#60a5fa', // light blue
  middle: '#fbbf24',    // amber/gold
  high: '#f87171',      // red
  academy: '#a78bfa',   // purple
  charter: '#34d399',   // green
  university: '#f472b6',// pink
  college: '#38bdf8',   // cyan
  kindergarten: '#facc15', // yellow
  default: '#d1d5db'    // gray
};

function createPopupCard(feature, color) {
  const tags = feature.properties.tags || {};
  const address = [tags['addr:housenumber'], tags['addr:street'], tags['addr:city'], tags['addr:postcode']].filter(Boolean).join(' ');
  const website = tags.website;
  const phone = tags.phone;
  const category = feature.properties.category || 'school';
  const name = feature.properties.name || 'Unnamed';
  const popup = document.createElement('div');
  popup.style.background = '#18181b';
  popup.style.color = '#fff';
  popup.style.borderRadius = '10px';
  popup.style.boxShadow = '0 2px 16px rgba(0,0,0,0.35)';
  popup.style.padding = '14px 18px 12px 18px';
  popup.style.fontSize = '15px';
  popup.style.maxWidth = '260px';
  popup.style.border = `1.5px solid ${color}`;
  popup.style.pointerEvents = 'auto';
  popup.innerHTML = `
    <div style="font-weight:700;font-size:16px;margin-bottom:2px;color:${color}">${name}</div>
    <div style="font-size:13px;color:${color};margin-bottom:8px;text-transform:capitalize;">${category}</div>
    ${address ? `<div style='font-size:13px;margin-bottom:4px;color:#e5e7eb;'>${address}</div>` : ''}
    ${phone ? `<div style='font-size:13px;margin-bottom:4px;color:#e5e7eb;'>${phone}</div>` : ''}
    ${website ? `<a href='${website}' target='_blank' rel='noopener noreferrer' style='color:#60a5fa;font-size:13px;text-decoration:underline;'>Website</a>` : ''}
  `;
  return popup;
}

export default function SchoolMarkersLayer({ map, showSchoolMarkers, schoolMarkers, setSchoolMarkers }) {
  const popupRef = useRef(null);

  useEffect(() => {
    if (!map.current) return;
    if (!showSchoolMarkers) {
      // Remove all school markers
      schoolMarkers.forEach(marker => marker.remove());
      setSchoolMarkers([]);
      if (popupRef.current) {
        popupRef.current.remove();
        popupRef.current = null;
      }
      return;
    }
    let isMounted = true;
    // Fetch and add school markers
    (async () => {
      try {
        console.log('Toggling ON Austin Schools layer, fetching from /data/osm/austin_schools.geojson');
        const response = await fetch('/data/osm/austin_schools.geojson');
        if (!response.ok) throw new Error('Could not load austin_schools.geojson');
        const data = await response.json();
        const markers = data.features.map((feature, idx) => {
          const el = document.createElement('div');
          const cat = (feature.properties.category || '').toLowerCase();
          const color = categoryColors[cat] || categoryColors.default;
          el.style.width = '11px';
          el.style.height = '11px';
          el.style.background = '#18181b';
          el.style.borderRadius = '50%';
          el.style.display = 'flex';
          el.style.alignItems = 'center';
          el.style.justifyContent = 'center';
          el.style.boxShadow = '0 1px 4px rgba(0,0,0,0.18)';
          el.style.cursor = 'pointer';
          el.style.border = `1.5px solid ${color}`;
          el.title = feature.properties.name;
          el.innerHTML = `<svg width=\"8\" height=\"8\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"${color}\" stroke-width=\"2\"><path d=\"M12 3L2 9l10 6 10-6-10-6z\"/><path d=\"M2 9v6a2 2 0 0 0 2 2h2v-4h8v4h2a2 2 0 0 0 2-2V9\"/></svg>`;
          el.onclick = (e) => {
            e.stopPropagation();
            // Remove any existing popup
            if (popupRef.current) {
              popupRef.current.remove();
              popupRef.current = null;
            }
            // Create and show new popup
            const popup = createPopupCard(feature, color);
            popup.style.position = 'absolute';
            popup.style.zIndex = 9999;
            document.body.appendChild(popup);
            popupRef.current = popup;
            // Get marker position on screen
            const markerLngLat = feature.geometry.coordinates;
            const point = map.current.project(markerLngLat);
            // Position popup above marker
            popup.style.left = `${point.x - 120}px`;
            popup.style.top = `${point.y - 90}px`;
            // Remove popup on map click or move
            const removePopup = () => {
              if (popupRef.current) {
                popupRef.current.remove();
                popupRef.current = null;
              }
              map.current.off('click', removePopup);
              map.current.off('movestart', removePopup);
            };
            map.current.on('click', removePopup);
            map.current.on('movestart', removePopup);
          };
          const marker = new mapboxgl.Marker(el)
            .setLngLat(feature.geometry.coordinates)
            .addTo(map.current);
          return marker;
        });
        if (isMounted) setSchoolMarkers(markers);
      } catch (error) {
        alert('Could not load Austin schools data. Please ensure the data file exists at /data/osm/austin_schools.geojson');
        setSchoolMarkers([]);
      }
    })();
    return () => {
      isMounted = false;
      schoolMarkers.forEach(marker => marker.remove());
      setSchoolMarkers([]);
      if (popupRef.current) {
        popupRef.current.remove();
        popupRef.current = null;
      }
    };
    // eslint-disable-next-line
  }, [showSchoolMarkers, map]);
  return null;
} 