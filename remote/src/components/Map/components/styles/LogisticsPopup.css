/* Override Mapbox popup styles */
.mapboxgl-popup-content {
  width: 320px !important;
  background: #1a1a1a !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  padding: 0 !important;
}

.mapboxgl-popup-tip {
  display: none !important;
}

.logistics-popup-header {
  padding: 16px;
  border-bottom: 1px solid #333;
}

.logistics-popup-address {
  font-size: 18px;
  font-weight: 500;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.logistics-popup-coordinates {
  font-size: 12px;
  color: #888;
  margin: 0;
}

.logistics-popup-main {
  padding: 16px;
}

.logistics-popup-name {
  font-size: 16px;
  font-weight: 500;
  color: #4CAF50;
  margin: 0 0 8px 0;
}

.logistics-popup-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #ffd700;
  font-size: 14px;
  margin-bottom: 8px;
}

.logistics-popup-rating-count {
  color: #888;
  font-size: 12px;
}

.logistics-popup-toggle {
  width: 100%;
  padding: 12px;
  background: #2a2a2a;
  border: none;
  color: #888;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.logistics-popup-toggle:hover {
  background: #333;
}

.logistics-popup-toggle svg {
  transition: transform 0.2s;
}

.logistics-popup-toggle.expanded svg {
  transform: rotate(180deg);
}

.logistics-popup-details {
  padding: 16px;
  border-top: 1px solid #333;
  display: none;
}

.logistics-popup-details.expanded {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}

.logistics-popup-section {
  margin-bottom: 16px;
}

.logistics-popup-section-title {
  font-size: 14px;
  font-weight: 500;
  color: #4CAF50;
  margin: 0 0 8px 0;
}

.logistics-popup-info {
  font-size: 14px;
  color: #ccc;
  margin: 4px 0;
}

.logistics-popup-info a {
  color: #4CAF50;
  text-decoration: none;
}

.logistics-popup-info a:hover {
  text-decoration: underline;
}

.logistics-popup-nearby {
  margin-top: 16px;
}

.logistics-popup-nearby-item {
  padding: 8px;
  background: #2a2a2a;
  border-radius: 4px;
  margin-bottom: 8px;
}

.logistics-popup-nearby-name {
  font-weight: 500;
  color: #fff;
  margin: 0 0 4px 0;
}

.logistics-popup-nearby-info {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.logistics-popup-close {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  color: #888;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
}

.logistics-popup-close:hover {
  color: #fff;
}

.logistics-popup-type-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.logistics-popup-badge {
  display: inline-block;
  background: #222;
  color: #fff;
  border-radius: 12px;
  padding: 2px 12px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 2px;
  border: 1px solid #444;
}

.logistics-popup-badge.energy {
  background: #ffe082;
  color: #333;
  border: 1px solid #ffd54f;
}

.logistics-popup-badge.type {
  background: #4CAF50;
  color: #fff;
  border: 1px solid #388e3c;
}

.logistics-popup-grab {
  position: absolute;
  top: 12px;
  left: 12px;
  width: 20px;
  height: 20px;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none" stroke="%23888" stroke-width="2"><path d="M5 5h10M5 10h10M5 15h10"/></svg>') no-repeat center;
  cursor: grab;
}

.logistics-popup-grab:active {
  cursor: grabbing;
} 