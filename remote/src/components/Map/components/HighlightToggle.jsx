import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import styled from 'styled-components';
import { CategorySection, CategoryHeader, CategoryIcon, CategoryTitle, ToggleSwitch } from './styles/LayerToggleStyles';
import DataCard from './DataCard';
import './HighlightInfoCard.css';

const highlightSourceId = 'highlight-areas';
const highlightFillLayerId = 'highlight-areas-fill';
const highlightOutlineLayerId = 'highlight-areas-outline';
const NEIGHBORHOOD_NAMES_URL = '/data/osm/saoPao/neighborhood_names.json';

export default function HighlightToggle({ map, enabled }) {
  const [highlightEnabled, setHighlightEnabled] = useState(enabled);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const hoveredIdRef = useRef(null);
  const [popupData, setPopupData] = useState(null); // { feature, x, y }
  const [pendingPopup, setPendingPopup] = useState(null); // store pending popupData for delay
  const popupTimeout = useRef(null);
  const [neighNames, setNeighNames] = useState([]);
  const [dragPos, setDragPos] = useState(null); // {left, top}
  const dragOffset = useRef({ x: 0, y: 0 });
  const dragging = useRef(false);
  const [logisticsCount, setLogisticsCount] = useState(null);

  // Update local state when prop changes
  useEffect(() => {
    console.log('HighlightToggle: enabled prop changed:', enabled);
    setHighlightEnabled(enabled);
  }, [enabled]);

  // Handle map loading
  useEffect(() => {
    if (!map?.current) {
      console.log('HighlightToggle: Waiting for map to be available');
      return;
    }

    const mapInstance = map.current;

    // Check if map is already loaded
    if (mapInstance.loaded()) {
      setIsMapLoaded(true);
    } else {
      // Wait for map to load
      mapInstance.once('load', () => {
        console.log('HighlightToggle: Map loaded');
        setIsMapLoaded(true);
      });
    }

    return () => {
      if (mapInstance) {
        mapInstance.off('load');
      }
    };
  }, [map]);

  useEffect(() => {
    if (!isMapLoaded || !map?.current) {
      console.log('HighlightToggle: Map not ready yet');
      return;
    }

    console.log('HighlightToggle: Component mounted/updated', {
      mapExists: !!map,
      highlightEnabled,
      enabled,
      mapInstance: map ? 'exists' : 'null'
    });

    const mapInstance = map.current;

    function onMouseMove(e) {
      console.log('HighlightToggle: Mouse move event triggered', {
        features: e.features,
        point: e.point,
        lngLat: e.lngLat
      });

      if (!e.features.length) {
        console.log('HighlightToggle: Mouse moved but no features found');
        return;
      }
      const feature = e.features[0];
      console.log('HighlightToggle: Feature found', {
        id: feature.id,
        properties: feature.properties,
        geometry: feature.geometry.type
      });

      const id = feature.id || feature.properties?.id || feature.properties?.ds_codigo;
      console.log('HighlightToggle: Using feature ID:', id);
      
      if (hoveredIdRef.current !== null && hoveredIdRef.current !== id) {
        console.log('HighlightToggle: Removing hover state from previous feature:', hoveredIdRef.current);
        mapInstance.setFeatureState(
          { source: highlightSourceId, id: hoveredIdRef.current },
          { hover: false }
        );
        // Update filter for previous feature
        mapInstance.setFilter('highlight-areas-fill-hover', ['==', ['get', 'ds_codigo'], '']);
      }
      
      hoveredIdRef.current = id;
      console.log('HighlightToggle: Setting hover state for feature:', id);
      mapInstance.setFeatureState(
        { source: highlightSourceId, id },
        { hover: true }
      );
      // Update filter for current feature
      mapInstance.setFilter('highlight-areas-fill-hover', ['==', ['get', 'ds_codigo'], id]);
    }

    function onMouseLeave() {
      console.log('HighlightToggle: Mouse leave event triggered');
      if (hoveredIdRef.current !== null) {
        console.log('HighlightToggle: Removing hover state from feature:', hoveredIdRef.current);
        mapInstance.setFeatureState(
          { source: highlightSourceId, id: hoveredIdRef.current },
          { hover: false }
        );
        // Reset filter
        mapInstance.setFilter('highlight-areas-fill-hover', ['==', ['get', 'ds_codigo'], '']);
      }
      hoveredIdRef.current = null;
    }

    function onClick(e) {
      if (!e.features.length) return;
      const feature = e.features[0];
      // Get mouse position for popup
      handleShowPopup({
        feature,
        x: e.point.x,
        y: e.point.y
      });
    }

    // Cleanup function
    const cleanup = () => {
      console.log('HighlightToggle: Cleaning up');
      if (hoveredIdRef.current !== null) {
        mapInstance.setFeatureState(
          { source: highlightSourceId, id: hoveredIdRef.current },
          { hover: false }
        );
        hoveredIdRef.current = null;
      }
      mapInstance.off('mousemove', highlightFillLayerId, onMouseMove);
      mapInstance.off('mouseleave', highlightFillLayerId, onMouseLeave);
      mapInstance.off('click', highlightFillLayerId, onClick);

      // Remove layers and source if they exist
      if (mapInstance.getLayer(highlightOutlineLayerId)) {
        mapInstance.removeLayer(highlightOutlineLayerId);
      }
      if (mapInstance.getLayer('highlight-areas-fill-hover')) {
        mapInstance.removeLayer('highlight-areas-fill-hover');
      }
      if (mapInstance.getLayer(highlightFillLayerId)) {
        mapInstance.removeLayer(highlightFillLayerId);
      }
      if (mapInstance.getSource(highlightSourceId)) {
        mapInstance.removeSource(highlightSourceId);
      }
    };

    // If disabled, clean up and return
    if (!highlightEnabled) {
      cleanup();
      return;
    }

    console.log('HighlightToggle: Setting up highlight functionality');
    
    // Add source
    if (!mapInstance.getSource(highlightSourceId)) {
      console.log('HighlightToggle: Adding highlight source');
      try {
        fetch('/data/osm/saoPao/distrito_wgs84.geojson')
          .then(response => {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            console.log('HighlightToggle: Successfully loaded highlight data');
            mapInstance.addSource(highlightSourceId, {
              type: 'geojson',
              data: data
            });
            console.log('HighlightToggle: Successfully added highlight source');

            // Add default fill layer (transparent by default)
            mapInstance.addLayer({
              id: highlightFillLayerId,
              type: 'fill',
              source: highlightSourceId,
              layout: {
                visibility: 'visible'
              },
              paint: {
                'fill-color': 'rgba(0, 0, 0, 0)', // Transparent by default
                'fill-opacity': 0,
                'fill-antialias': true,
                'fill-outline-color': 'rgba(0, 0, 0, 0)'
              }
            });
            console.log('HighlightToggle: Successfully added default fill layer');

            // Add hover fill layer with filter
            mapInstance.addLayer({
              id: 'highlight-areas-fill-hover',
              type: 'fill',
              source: highlightSourceId,
              layout: {
                visibility: 'visible'
              },
              filter: ['==', ['get', 'ds_codigo'], ''],
              paint: {
                'fill-color': '#aeff00', // Green on hover
                'fill-opacity': 0.15,
                'fill-antialias': true,
                'fill-outline-color': '#00FF00'
              }
            });
            console.log('HighlightToggle: Successfully added hover fill layer');

            // Add debug logging for feature state
            mapInstance.on('mousemove', highlightFillLayerId, (e) => {
              if (e.features && e.features.length > 0) {
                const feature = e.features[0];
                const featureId = feature.properties?.ds_codigo;
                const state = mapInstance.getFeatureState({ source: highlightSourceId, id: featureId });
                console.log('HighlightToggle: Feature state on mousemove:', { 
                  featureId, 
                  state,
                  paintProperties: {
                    'fill-color': mapInstance.getPaintProperty(highlightFillLayerId, 'fill-color'),
                    'fill-opacity': mapInstance.getPaintProperty(highlightFillLayerId, 'fill-opacity')
                  }
                });
              }
            });

            // Add outline layer
            if (!mapInstance.getLayer(highlightOutlineLayerId)) {
              console.log('HighlightToggle: Adding outline layer');
              mapInstance.addLayer({
                id: highlightOutlineLayerId,
                type: 'line',
                source: highlightSourceId,
                layout: {
                  visibility: 'visible'
                },
                paint: {
                  'line-color': [
                    'case',
                    ['boolean', ['feature-state', 'hover'], false],
                    '#00FF00', // Green on hover
                    'rgba(0, 0, 0, 0)'  // Transparent when not hovered
                  ],
                  'line-width': [
                    'case',
                    ['boolean', ['feature-state', 'hover'], false],
                    2, // Normal width on hover
                    0  // No width when not hovered
                  ],
                  'line-opacity': [
                    'case',
                    ['boolean', ['feature-state', 'hover'], false],
                    1,  // Fully opaque on hover
                    0   // Transparent when not hovered
                  ]
                }
              });
              console.log('HighlightToggle: Successfully added outline layer');

              // Add event listeners
              console.log('HighlightToggle: Adding event listeners');
              mapInstance.on('mousemove', highlightFillLayerId, onMouseMove);
              mapInstance.on('mouseleave', highlightFillLayerId, onMouseLeave);
            }

            // Add event listeners
            if (highlightEnabled && isMapLoaded && mapInstance.getLayer(highlightFillLayerId)) {
              mapInstance.on('click', highlightFillLayerId, onClick);
            }
          })
          .catch(error => {
            console.error('HighlightToggle: Error loading highlight data:', error);
          });
      } catch (error) {
        console.error('HighlightToggle: Error setting up highlight:', error);
        return;
      }
    }

    return cleanup;
  }, [highlightEnabled, map, isMapLoaded]);

  useEffect(() => {
    fetch(NEIGHBORHOOD_NAMES_URL)
      .then(res => res.json())
      .then(setNeighNames)
      .catch(() => setNeighNames([]));
  }, []);

  // Mock table data for demo
  const columns = [
    'Axes', 'Inv. (sq.m)', 'Vacancy', 'Price (BRL/sq.m)', 'Net Abs. (sq.m) 2025Q1', 'Const. (sq.m)', 'Proposed (sq.m)'
  ];
  const data = [
    { 'Axes': '15km São Paulo', 'Inv. (sq.m)': '539,205', 'Vacancy': '1.29%', 'Price (BRL/sq.m)': '39.39', 'Net Abs. (sq.m) 2025Q1': '38,849', 'Const. (sq.m)': '289,296', 'Proposed (sq.m)': '498,384' },
    { 'Axes': '30km Anchieta-Imigrantes', 'Inv. (sq.m)': '617,533', 'Vacancy': '22.62%', 'Price (BRL/sq.m)': '29.82', 'Net Abs. (sq.m) 2025Q1': '0', 'Const. (sq.m)': '0', 'Proposed (sq.m)': '0' },
    { 'Axes': '30km Anhanguera-Bandeirantes', 'Inv. (sq.m)': '2,671,884', 'Vacancy': '17.09%', 'Price (BRL/sq.m)': '27.35', 'Net Abs. (sq.m) 2025Q1': '0', 'Const. (sq.m)': '315,225', 'Proposed (sq.m)': '1,108,104' },
    { 'Axes': '30km Castelo Branco', 'Inv. (sq.m)': '955,269', 'Vacancy': '3.71%', 'Price (BRL/sq.m)': '25.00', 'Net Abs. (sq.m) 2025Q1': '1,638', 'Const. (sq.m)': '0', 'Proposed (sq.m)': '130,117' },
    { 'Axes': '30km Dutra-Senna', 'Inv. (sq.m)': '2,201,679', 'Vacancy': '14.47%', 'Price (BRL/sq.m)': '31.20', 'Net Abs. (sq.m) 2025Q1': '-1,215', 'Const. (sq.m)': '349,154', 'Proposed (sq.m)': '148,983' },
    { 'Axes': '30km Raposo-Régis', 'Inv. (sq.m)': '834,298', 'Vacancy': '26.78%', 'Price (BRL/sq.m)': '30.47', 'Net Abs. (sq.m) 2025Q1': '0', 'Const. (sq.m)': '0', 'Proposed (sq.m)': '338,913' },
    { 'Axes': '70km Anhanguera-Bandeirantes', 'Inv. (sq.m)': '2,529,116', 'Vacancy': '9.22%', 'Price (BRL/sq.m)': '22.68', 'Net Abs. (sq.m) 2025Q1': '-1,070', 'Const. (sq.m)': '52,570', 'Proposed (sq.m)': '345,841' },
    { 'Axes': '70km Castelo-Régis', 'Inv. (sq.m)': '809,811', 'Vacancy': '9.19%', 'Price (BRL/sq.m)': '23.93', 'Net Abs. (sq.m) 2025Q1': '0', 'Const. (sq.m)': '93,483', 'Proposed (sq.m)': '0' },
    { 'Axes': '70km Fernão Dias', 'Inv. (sq.m)': '446,838', 'Vacancy': '1.40%', 'Price (BRL/sq.m)': '20.78', 'Net Abs. (sq.m) 2025Q1': '0', 'Const. (sq.m)': '0', 'Proposed (sq.m)': '807,630' },
    { 'Axes': '150km Campinas-Sorocaba', 'Inv. (sq.m)': '1,770,730', 'Vacancy': '8.78%', 'Price (BRL/sq.m)': '20.19', 'Net Abs. (sq.m) 2025Q1': '-10,285', 'Const. (sq.m)': '170,633', 'Proposed (sq.m)': '2,790,394' },
    { 'Axes': '150km Dutra-Senna', 'Inv. (sq.m)': '614,439', 'Vacancy': '10.17%', 'Price (BRL/sq.m)': '25.39', 'Net Abs. (sq.m) 2025Q1': '-8,152', 'Const. (sq.m)': '0', 'Proposed (sq.m)': '28,569' },
    { 'Axes': '150km Extrema', 'Inv. (sq.m)': '1,166,032', 'Vacancy': '13.64%', 'Price (BRL/sq.m)': '28.36', 'Net Abs. (sq.m) 2025Q1': '75,377', 'Const. (sq.m)': '174,823', 'Proposed (sq.m)': '232,008' },
  ];

  // Drag handlers
  const onDragStart = (e) => {
    dragging.current = true;
    dragOffset.current = {
      x: e.clientX - (dragPos ? dragPos.left : e.target.parentElement.offsetLeft),
      y: e.clientY - (dragPos ? dragPos.top : e.target.parentElement.offsetTop),
    };
    document.addEventListener('mousemove', onDrag);
    document.addEventListener('mouseup', onDragEnd);
  };
  const onDrag = (e) => {
    if (!dragging.current) return;
    setDragPos({
      left: e.clientX - dragOffset.current.x,
      top: e.clientY - dragOffset.current.y,
    });
  };
  const onDragEnd = () => {
    dragging.current = false;
    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('mouseup', onDragEnd);
  };

  // Set dragPos to initial position when popupData is set (first click)
  useEffect(() => {
    if (popupData && dragPos == null) {
      setDragPos({ left: popupData.x + 20, top: popupData.y + 20 });
    }
  }, [popupData]);

  // When user clicks, set pendingPopup and start a 1s timer
  const handleShowPopup = (data) => {
    setPendingPopup(data);
    if (popupTimeout.current) clearTimeout(popupTimeout.current);
    popupTimeout.current = setTimeout(() => {
      setPopupData(data);
      setPendingPopup(null);
    }, 1000);
  };

  // When closing, clear timeout and popupData
  const handleClosePopup = () => {
    if (popupTimeout.current) clearTimeout(popupTimeout.current);
    setPopupData(null);
    setPendingPopup(null);
    setDragPos(null);
  };

  // Instead of fetching, generate a random logistics count on each popup
  useEffect(() => {
    if (pendingPopup || popupData) {
      setLogisticsCount(Math.floor(Math.random() * (400 - 10 + 1)) + 10);
    } else {
      setLogisticsCount(null);
    }
  }, [pendingPopup, popupData]);

  return (
    <>
      <CategorySection>
        <CategoryHeader>
          <CategoryIcon>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
            </svg>
          </CategoryIcon>
          <CategoryTitle>Highlight Areas</CategoryTitle>
          <ToggleSwitch>
            <input
              type="checkbox"
              checked={highlightEnabled}
              onChange={() => setHighlightEnabled(!highlightEnabled)}
            />
            <span></span>
          </ToggleSwitch>
        </CategoryHeader>
      </CategorySection>
      {((pendingPopup || popupData) && (
        (() => {
          // Find the map container
          const mapContainer = map?.current?.getContainer ? map.current.getContainer() : null;
          let left = dragPos ? dragPos.left : (pendingPopup ? pendingPopup.x + 20 : popupData.x + 20);
          let top = dragPos ? dragPos.top : (pendingPopup ? pendingPopup.y + 20 : popupData.y + 20);
          if (mapContainer) {
            left = Math.max(left, 10);
            top = Math.max(top, 10);
            // Get the neighborhood name from the feature's properties
            const feature = pendingPopup ? pendingPopup.feature : popupData.feature;
            const neighborhoodName = feature.properties?.name || feature.properties?.ds_nome || 'Neighborhood';
            const infoFields = [
              'Inv. (sq.m)',
              'Vacancy',
              'Price (BRL/sq.m)',
              'Net Abs. (sq.m) 2025Q1',
              'Const. (sq.m)',
              'Proposed (sq.m)'
            ];
            // Generate random values for each field on every click
            function randomInt(min, max) { return Math.floor(Math.random() * (max - min + 1)) + min; }
            function randomFloat(min, max, decimals = 2) { return (Math.random() * (max - min) + min).toFixed(decimals); }
            function randomPercent(min, max) { return randomFloat(min, max, 2) + '%'; }
            const info = {
              'Inv. (sq.m)': randomInt(400000, 3000000).toLocaleString(),
              'Vacancy': randomPercent(1, 30),
              'Price (BRL/sq.m)': randomFloat(18, 45, 2),
              'Net Abs. (sq.m) 2025Q1': randomInt(-12000, 80000).toLocaleString(),
              'Const. (sq.m)': randomInt(0, 350000).toLocaleString(),
              'Proposed (sq.m)': randomInt(0, 3000000).toLocaleString(),
            };
            return (
              <div className="highlight-info-card-popup" style={{ position: 'absolute', left, top, zIndex: 2000, cursor: dragging.current ? 'grabbing' : 'default' }}>
                <div className="highlight-info-card">
                  <div
                    className="highlight-info-drag-handle"
                    onMouseDown={onDragStart}
                    title="Drag card"
                  >
                    <svg className="highlight-info-drag-icon" viewBox="0 0 20 20">
                      <circle cx="5" cy="6" r="1.5" />
                      <circle cx="10" cy="6" r="1.5" />
                      <circle cx="15" cy="6" r="1.5" />
                      <circle cx="5" cy="12" r="1.5" />
                      <circle cx="10" cy="12" r="1.5" />
                      <circle cx="15" cy="12" r="1.5" />
                    </svg>
                  </div>
                  {/* Logistics badge */}
                  {logisticsCount !== null && (
                    <div style={{
                      fontSize: '0.72rem',
                      color: '#bfc2c7',
                      marginBottom: '-22px',
                      fontWeight: 500,
                      letterSpacing: '0.01em',
                      textAlign: 'left',
                      marginLeft: 2,
                      marginTop: '6px',
                    }}>
                      {logisticsCount} Logistics Assets
                    </div>
                  )}
                  <div className="highlight-info-title">{neighborhoodName}</div>
                  {pendingPopup ? (
                    <div className="highlight-info-skeleton">
                      {infoFields.map((_, i) => (
                        <div className="highlight-info-skeleton-row" key={i} />
                      ))}
                    </div>
                  ) : (
                    infoFields.map(label => (
                      <div className="highlight-info-row" key={label}>
                        <span className="highlight-info-label">{label}</span>
                        <span className="highlight-info-value">{info[label]}</span>
                      </div>
                    ))
                  )}
                </div>
                <button className="highlight-info-close" onClick={handleClosePopup}>Close</button>
              </div>
            );
          }
          return null;
        })()
      ))}
    </>
  );
} 