import React, { useState, useEffect } from 'react';
import mapboxgl from 'mapbox-gl';
import styled from 'styled-components';
import { osmLayerIds, loadOSMData, toggleOSMLayer } from '../utils/osmLayers';
import * as turf from '@turf/turf';
import NeighborhoodBoundaries from './NeighborhoodBoundaries';
import PlanningAnalysisLayer from './PlanningAnalysisLayer';
import PropertyPricesLayer from './PropertyPricesLayer';
import EmploymentLayer from './EmploymentLayer';
import { SidePopup } from './SidePopup';
import { LayerIcons } from './icons/LayerIcons';
import { useLayerToggles } from '../hooks/useLayerToggles';
import { use3DBuildings } from '../hooks/use3DBuildings';
import { NeighborhoodPopup } from './NeighborhoodPopup';
import { COLORS, TRANSPORTATION_CATEGORIES } from '../constants/layerConstants';
import {
  LayerToggleContainer,
  LayerHeader,
  Title,
  CollapseButton,
  ExpandButton,
  SearchInput,
  CategorySection,
  CategoryHeader,
  CategoryIcon,
  CategoryTitle,
  ToggleSwitch,
  SubLayerContainer,
  SubLayer
} from './styles/LayerToggleStyles';
import SceneManager from './SceneManager';
import { handleNeighborhoodSelection } from '../../../services/claude';
import { CONCENTRIC_CIRCLES_IDS, CONCENTRIC_CIRCLES, CONCENTRIC_CIRCLES_SOURCES, SAO_PAULO_CENTER } from '../constants/mapConstants';
import { circle } from '@turf/turf';
import SchoolMarkersLayer from './SchoolMarkersLayer';
import SchoolLegend from './SchoolLegend';
import DistritoLayer from './DistritoLayer';
import POIDensityLayer from './POIDensityLayer';
import LogisticsMarkersLayer from './LogisticsMarkersLayer';
import PopulationDensityLayer from './PopulationDensityLayer';
import DemographicDensityLayer from './DemographicDensityLayer';
import DistrictsLayer from './DistrictsLayer';
import HoverToggle from './HoverToggle';
import HighlightToggle from './HighlightToggle';

// Array of Mapbox green spaces layers
const parkLayers = [
  'park',
  'park-label',
  'national-park',
  'golf-course',
  'pitch',
  'grass'
];

// More specific filter for natural areas that are actually parks
const naturalParkFilter = [
  'any',
  ['==', ['get', 'class'], 'park'],
  ['==', ['get', 'class'], 'garden'],
  ['==', ['get', 'class'], 'forest'],
  ['==', ['get', 'class'], 'wood']
];

const LayerToggle = ({
  map,
  isLayerMenuCollapsed,
  setIsLayerMenuCollapsed,
  showZoningLayer,
  setShowZoningLayer,
  showPlanningAnalysis,
  setShowPlanningAnalysis,
  showAdaptiveReuse,
  setShowAdaptiveReuse,
  showDevelopmentPotential,
  setShowDevelopmentPotential,
  showTransportation = false,
  setShowTransportation,
  showRoads,
  setShowRoads,
  showNeighborhoodBoundaries,
  setShowNeighborhoodBoundaries,
  showPropertyPrices,
  setShowPropertyPrices,
  showEmployment = false,
  setShowEmployment,
  showParks = false,
  setShowParks,
  showNeighborhoodLabels = false,
  setShowNeighborhoodLabels,
  showEmploymentLabels = false,
  setShowEmploymentLabels,
  showLocalZones = false,
  setShowLocalZones,
  showLocalZoneBoundaries = false,
  setShowLocalZoneBoundaries,
  showLocalZoneLabels = false,
  setShowLocalZoneLabels,
  showConcentricCircles = false,
  setShowConcentricCircles,
  showRoadParticles = false,
  setShowRoadParticles
}) => {
  const [selectedNeighborhood, setSelectedNeighborhood] = useState(null);
  const [neighborhoodMarkers, setNeighborhoodMarkers] = useState(null);
  const [planningData, setPlanningData] = useState(null);
  const [isSceneSidebarOpen, setIsSceneSidebarOpen] = useState(false);
  const [showNeighborhoods, setShowNeighborhoods] = useState(false);
  const [schoolMarkers, setSchoolMarkers] = useState([]);
  const [buildingsByNeighborhood, setBuildingsByNeighborhood] = useState(null);
  const [hoveredNeighborhood, setHoveredNeighborhood] = useState(null);
  const [showDistricts, setShowDistricts] = useState(false);
  const [showPOIDensity, setShowPOIDensity] = useState(false);
  const [logisticsMarkersVisible, setLogisticsMarkersVisible] = useState(false);
  const [showDemographicDensity, setShowDemographicDensity] = useState(false);
  const [showNeighborhoodHover, setShowNeighborhoodHover] = useState(false);
  const [showHighlight, setShowHighlight] = useState(false);

  const {
    showOSMTransit,
    showOSMBike,
    showOSMPedestrian,
    expandedCategories,
    is3DLoading,
    toggleCategory,
    handleToggle,
    handleOSMTransitToggle,
    handleOSMBikeToggle,
    handleOSMPedestrianToggle,
    setIs3DLoading,
    showTransitStops,
    showTransitRoutes,
    showBikeLanes,
    showBikePaths,
    showBikeParking,
    showPedestrianPaths,
    showPedestrianCrossings,
    setShowTransitStops,
    setShowTransitRoutes,
    setShowBikeLanes,
    setShowBikePaths,
    setShowBikeParking,
    setShowPedestrianPaths,
    setShowPedestrianCrossings,
    showSchoolMarkers,
    setShowSchoolMarkers,
    show3DBuildingsOSM,
    setShow3DBuildingsOSM
  } = useLayerToggles(map);

  const {
    show3DBuildings,
    toggle3D,
    reset3DBuildings
  } = use3DBuildings(map);

  const handlePlanningDataLoaded = (data) => {
    setPlanningData(data);
  };

  // Function to toggle visibility and styling of park layers
  const toggleParkLayers = (visible) => {
    if (!map.current) return;
    
    parkLayers.forEach(layerId => {
      if (map.current.getLayer(layerId)) {
        try {
          // Set visibility
          map.current.setLayoutProperty(
            layerId,
            'visibility',
            visible ? 'visible' : 'none'
          );
          
          // Set color for fill layers
          const layer = map.current.getLayer(layerId);
          if (layer && layer.type === 'fill') {
            map.current.setPaintProperty(
              layerId, 
              'fill-color', 
              visible ? '#2a9d2a' : '#050f08'
            );
            map.current.setPaintProperty(
              layerId, 
              'fill-opacity', 
              visible ? 0.45 : 0.3
            );
          }
          
          // Handle symbol layers with background color
          if (layer && layer.type === 'symbol' && 
              map.current.getPaintProperty(layerId, 'background-color') !== undefined) {
            map.current.setPaintProperty(
              layerId, 
              'background-color', 
              visible ? '#2a9d2a' : '#050f08'
            );
          }
        } catch (error) {
          console.warn(`Could not style park layer ${layerId}:`, error);
        }
      }
    });
    
    // Apply filter to the 'natural' layer if it exists to only show park-like natural areas
    if (map.current.getLayer('natural')) {
      try {
        if (visible) {
          // Store the original filter if we haven't stored it yet
          if (!map.current._originalNaturalFilter) {
            map.current._originalNaturalFilter = map.current.getFilter('natural') || ['all'];
          }
          
          // Apply our custom filter for natural areas that are parks
          map.current.setFilter('natural', ['all', 
            map.current._originalNaturalFilter,
            naturalParkFilter
          ]);
          
          // Set visibility and style
          map.current.setLayoutProperty('natural', 'visibility', 'visible');
          map.current.setPaintProperty('natural', 'fill-color', '#2a9d2a');
          map.current.setPaintProperty('natural', 'fill-opacity', 0.45);
        } else {
          // Restore original filter and style
          if (map.current._originalNaturalFilter) {
            map.current.setFilter('natural', map.current._originalNaturalFilter);
          }
          map.current.setLayoutProperty('natural', 'visibility', 'none');
          map.current.setPaintProperty('natural', 'fill-color', '#050f08');
          map.current.setPaintProperty('natural', 'fill-opacity', 0.3);
        }
      } catch (error) {
        console.warn('Could not filter natural layer:', error);
      }
    }
  };

  useEffect(() => {
    if (map.current) {
      loadOSMData(map.current, 'austin');
    }
  }, [map]);

  // Effect to handle park layers visibility when showParks changes
  useEffect(() => {
    toggleParkLayers(showParks);
  }, [showParks]);

  // Add this function to handle concentric circles toggle
  const handleConcentricCirclesToggle = (checked) => {
    if (!map.current) return;
    
    // Create circles if they don't exist
    Object.entries(CONCENTRIC_CIRCLES).forEach(([key, radius]) => {
      const sourceId = CONCENTRIC_CIRCLES_SOURCES[key];
      const layerId = CONCENTRIC_CIRCLES_IDS[key];
      const fillLayerId = `${layerId}-fill`;

      // Create a circle geometry - convert feet to kilometers for turf.js
      const radiusInKm = radius / 3280.84;  // Convert feet to kilometers (1 km = 3280.84 feet)
      const circleGeometry = circle(SAO_PAULO_CENTER, radiusInKm, {
        steps: 64,
        units: 'kilometers'
      });

      // Add the source if it doesn't exist
      if (!map.current.getSource(sourceId)) {
        map.current.addSource(sourceId, {
          type: 'geojson',
          data: circleGeometry
        });
      }

      // Add the fill layer if it doesn't exist
      if (!map.current.getLayer(fillLayerId)) {
        map.current.addLayer({
          id: fillLayerId,
          type: 'fill',
          source: sourceId,
          layout: {
            visibility: checked ? 'visible' : 'none'
          },
          paint: {
            'fill-color': '#FFEB3B',
            'fill-opacity': key === 'INNER' ? 0.1 : key === 'MIDDLE' ? 0.05 : 0.01
          }
        });
      } else {
        // Toggle visibility of existing fill layer
        map.current.setLayoutProperty(fillLayerId, 'visibility', checked ? 'visible' : 'none');
      }

      // Add the line layer if it doesn't exist
      if (!map.current.getLayer(layerId)) {
        map.current.addLayer({
          id: layerId,
          type: 'line',
          source: sourceId,
          layout: {
            visibility: checked ? 'visible' : 'none'
          },
          paint: {
            'line-color': '#0080ff',
            'line-width': 2.5,
            'line-opacity': 0.5,
            'line-dasharray': [2, 2]
          }
        });
      } else {
        // Toggle visibility of existing line layer
        map.current.setLayoutProperty(layerId, 'visibility', checked ? 'visible' : 'none');
      }
    });
    
    // Move all concentric circle layers to the top
    if (checked) {
      Object.values(CONCENTRIC_CIRCLES_IDS).forEach(layerId => {
        try {
          map.current.moveLayer(layerId);
          map.current.moveLayer(`${layerId}-fill`);
        } catch (error) {}
      });
    }
    
    setShowConcentricCircles(checked);
  };

  // Load buildings data on mount
  useEffect(() => {
    fetch('/data/osm/buildings_by_neighborhood.geojson')
      .then(res => res.json())
      .then(data => setBuildingsByNeighborhood(data))
      .catch(err => console.error('Error loading buildings data:', err));
  }, []);

  // Add console log for demographic density toggle
  useEffect(() => {
    console.log('Demographic density layer visibility changed:', showDemographicDensity);
  }, [showDemographicDensity]);

  return (
    <>
      <LayerToggleContainer $isCollapsed={isLayerMenuCollapsed}>
        <LayerHeader>
          <Title>Map Layers</Title>
          <CollapseButton
            onClick={() => setIsLayerMenuCollapsed(!isLayerMenuCollapsed)}
            $isCollapsed={isLayerMenuCollapsed}
          >
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.41 7.41L10.83 12l4.58 4.59L14 18l-6-6 6-6 1.41 1.41z"/>
            </svg>
          </CollapseButton>
        </LayerHeader>

        {/* Scenes Section */}
        <CategorySection>
          <CategoryHeader 
            onClick={() => setIsSceneSidebarOpen(true)}
            style={{ background: 'rgba(59, 130, 246, 0.2)', borderColor: 'rgba(59, 130, 246, 0.2)' }}
          >
            <CategoryIcon>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" width="24" height="24">
                <path d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h-4.5m-9 0H5a2 2 0 01-2-2V7a2 2 0 012-2h1.5m9 0h4.5a2 2 0 012 2v.5M9 7h1m5 0h1M9 11h1m5 0h1M9 15h1m5 0h1M9 19h1m5 0h1" />
              </svg>
            </CategoryIcon>
            <CategoryTitle>Saved Scenes</CategoryTitle>
            <div style={{ marginLeft: 'auto' }}>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" width="20" height="20">
                <path d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </CategoryHeader>
        </CategorySection>

        {/* Parks Section */}
        <CategorySection>
          <CategoryHeader>
            <CategoryIcon><LayerIcons.Nature /></CategoryIcon>
            <CategoryTitle>Parks</CategoryTitle>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={showParks}
                onChange={() => setShowParks(!showParks)}
              />
              <span></span>
            </ToggleSwitch>
          </CategoryHeader>
        </CategorySection>

        {/* Concentric Circles Section */}
        <CategorySection>
          <CategoryHeader>
            <CategoryIcon>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" width="24" height="24">
                <circle cx="12" cy="12" r="10" />
                <circle cx="12" cy="12" r="6" />
                <circle cx="12" cy="12" r="2" />
              </svg>
            </CategoryIcon>
            <CategoryTitle>Distance Rings</CategoryTitle>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={showConcentricCircles}
                onChange={(e) => handleConcentricCirclesToggle(e.target.checked)}
              />
              <span></span>
            </ToggleSwitch>
          </CategoryHeader>
        </CategorySection>

        {/* Road Particles Section */}
        <CategorySection>
          <CategoryHeader>
            <CategoryIcon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="2" />
                <circle cx="12" cy="12" r="4" />
                <circle cx="12" cy="12" r="6" />
              </svg>
            </CategoryIcon>
            <CategoryTitle>Road Flow Animation</CategoryTitle>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={showRoadParticles}
                onChange={e => setShowRoadParticles(e.target.checked)}
              />
              <span></span>
            </ToggleSwitch>
          </CategoryHeader>
        </CategorySection>

        {/* POI Density Section - Commented out but functionality preserved
        <CategorySection>
          <CategoryHeader>
            <CategoryIcon>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" width="24" height="24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                <path d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z"/>
              </svg>
            </CategoryIcon>
            <CategoryTitle>POI Density</CategoryTitle>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={showPOIDensity}
                onChange={(e) => setShowPOIDensity(e.target.checked)}
              />
              <span></span>
            </ToggleSwitch>
          </CategoryHeader>
        </CategorySection>
        */}

        {/* Logistics Section */}
        <CategorySection>
          <CategoryHeader>
            <CategoryIcon>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" width="24" height="24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
              </svg>
            </CategoryIcon>
            <CategoryTitle>Logistics</CategoryTitle>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={logisticsMarkersVisible}
                onChange={(e) => setLogisticsMarkersVisible(e.target.checked)}
              />
              <span></span>
            </ToggleSwitch>
          </CategoryHeader>
        </CategorySection>

        {/* Demographic Density Section */}
        <CategorySection>
          <CategoryHeader>
            <CategoryIcon>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" width="24" height="24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>
                <path d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z"/>
              </svg>
            </CategoryIcon>
            <CategoryTitle>Demographic Density</CategoryTitle>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={showDemographicDensity}
                onChange={(e) => {
                  console.log('Demographic density toggle clicked:', e.target.checked);
                  setShowDemographicDensity(e.target.checked);
                }}
              />
              <span></span>
            </ToggleSwitch>
          </CategoryHeader>
        </CategorySection>

        {/* Districts Section */}
        <CategorySection>
          <CategoryHeader>
            <CategoryIcon>
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" width="24" height="24">
                <path d="M3 3h18v18H3z"/>
                <path d="M3 9h18M3 15h18M9 3v18M15 3v18"/>
              </svg>
            </CategoryIcon>
            <CategoryTitle>Districts</CategoryTitle>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={showDistricts}
                onChange={(e) => {
                  console.log('Districts layer toggle clicked:', e.target.checked);
                  setShowDistricts(e.target.checked);
                }}
              />
              <span></span>
            </ToggleSwitch>
          </CategoryHeader>
        </CategorySection>

        {/* Neighborhood Hover Section */}
        <CategorySection>
          <CategoryHeader>
            <CategoryIcon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="4" y="4" width="16" height="16" rx="2" />
                <path d="M8 8h8v8H8z" />
              </svg>
            </CategoryIcon>
            <CategoryTitle>Neighborhood Hover/3D</CategoryTitle>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={showNeighborhoodHover}
                onChange={() => {
                  console.log('LayerToggle: Neighborhood hover toggle clicked, current state:', showNeighborhoodHover);
                  setShowNeighborhoodHover(!showNeighborhoodHover);
                }}
              />
              <span></span>
            </ToggleSwitch>
          </CategoryHeader>
        </CategorySection>

        {/* Highlight Section */}
        <CategorySection>
          <CategoryHeader>
            <CategoryIcon>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
              </svg>
            </CategoryIcon>
            <CategoryTitle>Highlight Areas</CategoryTitle>
            <ToggleSwitch>
              <input
                type="checkbox"
                checked={showHighlight}
                onChange={() => {
                  console.log('LayerToggle: Highlight toggle clicked, current state:', showHighlight);
                  setShowHighlight(!showHighlight);
                }}
              />
              <span></span>
            </ToggleSwitch>
          </CategoryHeader>
        </CategorySection>
      </LayerToggleContainer>

      <ExpandButton
        onClick={() => setIsLayerMenuCollapsed(false)}
        $isCollapsed={isLayerMenuCollapsed}
        title="Expand layer menu"
      >
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M15.41 7.41L10.83 12l4.58 4.59L14 18l-6-6 6-6 1.41 1.41z"/>
        </svg>
      </ExpandButton>

      <SceneManager
        map={map.current}
        layerStates={{
          showParks,
          showPOIDensity
        }}
        onLoadScene={(sceneLayerStates) => {
          if (sceneLayerStates.showParks !== undefined) setShowParks(sceneLayerStates.showParks);
          if (sceneLayerStates.showPOIDensity !== undefined) setShowPOIDensity(sceneLayerStates.showPOIDensity);
        }}
        isOpen={isSceneSidebarOpen}
        onClose={() => setIsSceneSidebarOpen(false)}
      />

      <POIDensityLayer map={map} visible={showPOIDensity} />

      <LogisticsMarkersLayer map={map} visible={logisticsMarkersVisible} />

      <DemographicDensityLayer map={map} visible={showDemographicDensity} />

      <DistrictsLayer map={map} visible={showDistricts} />

      <HoverToggle map={map} enabled={showNeighborhoodHover} />
      <HighlightToggle map={map} enabled={showHighlight} />
    </>
  );
};

export default LayerToggle; 