import React from 'react';

const categoryColors = {
  elementary: '#60a5fa', // light blue
  middle: '#fbbf24',    // amber/gold
  high: '#f87171',      // red
  academy: '#a78bfa',   // purple
  charter: '#34d399',   // green
  university: '#f472b6',// pink
  college: '#38bdf8',   // cyan
  kindergarten: '#facc15', // yellow
  default: '#d1d5db'    // gray
};

const categoryLabels = {
  elementary: 'Elementary',
  middle: 'Middle',
  high: 'High',
  academy: 'Academy',
  charter: 'Charter',
  university: 'University',
  college: 'College',
  kindergarten: 'Kindergarten',
  default: 'Other'
};

export default function SchoolLegend({ visible }) {
  if (!visible) return null;
  return (
    <div style={{
      position: 'fixed',
      right: 18,
      bottom: 18,
      background: '#18181b',
      color: '#fff',
      borderRadius: 10,
      boxShadow: '0 2px 16px rgba(0,0,0,0.35)',
      padding: '10px 16px 8px 16px',
      fontSize: 13,
      zIndex: 10000,
      minWidth: 120,
      maxWidth: 180,
      pointerEvents: 'auto',
      opacity: 0.95
    }}>
      <div style={{fontWeight: 700, fontSize: 14, marginBottom: 6, letterSpacing: 0.2}}>School Types</div>
      {Object.keys(categoryColors).filter(key => key !== 'default').map(key => (
        <div key={key} style={{ display: 'flex', alignItems: 'center', marginBottom: 3 }}>
          <span style={{
            display: 'inline-block',
            width: 11,
            height: 11,
            borderRadius: '50%',
            background: '#18181b',
            border: `1.5px solid ${categoryColors[key]}`,
            marginRight: 8,
            boxSizing: 'border-box',
          }}>
            <svg width="7" height="7" viewBox="0 0 24 24" fill="none" stroke={categoryColors[key]} strokeWidth="2" style={{verticalAlign:'middle',marginLeft:2,marginTop:2}}><path d="M12 3L2 9l10 6 10-6-10-6z"/><path d="M2 9v6a2 2 0 0 0 2 2h2v-4h8v4h2a2 2 0 0 0 2-2V9"/></svg>
          </span>
          <span style={{color: categoryColors[key], fontWeight: 500, fontSize: 13}}>{categoryLabels[key]}</span>
        </div>
      ))}
    </div>
  );
} 