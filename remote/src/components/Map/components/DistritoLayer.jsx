import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';

const DistritoLayer = ({ map, visible }) => {
  const layerLoaded = useRef(false);
  const popupRef = useRef(null);
  const dataRef = useRef(null);
  const styleLoadHandler = useRef(null);

  useEffect(() => {
    if (!map?.current) return;

    const loadDistritoLayer = async () => {
      try {
        // Only fetch data if we haven't already
        if (!dataRef.current) {
          const response = await fetch('/data/osm/saoPao/distrito_wgs84.geojson');
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
          dataRef.current = await response.json();
        }

        // Add or update source
        if (map.current.getSource('distrito')) {
          map.current.removeSource('distrito');
        }
        map.current.addSource('distrito', {
          type: 'geojson',
          data: dataRef.current
        });

        // Add or update layer
        if (map.current.getLayer('distrito-fill')) {
          map.current.removeLayer('distrito-fill');
        }
        map.current.addLayer({
          id: 'distrito-fill',
          type: 'fill',
          source: 'distrito',
          paint: {
            'fill-color': '#ff0000',
            'fill-opacity': 0.3,
            'fill-outline-color': '#ffffff'
          },
          layout: {
            visibility: visible ? 'visible' : 'none'
          }
        });

        // Create popup if it doesn't exist
        if (!popupRef.current) {
          popupRef.current = new mapboxgl.Popup({
            closeButton: false,
            closeOnClick: false
          });
        }

        // Add event handlers if not already added
        if (!layerLoaded.current) {
          map.current.on('mousemove', 'distrito-fill', (e) => {
            if (e.features.length > 0) {
              map.current.getCanvas().style.cursor = 'pointer';
              const properties = e.features[0].properties;
              console.log('Hovering over distrito:', {
                id: properties.distrito_id,
                name: properties.distrito_nome,
                area: properties.distrito_area,
                population: properties.distrito_populacao
              });
            }
          });

          map.current.on('mouseleave', 'distrito-fill', () => {
            map.current.getCanvas().style.cursor = '';
          });

          map.current.on('click', 'distrito-fill', (e) => {
            const coordinates = e.lngLat;
            const properties = e.features[0].properties;

            const popupContent = `
              <div style="padding: 10px;">
                <h3 style="margin: 0 0 10px 0;">District Information</h3>
                <p style="margin: 0 0 5px 0;"><strong>Name:</strong> ${properties.distrito_nome}</p>
                <p style="margin: 0 0 5px 0;"><strong>Area:</strong> ${properties.distrito_area.toFixed(2)} m²</p>
                <p style="margin: 0 0 5px 0;"><strong>Population:</strong> ${properties.distrito_populacao.toLocaleString()} inhabitants</p>
              </div>
            `;

            popupRef.current
              .setLngLat(coordinates)
              .setHTML(popupContent)
              .addTo(map.current);
          });

          layerLoaded.current = true;
        }

        // Update visibility
        map.current.setLayoutProperty('distrito-fill', 'visibility', visible ? 'visible' : 'none');
        if (visible) {
          map.current.moveLayer('distrito-fill');
        }

      } catch (error) {
        console.error('Error in distrito layer:', error);
      }
    };

    const initializeLayer = () => {
      if (map.current.isStyleLoaded()) {
        loadDistritoLayer();
      } else {
        // Store the handler reference so we can remove it later
        styleLoadHandler.current = () => {
          loadDistritoLayer();
          map.current.off('style.load', styleLoadHandler.current);
        };
        map.current.on('style.load', styleLoadHandler.current);
      }
    };

    // Initialize layer if map is ready
    if (map.current.loaded()) {
      initializeLayer();
    } else {
      const onMapLoad = () => {
        initializeLayer();
        map.current.off('load', onMapLoad);
      };
      map.current.on('load', onMapLoad);
    }

    // Cleanup
    return () => {
      if (map.current) {
        map.current.off('click', 'distrito-fill');
        map.current.off('mousemove', 'distrito-fill');
        map.current.off('mouseleave', 'distrito-fill');
        if (styleLoadHandler.current) {
          map.current.off('style.load', styleLoadHandler.current);
        }
        
        if (popupRef.current) {
          popupRef.current.remove();
        }
        
        if (map.current.getLayer('distrito-fill')) {
          map.current.removeLayer('distrito-fill');
        }
        if (map.current.getSource('distrito')) {
          map.current.removeSource('distrito');
        }
      }
    };
  }, [map, visible]);

  return null;
};

export default DistritoLayer; 