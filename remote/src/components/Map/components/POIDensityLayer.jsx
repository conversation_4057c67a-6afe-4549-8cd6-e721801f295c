import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';

const POIDensityLayer = ({ map, visible }) => {
  const layerLoaded = useRef(false);

  useEffect(() => {
    console.log('POIDensityLayer effect triggered:', { visible, mapLoaded: map?.current?.loaded() });
    
    if (!map || !map.current) {
      console.log('Map not available yet');
      return;
    }

    const loadPOIDensityLayer = async () => {
      console.log('Starting to load POI density layer...');
      try {
        // Wait for map style to be loaded
        if (!map.current.isStyleLoaded()) {
          console.log('Waiting for map style to load...');
          await new Promise(resolve => map.current.once('style.load', resolve));
        }

        // Add source
        console.log('Adding POI density source...');
        if (map.current.getSource('poi-density')) {
          map.current.removeSource('poi-density');
        }
        map.current.addSource('poi-density', {
          type: 'geojson',
          data: '/data/osm/saoPao/poi_density.json'
        });

        // Add fill layer
        console.log('Adding POI density fill layer...');
        if (map.current.getLayer('poi-density-fill')) {
          map.current.removeLayer('poi-density-fill');
        }
        map.current.addLayer({
          id: 'poi-density-fill',
          type: 'fill',
          source: 'poi-density',
          paint: {
            'fill-color': [
              'interpolate',
              ['linear'],
              ['get', 'poiCount'],
              0, '#ffffff',
              1, '#ffebee',
              10, '#ffcdd2',
              50, '#ef9a9a',
              100, '#e57373',
              200, '#ef5350',
              300, '#f44336',
              400, '#e53935',
              500, '#d32f2f',
              1000, '#c62828',
              2000, '#b71c1c'
            ],
            'fill-opacity': [
              'case',
              ['==', ['get', 'poiCount'], 0],
              0,
              0.5
            ]
          },
          layout: {
            visibility: visible ? 'visible' : 'none'
          }
        });

        // Add hover effect
        console.log('Setting up POI density hover effects...');
        map.current.on('mousemove', 'poi-density-fill', (e) => {
          if (e.features.length > 0) {
            map.current.getCanvas().style.cursor = 'pointer';
            const poiCount = e.features[0].properties.poiCount;
            console.log(`POI Count: ${poiCount}`);
          }
        });

        map.current.on('mouseleave', 'poi-density-fill', () => {
          map.current.getCanvas().style.cursor = '';
        });

        // Log layer state
        console.log('Layer state after setup:', {
          sourceExists: !!map.current.getSource('poi-density'),
          layerExists: !!map.current.getLayer('poi-density-fill'),
          layerVisibility: map.current.getLayoutProperty('poi-density-fill', 'visibility'),
          styleLoaded: map.current.isStyleLoaded(),
          currentStyle: map.current.getStyle().name
        });

        layerLoaded.current = true;
        console.log('POI density layer setup complete');
      } catch (error) {
        console.error('Error loading POI density layer:', error);
        if (error.message) {
          console.error('Error details:', error.message);
        }
        if (error.stack) {
          console.error('Error stack:', error.stack);
        }
      }
    };

    const onMapLoad = () => {
      console.log('Map load event triggered');
      if (!layerLoaded.current) {
        loadPOIDensityLayer();
      } else {
        console.log('Updating existing POI density layer visibility:', visible);
        if (map.current.getLayer('poi-density-fill')) {
          map.current.setLayoutProperty('poi-density-fill', 'visibility', visible ? 'visible' : 'none');
        }
      }
    };

    // Check if map is already loaded
    if (map.current.loaded()) {
      console.log('Map already loaded, proceeding with layer setup');
      onMapLoad();
    } else {
      console.log('Waiting for map to load...');
      // Wait for map to load
      map.current.on('load', onMapLoad);
    }

    // Cleanup
    return () => {
      console.log('Cleaning up POI density layer');
      if (map.current) {
        map.current.off('load', onMapLoad);
        map.current.off('mousemove', 'poi-density-fill');
        map.current.off('mouseleave', 'poi-density-fill');
        
        if (map.current.getLayer('poi-density-fill')) {
          map.current.removeLayer('poi-density-fill');
        }
        // Don't remove the source as it might be used by other layers
      }
    };
  }, [map, visible]);

  return null;
};

export default POIDensityLayer; 