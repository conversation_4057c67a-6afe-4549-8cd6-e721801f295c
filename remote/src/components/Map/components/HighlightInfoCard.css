.highlight-info-card-popup {
  background: none;
  pointer-events: auto;
}

.highlight-info-card {
  background: #181c23;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.7);
  padding: 24px 28px 36px 28px;
  color: #fff;
  min-width: 286px;
  max-width: 374px;
  font-family: 'Inter', 'Segoe UI', Arial, sans-serif;
  display: flex;
  flex-direction: column;
  gap: 14px;
  position: relative;
  margin-bottom: 14px;
}

.highlight-info-title {
  font-size: 1.86rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 10px;
  letter-spacing: 0.01em;
  line-height: 0.5;
  margin-top: 20px;
  
}

.highlight-info-row {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  font-size: 0.79rem;
  margin-bottom: -4px;
}

.highlight-info-label {
  color: #bfc2c7;
  font-size: 0.71rem;
  font-weight: 400;
  letter-spacing: 0.01em;
  margin-right: 16px;
  flex: 1 1 60%;
  text-align: left;
}

.highlight-info-value {
  color: #ffb366;
  font-size: 0.82rem;
  font-weight: 600;
  flex: 1 1 40%;
  text-align: right;
}

.highlight-info-close {
  margin-top: 10px;
  background: #23262b;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 6px 18px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.18s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.18);
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.highlight-info-close:hover {
  background: #444;
}

.highlight-info-drag-handle {
  position: absolute;
  top: 10px;
  right: 14px;
  width: 24px;
  height: 24px;
  background: #23262b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  z-index: 10;
  box-shadow: 0 1px 4px rgba(0,0,0,0.18);
  transition: background 0.18s;
}

.highlight-info-drag-handle:hover {
  background: #444;
}

.highlight-info-drag-icon {
  width: 14px;
  height: 14px;
  display: block;
  fill: #bfc2c7;
  opacity: 0.8;
}

.highlight-info-skeleton {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}

.highlight-info-skeleton-row {
  height: 1.2em;
  width: 100%;
  border-radius: 6px;
  background: linear-gradient(90deg, #23262b 25%, #2c2f36 50%, #23262b 75%);
  background-size: 200% 100%;
  animation: highlight-skeleton-shimmer 1.2s infinite linear;
}

@keyframes highlight-skeleton-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
} 