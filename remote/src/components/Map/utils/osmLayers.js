// Layer IDs for OSM data
export const osmLayerIds = {
    publicTransit: {
        stops: 'osm-transit-stops',
        routes: 'osm-transit-routes'
    },
    bikeInfra: {
        lanes: 'osm-bike-lanes',
        paths: 'osm-bike-paths',
        parking: 'osm-bike-parking'
    },
    pedestrian: {
        paths: 'osm-pedestrian-paths',
        crossings: 'osm-pedestrian-crossings'
    }
};

// Austin bounds for data validation
const LA_BOUNDS = {
    north: 30.5,
    south: 30.0,
    east: -97.5,
    west: -98.0
};

// Layer styles
const styles = {
    publicTransit: {
        stops: {
            type: 'circle',
            paint: {
                'circle-radius': 2,
                'circle-color': '#9B59B6'
            }
        },
        routes: {
            type: 'line',
            paint: {
                'line-color': '#9B59B6',
                'line-width': 1,
                'line-opacity': 0.8
            }
        }
    },
    bikeInfra: {
        lanes: {
            type: 'line',
            paint: {
                'line-color': '#2ECC71',
                'line-width': 2,
                'line-dasharray': [2, 1]
            }
        },
        paths: {
            type: 'line',
            paint: {
                'line-color': '#27AE60',
                'line-width': 1.6
            }
        },
        parking: {
            type: 'symbol',
            layout: {
                'icon-image': 'bicycle-15',
                'icon-size': 1,
                'icon-allow-overlap': true
            },
            paint: {
                'icon-opacity': 0.8,
                'icon-color': '#2ECC71'
            }
        }
    },
    pedestrian: {
        paths: {
            type: 'line',
            paint: {
                'line-color': '#E67E22',
                'line-width': 0.7,
                'line-opacity': 0.7
            }
        },
        crossings: {
            type: 'circle',
            paint: {
                'circle-radius': 1.5,
                'circle-color': '#D35400',
                'circle-stroke-width': 0.5,
                'circle-stroke-color': '#ffffff'
            }
        }
    }
};

export const loadOSMData = async (map, city) => {
  try {
    // Load data files
    const [transportationData, bikeData, pedestrianData] = await Promise.all([
      fetch(`/data/osm/${city}_transportation.geojson`).then(r => r.json()),
      fetch(`/data/osm/${city}_bike_network.geojson`).then(r => r.json()),
      fetch(`/data/osm/${city}_pedestrian_network.geojson`).then(r => r.json())
    ]);

    // Add sources only if they don't exist
    if (!map.getSource('osm-transit')) {
      map.addSource('osm-transit', {
        type: 'geojson',
        data: transportationData
      });
    }

    if (!map.getSource('osm-bike')) {
      map.addSource('osm-bike', {
        type: 'geojson',
        data: bikeData
      });
    }

    if (!map.getSource('osm-pedestrian')) {
      map.addSource('osm-pedestrian', {
        type: 'geojson',
        data: pedestrianData
      });
    }
  } catch (error) {}
};

// Helper function to calculate bounding box of features
function getBoundingBox(features) {
    let bounds = {
        north: -90,
        south: 90,
        east: -180,
        west: 180
    };
    
    features.forEach(feature => {
        if (!feature.geometry || !feature.geometry.coordinates) return;
        
        const coords = feature.geometry.coordinates;
        let points = [];
        
        if (feature.geometry.type === 'Point') {
            points = [coords];
        } else if (feature.geometry.type === 'LineString') {
            points = coords;
        }
        
        points.forEach(([lon, lat]) => {
            bounds.north = Math.max(bounds.north, lat);
            bounds.south = Math.min(bounds.south, lat);
            bounds.east = Math.max(bounds.east, lon);
            bounds.west = Math.min(bounds.west, lon);
        });
    });
    
    return bounds;
}

export function toggleOSMLayer(map, category, subcategory, visible, color = null) {
    const layerId = osmLayerIds[category][subcategory];
    
    try {
        if (!map.getLayer(layerId)) {
            loadOSMData(map).then(() => {
                if (map.getLayer(layerId)) {
                    updateLayerVisibility(map, layerId, visible, color);
                    
                    // If this is a bike layer and it's being made visible, move it to the top
                    if (visible && category === 'bikeInfra') {
                        moveBikeLayerToTop(map, layerId);
                    }
                }
            });
            return;
        }
        
        updateLayerVisibility(map, layerId, visible, color);
        
        // If this is a bike layer and it's being made visible, move it to the top
        if (visible && category === 'bikeInfra') {
            moveBikeLayerToTop(map, layerId);
        }
    } catch (error) {}
}

function moveBikeLayerToTop(map, layerId) {
    try {
        // Get all layers
        const style = map.getStyle();
        const layers = style.layers;
        
        // Find the topmost non-bike OSM layer to place bike layers above
        // We need to identify what should be the reference layer
        
        // First, try to find a good reference layer
        const symbolLayers = layers.filter(layer => layer.type === 'symbol');
        if (symbolLayers.length > 0) {
            // Use a symbol layer as reference, as they're typically rendered on top
            const referenceLayer = symbolLayers[symbolLayers.length - 1].id;
            map.moveLayer(layerId, referenceLayer);
        } else {
            // If no symbol layer is found, just move to the top
            map.moveLayer(layerId);
        }
    } catch (error) {}
}

function updateLayerVisibility(map, layerId, visible, color = null) {
    // Update visibility
    map.setLayoutProperty(layerId, 'visibility', visible ? 'visible' : 'none');
    
    // Update color if provided
    if (color) {
        const layer = map.getLayer(layerId);
        if (layer.type === 'line') {
            map.setPaintProperty(layerId, 'line-color', color);
        } else if (layer.type === 'circle') {
            map.setPaintProperty(layerId, 'circle-color', color);
        } else if (layer.type === 'symbol') {
            map.setPaintProperty(layerId, 'icon-color', color);
        }
    }
} 