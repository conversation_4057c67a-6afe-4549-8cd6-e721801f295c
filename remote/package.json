{"name": "remote", "version": "1.0.0", "private": true, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@gi-nx/iframe-sdk": "^0.0.11", "@gi-nx/iframe-sdk-react": "^0.0.3", "@turf/turf": "^7.2.0", "axios": "^1.7.9", "cors": "^2.8.5", "cors-anywhere": "^0.4.4", "dotenv": "^16.4.7", "express": "^4.21.2", "framer-motion": "^11.12.0", "lucide-react": "^0.462.0", "openai": "^4.87.3", "pdf-parse": "^1.1.1", "proj4": "^2.17.0", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^5.3.0", "react-scripts": "^5.0.1", "recharts": "^2.15.1", "styled-components": "^6.1.15", "typewriter-effect": "^2.21.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "main": "tailwind.config.js", "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"concurrently": "^9.1.2"}}