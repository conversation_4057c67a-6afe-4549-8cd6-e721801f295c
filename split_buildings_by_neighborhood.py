import geopandas as gpd
import os
from pathlib import Path
import json

def split_buildings_by_neighborhood():
    # Paths
    buildings_path = Path('public/data/osm/saoPao/sp_buildings_3d_cleaned.geojson')
    neighborhoods_path = Path('remote/public/data/osm/saoPao/distrito_wgs84.geojson')
    output_dir = Path('remote/public/data/osm/saoPao')
    
    print("Loading data...")
    
    # Load neighborhoods
    neighborhoods = gpd.read_file(neighborhoods_path)
    print(f"Loaded {len(neighborhoods)} neighborhoods")
    print("Neighborhood columns:", neighborhoods.columns.tolist())
    
    # Load buildings
    buildings = gpd.read_file(buildings_path)
    print(f"Loaded {len(buildings)} buildings")
    
    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Process each neighborhood
    for idx, neighborhood in neighborhoods.iterrows():
        # Use 'ds_nome' as the neighborhood name column
        neighborhood_name = neighborhood['ds_nome']
        print(f"\nProcessing neighborhood: {neighborhood_name}")
        
        # Get buildings within this neighborhood
        buildings_in_neighborhood = buildings[buildings.geometry.within(neighborhood.geometry)]
        
        if len(buildings_in_neighborhood) > 0:
            # Save to GeoJSON
            output_file = output_dir / f"{neighborhood_name}.geojson"
            buildings_in_neighborhood.to_file(output_file, driver='GeoJSON')
            print(f"Saved {len(buildings_in_neighborhood)} buildings to {output_file}")
        else:
            print(f"No buildings found in {neighborhood_name}")
    
    print("\nSplitting complete!")
    print(f"Output directory: {output_dir.absolute()}")

if __name__ == "__main__":
    split_buildings_by_neighborhood() 