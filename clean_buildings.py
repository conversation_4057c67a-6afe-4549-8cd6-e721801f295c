import json
import os
import numpy as np
from datetime import datetime

def clean_buildings():
    # Path to the buildings file
    input_filepath = os.path.join('public', 'data', 'osm', 'saoPao', 'sp_buildings_3d.geojson')
    output_filepath = os.path.join('public', 'data', 'osm', 'saoPao', 'sp_buildings_3d_cleaned.geojson')
    
    print("Cleaning São Paulo building data...")
    
    # Define reasonable limits
    HEIGHT_MIN = 1.0  # Minimum height in meters
    HEIGHT_MAX = 300.0  # Maximum height in meters (taller than any building in São Paulo)
    LEVELS_MIN = 1  # Minimum number of levels
    LEVELS_MAX = 50  # Maximum number of levels
    
    # Initialize counters
    total_buildings = 0
    height_outliers = 0
    level_outliers = 0
    invalid_geometry = 0
    heights_before = []
    levels_before = []
    cleaned_features = []
    
    # Process the file in chunks
    chunk_size = 10000  # Process 10,000 buildings at a time
    current_chunk = []
    
    print("\nReading and cleaning data in chunks...")
    
    # First pass: collect statistics and clean data
    with open(input_filepath, 'r') as f:
        # Read the entire file into memory (since it's a single JSON object)
        data = json.load(f)
        
        if not isinstance(data, dict) or data.get('type') != 'FeatureCollection':
            raise ValueError("Input file is not a valid GeoJSON FeatureCollection")
        
        features = data.get('features', [])
        total_buildings = len(features)
        print(f"Found {total_buildings:,} buildings to process")
        
        for i, feature in enumerate(features):
            if (i + 1) % 100000 == 0:
                print(f"Processed {i + 1:,} buildings...")
            
            # Collect height and level statistics
            if 'height' in feature.get('properties', {}):
                heights_before.append(feature['properties']['height'])
            if 'levels' in feature.get('properties', {}):
                try:
                    levels_before.append(float(feature['properties']['levels']))
                except (ValueError, TypeError):
                    pass
            
            # Clean height
            if feature['properties'].get('height'):
                height = feature['properties']['height']
                if not (HEIGHT_MIN <= height <= HEIGHT_MAX):
                    feature['properties']['height'] = None
                    height_outliers += 1
            
            # Clean levels
            if feature['properties'].get('levels'):
                try:
                    levels = float(feature['properties']['levels'])
                    if not (LEVELS_MIN <= levels <= LEVELS_MAX):
                        feature['properties']['levels'] = None
                        level_outliers += 1
                except (ValueError, TypeError):
                    feature['properties']['levels'] = None
                    level_outliers += 1
            
            # Validate geometry
            if feature['geometry']['type'] == 'Polygon':
                coords = feature['geometry']['coordinates'][0]
                if len(coords) >= 3:  # Valid polygon must have at least 3 points
                    current_chunk.append(feature)
                else:
                    invalid_geometry += 1
            else:
                invalid_geometry += 1
            
            # Write chunk if it's full
            if len(current_chunk) >= chunk_size:
                cleaned_features.extend(current_chunk)
                current_chunk = []
    
    # Add any remaining features
    if current_chunk:
        cleaned_features.extend(current_chunk)
    
    print("\nBefore cleaning:")
    # Filter out None values for statistics
    heights_before_valid = [h for h in heights_before if h is not None]
    levels_before_valid = [l for l in levels_before if l is not None]
    if heights_before_valid:
        print(f"Height range: {min(heights_before_valid):.1f} to {max(heights_before_valid):.1f} meters")
    if levels_before_valid:
        print(f"Levels range: {min(levels_before_valid):.1f} to {max(levels_before_valid):.1f}")
    
    # Create cleaned dataset
    cleaned_data = {
        'type': 'FeatureCollection',
        'features': cleaned_features
    }
    
    # Save cleaned data
    print("\nSaving cleaned data...")
    with open(output_filepath, 'w') as f:
        json.dump(cleaned_data, f)
    
    # Statistics after cleaning
    heights_after = []
    levels_after = []
    for feature in cleaned_features:
        if feature['properties'].get('height') is not None:
            heights_after.append(feature['properties']['height'])
        if feature['properties'].get('levels') is not None:
            try:
                levels_after.append(float(feature['properties']['levels']))
            except (ValueError, TypeError):
                pass
    heights_after_valid = [h for h in heights_after if h is not None]
    levels_after_valid = [l for l in levels_after if l is not None]
    if heights_after_valid:
        print(f"\nHeight statistics after cleaning:")
        print(f"- Buildings with height data: {len(heights_after_valid):,}")
        print(f"- Average height: {np.mean(heights_after_valid):.1f} meters")
        print(f"- Median height: {np.median(heights_after_valid):.1f} meters")
        print(f"- Min height: {min(heights_after_valid):.1f} meters")
        print(f"- Max height: {max(heights_after_valid):.1f} meters")
    if levels_after_valid:
        print(f"\nLevel statistics after cleaning:")
        print(f"- Buildings with level data: {len(levels_after_valid):,}")
        print(f"- Average levels: {np.mean(levels_after_valid):.1f}")
        print(f"- Median levels: {np.median(levels_after_valid):.1f}")
        print(f"- Min levels: {min(levels_after_valid):.1f}")
        print(f"- Max levels: {max(levels_after_valid):.1f}")
    
    print(f"\nCleaned data saved to: {os.path.abspath(output_filepath)}")

if __name__ == "__main__":
    clean_buildings() 